name: 'Deployment on Dev'
on:
  workflow_dispatch:
#  push:
#    branches:
#      - master

concurrency:
  group: dev-environment

jobs:
  backend-deploy-dev:
    name: 'Backend Deployment on Dev'
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          distribution: corretto
          java-version: 21

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v3

      - name: Grant execute permission for gradlew
        run: chmod +x gradlew

      - name: Setup AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_KEY }}
          aws-region: eu-central-1

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Build Docker Image with Gradle
        run: ./gradlew jib -PuseArm64=true

  webportal-deploy-dev:
    name: 'Webportal Deployment on Dev'
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_KEY }}
          aws-region: eu-central-1

      - name: Install dependencies
        run: |
          cd app-webportal
          npm ci

      - name: Build WebPortal
        run: |
          cd app-webportal
          npx ng build --configuration production

      - name: Clear S3 bucket
        run: |
          cd app-webportal
          aws s3 rm s3://klosesoft-webportal-bucket2 --recursive

      - name: Upload to S3
        run: |
          cd app-webportal
          aws s3 sync dist/billing-solution/browser/ s3://klosesoft-webportal-bucket2

  demoshop-deploy-dev:
    name: 'Demoshop Deployment on Dev'
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_KEY }}
          aws-region: eu-central-1

      - name: Install dependencies
        run: |
          cd demo-shop
          npm ci

      - name: Build Demoshop
        run: |
          cd demo-shop
          npx ng build --configuration production

      - name: Clear S3 bucket
        run: |
          cd demo-shop
          aws s3 rm s3://klosesoft-demoshop-bucket2 --recursive

      - name: Upload to S3
        run: |
          cd demo-shop
          aws s3 sync dist/demoshop/browser/ s3://klosesoft-demoshop-bucket2

  documentation-deploy-dev:
    name: 'Documentation Deployment on Dev'
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_KEY }}
          aws-region: eu-central-1

      - name: Create documentation
        run: |
          mkdir documentation
          cd documentation
          npx @redocly/cli build-docs ../api/external-api.yaml --config ../api/.redocly.yaml --disableGoogleFont=true --output=index.html --title="Billing Solution API Documentation"

      - name: Clear S3 bucket
        run: |
          aws s3 rm s3://klosesoft-documentation-bucket2 --recursive

      - name: Upload to S3
        run: |
          aws s3 sync documentation/ s3://klosesoft-documentation-bucket2
