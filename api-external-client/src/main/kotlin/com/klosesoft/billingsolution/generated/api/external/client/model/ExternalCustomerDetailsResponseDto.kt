/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package com.klosesoft.billingsolution.generated.api.external.client.model

import com.klosesoft.billingsolution.generated.api.external.client.model.AddressDto
import com.klosesoft.billingsolution.generated.api.external.client.model.ExternalCustomerDetailsDataDto

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param customerData 
 * @param billingAddress 
 */


data class ExternalCustomerDetailsResponseDto (

    @get:JsonProperty("customerData")
    val customerData: ExternalCustomerDetailsDataDto,

    @get:JsonProperty("billingAddress")
    val billingAddress: AddressDto

) {


}

