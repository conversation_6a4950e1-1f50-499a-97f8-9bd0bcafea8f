/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package com.klosesoft.billingsolution.generated.api.external.client.model


import com.fasterxml.jackson.annotation.JsonProperty

/**
 * The status of the subscription
 *
 * Values: ACTIVE,PAUSED,CANCELLED,EXPIRED
 */

enum class SubscriptionStatusDto(val value: kotlin.String) {

    @JsonProperty(value = "ACTIVE")
    ACTIVE("ACTIVE"),

    @JsonProperty(value = "PAUSED")
    PAUSED("PAUSED"),

    @JsonProperty(value = "CANCELLED")
    CANCELLED("CANCELLED"),

    @JsonProperty(value = "EXPIRED")
    EXPIRED("EXPIRED");

    /**
     * Override [toString()] to avoid using the enum variable name as the value, and instead use
     * the actual value defined in the API spec file.
     *
     * This solves a problem when the variable name and its value are different, and ensures that
     * the client sends the correct enum values to the server always.
     */
    override fun toString(): kotlin.String = value

    companion object {
        /**
         * Converts the provided [data] to a [String] on success, null otherwise.
         */
        fun encode(data: kotlin.Any?): kotlin.String? = if (data is SubscriptionStatusDto) "$data" else null

        /**
         * Returns a valid [SubscriptionStatusDto] for [data], null otherwise.
         */
        fun decode(data: kotlin.Any?): SubscriptionStatusDto? = data?.let {
          val normalizedData = "$it".lowercase()
          values().firstOrNull { value ->
            it == value || normalizedData == "$value".lowercase()
          }
        }
    }
}

