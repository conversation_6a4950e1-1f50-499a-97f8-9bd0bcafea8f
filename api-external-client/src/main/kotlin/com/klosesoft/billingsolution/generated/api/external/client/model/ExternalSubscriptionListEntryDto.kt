/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package com.klosesoft.billingsolution.generated.api.external.client.model

import com.klosesoft.billingsolution.generated.api.external.client.model.CurrencyDto
import com.klosesoft.billingsolution.generated.api.external.client.model.SubscriptionFrequencyDto
import com.klosesoft.billingsolution.generated.api.external.client.model.SubscriptionStatusDto

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param key The unique key of the subscription
 * @param name The name of the subscription
 * @param customerKey The key of the customer who owns this subscription
 * @param status 
 * @param frequency 
 * @param amount The recurring amount for the subscription
 * @param currency 
 * @param nextBillingDate The next billing date for the subscription
 * @param createdAt When the subscription was created
 */


data class ExternalSubscriptionListEntryDto (

    /* The unique key of the subscription */
    @get:JsonProperty("key")
    val key: kotlin.String,

    /* The name of the subscription */
    @get:JsonProperty("name")
    val name: kotlin.String,

    /* The key of the customer who owns this subscription */
    @get:JsonProperty("customerKey")
    val customerKey: kotlin.String,

    @get:JsonProperty("status")
    val status: SubscriptionStatusDto,

    @get:JsonProperty("frequency")
    val frequency: SubscriptionFrequencyDto,

    /* The recurring amount for the subscription */
    @get:JsonProperty("amount")
    val amount: java.math.BigDecimal,

    @get:JsonProperty("currency")
    val currency: CurrencyDto,

    /* The next billing date for the subscription */
    @get:JsonProperty("nextBillingDate")
    val nextBillingDate: java.time.LocalDate,

    /* When the subscription was created */
    @get:JsonProperty("createdAt")
    val createdAt: java.time.OffsetDateTime? = null

) {


}

