/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package com.klosesoft.billingsolution.generated.api.external.client.model

import com.klosesoft.billingsolution.generated.api.external.client.model.BookingAccountTypeDto

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param accountNumber 
 * @param name 
 * @param key 
 * @param bookingAccountType 
 * @param description 
 */


data class ExternalBookingAccountsListEntryDto (

    @get:JsonProperty("accountNumber")
    val accountNumber: kotlin.Long,

    @get:JsonProperty("name")
    val name: kotlin.String,

    @get:JsonProperty("key")
    val key: kotlin.String,

    @get:JsonProperty("bookingAccountType")
    val bookingAccountType: BookingAccountTypeDto? = null,

    @get:JsonProperty("description")
    val description: kotlin.String? = null

) {


}

