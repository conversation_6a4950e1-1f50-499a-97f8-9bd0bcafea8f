package com.klosesoft.billingsolution.domain.logic.service

import com.klosesoft.billingsolution.persistence.model.entity.OrderTemplate
import kotlinx.coroutines.reactive.awaitSingle
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate
import org.springframework.stereotype.Service

@Service
class OrderTemplateService(private val r2dbcEntityTemplate: R2dbcEntityTemplate) {

    suspend fun createOrderTemplate(
        orderTemplate: OrderTemplate,
    ): OrderTemplate = r2dbcEntityTemplate.insert(orderTemplate).awaitSingle()

    suspend fun updateOrderTemplate(
        orderTemplate: OrderTemplate,
    ): OrderTemplate = r2dbcEntityTemplate.update(orderTemplate).awaitSingle()
}
