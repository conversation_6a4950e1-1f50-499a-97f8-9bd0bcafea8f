package com.klosesoft.billingsolution.domain.logic.service.common

import com.klosesoft.billingsolution.common.utils.CacheKeys
import com.klosesoft.billingsolution.persistence.model.entity.Tenant
import com.klosesoft.billingsolution.persistence.model.entity.TenantConfig
import kotlinx.coroutines.reactor.awaitSingle
import org.springframework.cache.annotation.CacheEvict
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate
import org.springframework.stereotype.Service

@Service
class TenantService(
    private val r2dbcEntityTemplate: R2dbcEntityTemplate,
) {
    @CacheEvict(
        cacheNames = [
            CacheKeys.TENANT_KEYS_BY_USER_ID,
            CacheKeys.TENANTS_BY_ID,
            CacheKeys.TENANTS_BY_KEY,
            CacheKeys.TENANT_IDS_BY_KEY,
        ],
        allEntries = true,
    )
    suspend fun createTenant(
        tenant: Tenant,
    ): Tenant {
        return r2dbcEntityTemplate.insert(tenant).awaitSingle()
    }

    @CacheEvict(
        cacheNames = [
            CacheKeys.TENANT_CONFIGS_BY_APPCLIENTID,
            CacheKeys.TENANT_CONFIGS_BY_TENANTID,
        ],
        allEntries = true,
    )
    suspend fun createTenantConfig(
        tenantConfig: TenantConfig,
    ) {
        r2dbcEntityTemplate.insert(tenantConfig).awaitSingle()
    }

    @CacheEvict(
        cacheNames = [
            CacheKeys.TENANT_CONFIGS_BY_APPCLIENTID,
            CacheKeys.TENANT_CONFIGS_BY_TENANTID,
        ],
        allEntries = true,
    )
    suspend fun updateTenantConfig(
        tenantConfig: TenantConfig,
    ): TenantConfig {
        return r2dbcEntityTemplate.update(tenantConfig).awaitSingle()
    }
}
