package com.klosesoft.billingsolution.domain.logic.service.billing

import com.klosesoft.billingsolution.common.utils.RsqlFieldConstants
import com.klosesoft.billingsolution.common.utils.RsqlUtil
import com.klosesoft.billingsolution.common.utils.TimeProvider
import com.klosesoft.billingsolution.common.utils.exception.EntityNotFoundException
import com.klosesoft.billingsolution.domain.logic.service.billing.mapping.TranslateService
import com.klosesoft.billingsolution.domain.logic.service.common.AddressService
import com.klosesoft.billingsolution.domain.logic.service.common.ColumnInformationService
import com.klosesoft.billingsolution.domain.model.ReactivePageImpl
import com.klosesoft.billingsolution.domain.model.valueobject.AddressType
import com.klosesoft.billingsolution.domain.model.valueobject.Aggregation
import com.klosesoft.billingsolution.domain.model.valueobject.DebitCreditIndicator
import com.klosesoft.billingsolution.domain.model.valueobject.DocumentItemMode
import com.klosesoft.billingsolution.domain.model.valueobject.DocumentType
import com.klosesoft.billingsolution.domain.model.valueobject.GeneralDocumentTranslationKey
import com.klosesoft.billingsolution.domain.model.valueobject.ItemCategory
import com.klosesoft.billingsolution.domain.model.valueobject.Money
import com.klosesoft.billingsolution.domain.model.valueobject.PaymentStatus
import com.klosesoft.billingsolution.domain.model.valueobject.PropertyKey
import com.klosesoft.billingsolution.domain.model.valueobject.SumAggregation
import com.klosesoft.billingsolution.domain.model.valueobject.TaxCode
import com.klosesoft.billingsolution.domain.model.valueobject.TaxInformation
import com.klosesoft.billingsolution.domain.model.valueobject.TaxType
import com.klosesoft.billingsolution.domain.model.valueobject.TranslationType
import com.klosesoft.billingsolution.persistence.model.entity.Document
import com.klosesoft.billingsolution.persistence.model.entity.DocumentItem
import com.klosesoft.billingsolution.persistence.model.entity.Order
import com.klosesoft.billingsolution.persistence.model.entity.OrderItem
import com.klosesoft.billingsolution.persistence.model.entity.Subscription
import com.klosesoft.billingsolution.persistence.model.entity.SubscriptionItem
import com.klosesoft.billingsolution.persistence.model.entity.SumAggregations
import com.klosesoft.billingsolution.persistence.service.AddressLoadService
import com.klosesoft.billingsolution.persistence.service.CustomerLoadService
import com.klosesoft.billingsolution.persistence.service.DocumentItemLoadService
import com.klosesoft.billingsolution.persistence.service.DocumentLoadService
import com.klosesoft.billingsolution.persistence.service.OrderItemLoadService
import com.klosesoft.billingsolution.persistence.service.OrderLoadService
import com.klosesoft.billingsolution.persistence.service.SubscriptionItemLoadService
import com.klosesoft.billingsolution.persistence.service.SubscriptionLoadService
import com.klosesoft.billingsolution.persistence.service.TenantLoadService
import kotlinx.coroutines.reactor.awaitSingle
import org.springframework.data.domain.Pageable
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.util.UUID

@Service
class DocumentNormalService(
    private val orderLoadService: OrderLoadService,
    private val subscriptionLoadService: SubscriptionLoadService,
    private val tenantLoadService: TenantLoadService,
    private val documentNumberService: DocumentNumberService,
    private val addressLoadService: AddressLoadService,
    private val addressService: AddressService,
    private val orderItemLoadService: OrderItemLoadService,
    private val subscriptionItemLoadService: SubscriptionItemLoadService,
    private val aggregationService: AggregationService,
    private val documentLoadService: DocumentLoadService,
    private val documentItemLoadService: DocumentItemLoadService,
    private val r2dbcEntityTemplate: R2dbcEntityTemplate,
    private val columnInformationService: ColumnInformationService,
    private val translateService: TranslateService,
    private val customerLoadService: CustomerLoadService,
) {
    internal suspend fun createDocumentFromOrder(
        createdBy: UUID,
        orderId: UUID,
        documentType: DocumentType,
        documentItemMode: DocumentItemMode,
    ): Document {
        val order = orderLoadService.findById(orderId)
        val customer = customerLoadService.findById(order.customerId)

        val customerProperties = HashMap<PropertyKey, String>()
        customerProperties.putAll(customer.properties)
        if (customer.vatId != null) {
            customerProperties[PropertyKey.VAT_ID] = customer.vatId!!
        }

        val tenantConfig = tenantLoadService.findTenantConfigByTenantId(order.tenantId)

        val documentDate = TimeProvider.nowDateInTimeZone(tenantConfig.getTimeZoneAsObject())

        val documentNumber = documentNumberService.fetchNextDocumentNumber(
            documentDate,
            order.tenantId,
            order.businessSegmentId,
            documentDate.year,
        )

        val billingAddress = addressLoadService.findById(order.billingAddressId)
            .copyAddressData(order.tenantId, documentNumber, createdBy).let { addressService.createAddress(it) }

        val shippingAddress = order.shippingAddressId?.let { addressLoadService.findById(it) }
            ?.copyAddressData(order.tenantId, documentNumber, createdBy)?.let { addressService.createAddress(it) }

        val document = Document(
            customerId = order.customerId,
            orderId = order.id,
            orderKey = order.key,
            documentType = documentType,
            currency = order.currency,
            documentDate = documentDate,
            paymentStatus = PaymentStatus.UNPAID,
            orderProperties = order.properties,
            customerProperties = customerProperties,
            createdBy = createdBy,
            lastModifiedBy = createdBy,
            tenantId = order.tenantId,
            key = documentNumber,
            billingAddressId = billingAddress.id,
            shippingAddressId = shippingAddress?.id,
            businessSegmentId = order.businessSegmentId,
        )

        val taxCodes = mutableSetOf<TaxCode>()

        val orderItems = orderItemLoadService.findAllByTenantIdAndOrderId(order.tenantId, order.id, null, Pageable.unpaged())

        createDocumentItems(document, orderItems, documentItemMode, taxCodes, order)

        if (documentType == DocumentType.DEPOSIT) {
            document.sumAggregations = createDepositInvoiceSumAggregations(document, order, taxCodes)
        } else {
            document.sumAggregations = createInvoiceSumAggregations(document, order, taxCodes)
        }

        columnInformationService.updateColumnInformation(document.tenantId, document.key, "document")

        return r2dbcEntityTemplate.insert(document).awaitSingle()
    }

    internal suspend fun createDocumentFromSubscription(
        createdBy: UUID,
        subscriptionId: UUID,
        documentType: DocumentType,
        documentItemMode: DocumentItemMode,
    ): Document {
        val subscription = subscriptionLoadService.findById(subscriptionId)
        val customer = customerLoadService.findById(subscription.customerId)

        val customerProperties = HashMap<PropertyKey, String>()
        customerProperties.putAll(customer.properties)
        if (customer.vatId != null) {
            customerProperties[PropertyKey.VAT_ID] = customer.vatId!!
        }

        val tenantConfig = tenantLoadService.findTenantConfigByTenantId(subscription.tenantId)

        val documentDate = TimeProvider.nowDateInTimeZone(tenantConfig.getTimeZoneAsObject())

        val documentNumber = documentNumberService.fetchNextDocumentNumber(
            documentDate,
            subscription.tenantId,
            subscription.businessSegmentId,
            documentDate.year,
        )

        val customerBillingAddress = addressLoadService.findLatestCustomerAddress(
            subscription.tenantId,
            customer.id,
            AddressType.BILLING,
        )
        val customerShippingAddress = try {
            addressLoadService.findLatestCustomerAddress(
                subscription.tenantId,
                customer.id,
                AddressType.SHIPPING,
            )
        } catch (e: Exception) {
            null
        }

        // Load the actual Address entities and create copies (following the same pattern as order document creation)
        val billingAddress = addressLoadService.findById(customerBillingAddress.addressId)
            .copyAddressData(subscription.tenantId, documentNumber, createdBy)
            .let { addressService.createAddress(it) }

        val shippingAddress = customerShippingAddress?.let {
            addressLoadService.findById(it.addressId)
                .copyAddressData(subscription.tenantId, documentNumber, createdBy)
                .let { addressService.createAddress(it) }
        }

        val document = Document(
            customerId = subscription.customerId,
            subscriptionId = subscription.id,
            subscriptionKey = subscription.key,
            documentType = documentType,
            currency = subscription.currency,
            documentDate = documentDate,
            paymentStatus = PaymentStatus.UNPAID,
            orderProperties = subscription.properties,
            customerProperties = customerProperties,
            createdBy = createdBy,
            lastModifiedBy = createdBy,
            tenantId = subscription.tenantId,
            key = documentNumber,
            billingAddressId = billingAddress.id,
            shippingAddressId = shippingAddress?.id,
            businessSegmentId = subscription.businessSegmentId,
        )

        val taxCodes = mutableSetOf<TaxCode>()

        val subscriptionItems = subscriptionItemLoadService.findAllByTenantIdAndSubscriptionId(
            subscription.tenantId,
            subscription.id,
            null,
            Pageable.unpaged(),
        )

        createDocumentItemsFromSubscription(document, subscriptionItems, documentItemMode, taxCodes, subscription)

        if (documentType == DocumentType.DEPOSIT) {
            document.sumAggregations = createDepositInvoiceSumAggregationsFromSubscription(document, subscription, taxCodes)
        } else {
            document.sumAggregations = createInvoiceSumAggregationsFromSubscription(document, subscription, taxCodes)
        }

        columnInformationService.updateColumnInformation(document.tenantId, document.key, "document")

        return r2dbcEntityTemplate.insert(document).awaitSingle()
    }

    internal suspend fun createReverseDocumentFromOrder(
        createdBy: UUID,
        orderId: UUID,
    ): Document {
        val relatedDocument = loadInvoice(orderId)
        val order = orderLoadService.findById(
            relatedDocument.orderId ?: throw IllegalStateException(
                "Document ${relatedDocument.key} does not reference an order but a subscription. " +
                    "Reversed documents for subscriptions are not yet supported.",
            ),
        )

        val customerProperties = HashMap<PropertyKey, String>()
        customerProperties.putAll(relatedDocument.customerProperties)

        val tenantConfig = tenantLoadService.findTenantConfigByTenantId(order.tenantId)

        val documentDate = TimeProvider.nowDateInTimeZone(tenantConfig.getTimeZoneAsObject())

        val documentNumber = documentNumberService.fetchNextDocumentNumber(
            documentDate,
            relatedDocument.tenantId,
            order.businessSegmentId,
            documentDate.year,
        )

        val billingAddress = addressLoadService.findById(relatedDocument.billingAddressId).copyAddressData(
            relatedDocument.tenantId,
            documentNumber,
            createdBy,
        )
            .let { addressService.createAddress(it) }

        val shippingAddress =
            relatedDocument.shippingAddressId?.let { addressLoadService.findById(it) }
                ?.copyAddressData(relatedDocument.tenantId, documentNumber, createdBy)
                ?.let { addressService.createAddress(it) }

        val filter = RsqlUtil.addToFilterWithAnd(null, "${RsqlFieldConstants.DOCUMENT_ID}==${relatedDocument.id}")

        val relatedDocumentItems = documentItemLoadService.findAllByTenantId(
            relatedDocument.tenantId,
            filter,
            Pageable.unpaged(),
        ).toList()

        val taxCodes = mutableSetOf<TaxCode>()

        relatedDocumentItems.forEach {
            taxCodes.addAll(it.taxes.map { taxInformation -> taxInformation.taxCode })
        }

        val document =
            Document(
                customerId = relatedDocument.customerId,
                orderId = relatedDocument.orderId,
                orderKey = relatedDocument.orderKey,
                documentType = DocumentType.REVERSED,
                currency = relatedDocument.currency,
                relatedDocumentId = relatedDocument.id,
                documentDate = documentDate,
                paymentStatus = PaymentStatus.UNPAID,
                createdBy = createdBy,
                lastModifiedBy = createdBy,
                tenantId = relatedDocument.tenantId,
                key = documentNumber,
                billingAddressId = billingAddress.id,
                shippingAddressId = shippingAddress?.id,
                businessSegmentId = order.businessSegmentId,
            )

        createCreditNoteItems(document, relatedDocumentItems)

        document.sumAggregations = if (relatedDocument.documentType == DocumentType.DEPOSIT) {
            createCreditNoteDepositSumAggregations(relatedDocument, order, taxCodes)
        } else {
            createCreditNoteSumAggregations(document, relatedDocument, taxCodes)
        }

        columnInformationService.updateColumnInformation(document.tenantId, document.key, "document")

        return r2dbcEntityTemplate.insert(document).awaitSingle()
    }

    private suspend fun loadInvoice(
        orderId: UUID,
    ): Document {
        val finalDocuments = documentLoadService.findOptByOrderIdAndDocumentType(orderId, DocumentType.FINAL)

        if (finalDocuments.isNotEmpty()) {
            return finalDocuments.first()
        }

        val depositDocuments = documentLoadService.findOptByOrderIdAndDocumentType(orderId, DocumentType.DEPOSIT)

        if (depositDocuments.isNotEmpty()) {
            return depositDocuments.first()
        } else {
            throw EntityNotFoundException("No invoice found for order $orderId")
        }
    }

    private suspend fun createDepositInvoiceSumAggregations(
        document: Document,
        order: Order,
        taxCodes: MutableSet<TaxCode>,
    ): SumAggregations {
        val sumAggregations = SumAggregations()
        val deposit = aggregationService.createTotalForDeposit(document, order)
        sumAggregations.put(Aggregation.TOTAL.name, deposit)

        taxCodes.forEach { taxCode ->
            sumAggregations.put(
                Aggregation.TOTAL_TAX_BY_TAXCODE_.name + taxCode,
                aggregationService.createTotalTaxByTaxCodeForDeposit(document, taxCode, order),
            )
        }
        sumAggregations.put(
            Aggregation.TOTAL_FINANCE_DUE.name,
            SumAggregation(deposit.grossSum, Money.zero(document.currency), deposit.grossSum),
        )

        sumAggregations.put(Aggregation.TOTAL_PAY_INS.name, aggregationService.createTotalPayIns(order))

        return sumAggregations
    }

    private suspend fun createInvoiceSumAggregations(
        document: Document,
        order: Order,
        taxCodes: MutableSet<TaxCode>,
    ): SumAggregations {
        val sumAggregations = SumAggregations()

        sumAggregations.put(
            Aggregation.TOTAL.name,
            aggregationService.createTotal(document),
        )

        taxCodes.forEach { taxCode ->
            sumAggregations.put(
                Aggregation.TOTAL_TAX_BY_TAXCODE_.name + taxCode,
                aggregationService.createTotalTaxByTaxCode(document, taxCode),
            )
        }
        sumAggregations.put(Aggregation.TOTAL_FINANCE_DUE.name, aggregationService.createFinanceDue(order))

        sumAggregations.put(Aggregation.TOTAL_PAY_INS.name, aggregationService.createTotalPayIns(order))

        return sumAggregations
    }

    private suspend fun createCreditNoteDepositSumAggregations(
        document: Document,
        order: Order,
        taxCodes: MutableSet<TaxCode>,
    ): SumAggregations {
        val sumAggregations = SumAggregations()
        val deposit = aggregationService.createTotalForDeposit(document, order).negate()
        sumAggregations.put(Aggregation.TOTAL.name, deposit)

        taxCodes.forEach { taxCode ->
            sumAggregations.put(
                Aggregation.TOTAL_TAX_BY_TAXCODE_.name + taxCode,
                aggregationService.createTotalTaxByTaxCodeForDeposit(document, taxCode, order).negate(),
            )
        }
        sumAggregations.put(
            Aggregation.TOTAL_FINANCE_DUE.name,
            SumAggregation(deposit.grossSum, Money.zero(document.currency), deposit.grossSum),
        )

        sumAggregations.put(Aggregation.TOTAL_PAY_INS.name, aggregationService.createTotalPayIns(order).negate())

        return sumAggregations
    }

    private suspend fun createCreditNoteSumAggregations(
        document: Document,
        relatedDocument: Document,
        taxCodes: MutableSet<TaxCode>,
    ): SumAggregations {
        val sumAggregations = SumAggregations()

        sumAggregations.put(
            Aggregation.TOTAL.name,
            aggregationService.createTotal(document),
        )

        taxCodes.forEach { taxCode ->
            sumAggregations.put(
                Aggregation.TOTAL_TAX_BY_TAXCODE_.name + taxCode,
                aggregationService.createTotalTaxByTaxCode(document, taxCode),
            )
        }

        sumAggregations.put(
            Aggregation.TOTAL_PAY_INS.name,
            relatedDocument.getSumAggregations(Aggregation.TOTAL_PAY_INS.name),
        )

        sumAggregations.put(
            Aggregation.TOTAL_FINANCE_DUE.name,
            relatedDocument.getSumAggregations(Aggregation.TOTAL_FINANCE_DUE.name),
        )

        return sumAggregations
    }

    private suspend fun createDocumentItems(
        document: Document,
        lineItems: ReactivePageImpl<OrderItem>,
        documentItemMode: DocumentItemMode,
        taxCodes: MutableSet<TaxCode>,
        order: Order,
    ) {
        var maxTaxInformation = TaxInformation(
            taxType = TaxType.VAT,
            taxCode = TaxCode.EMPTY,
            taxRate = BigDecimal.ZERO,
            totalAmount = BigDecimal.ZERO,
            currency = document.currency,
        )
        lineItems.toList().filter {
            when (documentItemMode) {
                DocumentItemMode.CREDIT_ONLY ->
                    it.debitCreditIndicator == DebitCreditIndicator.CREDIT

                DocumentItemMode.DEBIT_ONLY ->
                    it.debitCreditIndicator == DebitCreditIndicator.DEBIT

                else -> true
            }
        }.map { item ->
            taxCodes.addAll(item.taxes.map { it.taxCode })
            if ((item.taxes.maxOfOrNull { it.taxRate } ?: BigDecimal.ZERO) > maxTaxInformation.taxRate) {
                maxTaxInformation = item.taxes.maxBy { it.taxRate }
            }

            val documentItem = DocumentItem(
                documentId = document.id,
                debitCreditIndicator = item.debitCreditIndicator,
                itemGroup = item.itemGroup,
                articleNumber = item.articleNumber,
                name = item.name,
                category = item.category,
                description = item.description,
                taxes = item.taxes,
                quantity = item.quantity,
                currency = document.currency,
                unitNetAmount = item.unitNetAmount,
                totalNetAmount = item.totalNetAmount,
                properties = item.properties,
                tenantId = document.tenantId,
                createdBy = document.createdBy,
                lastModifiedBy = document.lastModifiedBy,
                key = document.key + "_" + item.key,
            )

            r2dbcEntityTemplate.insert(documentItem).awaitSingle()
        }

        if (document.documentType == DocumentType.FINAL && order.agreedDepositAmount != null) {
            val depositDocument = documentLoadService.findAllByOrderIdAndDocumentType(order.id, DocumentType.DEPOSIT).first()
            val depositSumAggregation = depositDocument.getSumAggregations(Aggregation.TOTAL.name)
            val customer = customerLoadService.findById(document.customerId)

            val language = translateService.determineLanguage(
                document.tenantId,
                customer.language,
                TranslationType.DOCUMENT,
            )

            val depositItem = DocumentItem(
                documentId = document.id,
                debitCreditIndicator = DebitCreditIndicator.CREDIT,
                itemGroup = null,
                articleNumber = "deposit",
                name = translateService.translate(
                    GeneralDocumentTranslationKey.DEPOSIT.name,
                    language,
                    document.tenantId,
                    TranslationType.DOCUMENT,
                ),
                category = ItemCategory.DEPOSIT,
                description = null,
                taxes = listOf(maxTaxInformation.copy(totalAmount = depositSumAggregation.taxSum.amount)),
                quantity = BigDecimal.ONE,
                currency = document.currency,
                unitNetAmount = depositSumAggregation.netSum.amount,
                totalNetAmount = depositSumAggregation.netSum.amount,
                properties = mutableMapOf(),
                tenantId = document.tenantId,
                createdBy = document.createdBy,
                lastModifiedBy = document.lastModifiedBy,
                key = document.key + "_deposit",
            )

            r2dbcEntityTemplate.insert(depositItem).awaitSingle()
        }
    }

    private suspend fun createCreditNoteItems(
        document: Document,
        lineItems: List<DocumentItem>,
    ) {
        lineItems.filter { it.category != ItemCategory.DEPOSIT }.map { createCreditNoteItem(it, document) }
    }

    private suspend fun createCreditNoteItem(
        it: DocumentItem,
        document: Document,
    ): DocumentItem? {
        val taxInfos = it.taxes.map { taxInformation ->
            TaxInformation(
                taxType = taxInformation.taxType,
                taxCode = taxInformation.taxCode,
                taxRate = taxInformation.taxRate,
                totalAmount = taxInformation.totalAmount,
                currency = taxInformation.currency,
            )
        }

        val documentItem = DocumentItem(
            debitCreditIndicator = it.debitCreditIndicator.negate(),
            itemGroup = it.itemGroup,
            articleNumber = it.articleNumber,
            name = it.name,
            category = it.category,
            description = it.description,
            documentId = document.id,
            taxes = taxInfos,
            quantity = it.quantity,
            currency = document.currency,
            unitNetAmount = it.unitNetAmount,
            totalNetAmount = it.totalNetAmount,
            properties = it.properties,
            key = document.key + "_" + it.key,
            tenantId = document.tenantId,
            createdBy = document.createdBy,
            lastModifiedBy = document.lastModifiedBy,
        )

        return r2dbcEntityTemplate.insert(documentItem).awaitSingle()
    }

    @Suppress("UnusedParameter")
    private suspend fun createDocumentItemsFromSubscription(
        document: Document,
        lineItems: ReactivePageImpl<SubscriptionItem>,
        documentItemMode: DocumentItemMode,
        taxCodes: MutableSet<TaxCode>,
        subscription: Subscription,
    ) {
        var maxTaxInformation = TaxInformation(
            taxType = TaxType.VAT,
            taxCode = TaxCode.EMPTY,
            taxRate = BigDecimal.ZERO,
            totalAmount = BigDecimal.ZERO,
            currency = document.currency,
        )

        lineItems.toList().filter { item ->
            when (documentItemMode) {
                DocumentItemMode.ALL -> true
                DocumentItemMode.DEBIT_ONLY -> item.debitCreditIndicator.isDebit
                DocumentItemMode.CREDIT_ONLY -> item.debitCreditIndicator.isCredit
            }
        }.forEach { item ->
            item.taxes.forEach { taxInformation ->
                taxCodes.add(taxInformation.taxCode)
                if (taxInformation.taxRate > maxTaxInformation.taxRate) {
                    maxTaxInformation = taxInformation
                }
            }

            val documentItem = DocumentItem(
                documentId = document.id,
                debitCreditIndicator = item.debitCreditIndicator,
                itemGroup = item.itemGroup,
                articleNumber = item.articleNumber,
                name = item.name,
                category = item.category,
                description = item.description,
                taxes = item.taxes,
                quantity = item.quantity,
                currency = document.currency,
                unitNetAmount = item.unitNetAmount,
                totalNetAmount = item.totalNetAmount,
                properties = item.properties,
                tenantId = document.tenantId,
                createdBy = document.createdBy,
                lastModifiedBy = document.lastModifiedBy,
                key = document.key + "_" + item.key,
            )

            r2dbcEntityTemplate.insert(documentItem).awaitSingle()
        }
    }

    @Suppress("UnusedParameter")
    private suspend fun createInvoiceSumAggregationsFromSubscription(
        document: Document,
        subscription: Subscription,
        taxCodes: Set<TaxCode>,
    ): SumAggregations {
        val sumAggregations = SumAggregations()

        sumAggregations.put(
            Aggregation.TOTAL.name,
            aggregationService.createTotal(document),
        )

        taxCodes.forEach { taxCode ->
            sumAggregations.put(
                Aggregation.TOTAL_TAX_BY_TAXCODE_.name + taxCode,
                aggregationService.createTotalTaxByTaxCode(document, taxCode),
            )
        }

        return sumAggregations
    }

    @Suppress("UnusedParameter")
    private suspend fun createDepositInvoiceSumAggregationsFromSubscription(
        document: Document,
        subscription: Subscription,
        taxCodes: Set<TaxCode>,
    ): SumAggregations {
        val sumAggregations = SumAggregations()

        sumAggregations.put(
            Aggregation.TOTAL.name,
            aggregationService.createTotal(document),
        )

        taxCodes.forEach { taxCode ->
            sumAggregations.put(
                Aggregation.TOTAL_TAX_BY_TAXCODE_.name + taxCode,
                aggregationService.createTotalTaxByTaxCode(document, taxCode),
            )
        }

        return sumAggregations
    }
}
