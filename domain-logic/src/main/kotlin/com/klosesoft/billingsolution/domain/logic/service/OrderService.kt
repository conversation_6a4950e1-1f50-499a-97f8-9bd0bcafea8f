package com.klosesoft.billingsolution.domain.logic.service

import com.klosesoft.billingsolution.domain.logic.service.common.ColumnInformationService
import com.klosesoft.billingsolution.domain.logic.service.countnotification.CountNotificationService
import com.klosesoft.billingsolution.domain.model.dto.OrderDomainDto
import com.klosesoft.billingsolution.domain.model.dto.OrderSupplierRequestDomainDto
import com.klosesoft.billingsolution.domain.model.dto.SupplyType
import com.klosesoft.billingsolution.domain.model.valueobject.AddressType
import com.klosesoft.billingsolution.domain.model.valueobject.OrderStatus
import com.klosesoft.billingsolution.domain.model.valueobject.ProcessingStatus
import com.klosesoft.billingsolution.persistence.model.entity.BusinessSegment
import com.klosesoft.billingsolution.persistence.model.entity.Customer
import com.klosesoft.billingsolution.persistence.model.entity.CustomerAddress
import com.klosesoft.billingsolution.persistence.model.entity.Order
import com.klosesoft.billingsolution.persistence.model.entity.OrderHistoryEntry
import com.klosesoft.billingsolution.persistence.model.entity.OrderSupplier
import com.klosesoft.billingsolution.persistence.service.AddressLoadService
import com.klosesoft.billingsolution.persistence.service.BusinessSegmentLoadService
import com.klosesoft.billingsolution.persistence.service.CustomerLoadService
import com.klosesoft.billingsolution.persistence.service.OrderHistoryEntryLoadService
import com.klosesoft.billingsolution.persistence.service.OrderLoadService
import com.klosesoft.billingsolution.persistence.service.PersistenceFieldConstants
import com.klosesoft.billingsolution.persistence.service.SupplierLoadService
import kotlinx.coroutines.reactor.awaitSingle
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate
import org.springframework.data.relational.core.query.Criteria
import org.springframework.data.relational.core.query.Query
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.util.UUID

@Service
class OrderService(
    private val customerLoadService: CustomerLoadService,
    private val addressLoadService: AddressLoadService,
    private val businessSegmentLoadService: BusinessSegmentLoadService,
    private val r2dbcEntityTemplate: R2dbcEntityTemplate,
    private val orderLoadService: OrderLoadService,
    private val orderItemService: OrderItemService,
    private val orderHistoryService: OrderHistoryService,
    private val orderHistoryEntryLoadService: OrderHistoryEntryLoadService,
    private val supplierLoadService: SupplierLoadService,
    private val columnInformationService: ColumnInformationService,
    private val orderSumUpdateService: OrderSumUpdateService,
    private val countNotificationService: CountNotificationService,
    private val taxService: TaxService,
) {
    suspend fun createAndSaveOrder(
        tenantId: UUID,
        createdBy: UUID,
        orderDomainDto: OrderDomainDto,
    ): Order {
        val businessSegment =
            businessSegmentLoadService.findBusinessSegment(tenantId, orderDomainDto.businessSegmentKey)

        val customer = customerLoadService.findByTenantIdAndKey(tenantId, orderDomainDto.customerKey)

        val order = createOrderModel(orderDomainDto, tenantId, businessSegment.id, customer, createdBy)

        orderDomainDto.items.forEach {
            orderItemService.addOrderItem(
                userId = createdBy,
                order = order,
                orderItemRequestDomainDto = it,
                withoutUpdateAmounts = true,
            )
        }

        orderSumUpdateService.updateTotalAmounts(order, createdBy)

        createOrderSuppliers(tenantId, createdBy, orderDomainDto)

        return order
    }

    private suspend fun toOrderSupplier(
        order: Order,
        createdBy: UUID,
        orderSupplierDomainDto: OrderSupplierRequestDomainDto,
    ): OrderSupplier = when (orderSupplierDomainDto.supplyType) {
        SupplyType.SELFBILLING -> toSelfbillingSupplier(order, orderSupplierDomainDto, createdBy)
        SupplyType.COMMISSION -> toCommissionSupplier(order, orderSupplierDomainDto, createdBy)
    }

    private suspend fun createOrderSupplier(
        orderSupplier: OrderSupplier,
    ) {
        r2dbcEntityTemplate.insert(orderSupplier).awaitSingle()
    }

    private suspend fun toSelfbillingSupplier(
        order: Order,
        supplierContribution: OrderSupplierRequestDomainDto,
        createdBy: UUID,
    ): OrderSupplier {
        val supplier = supplierLoadService.findByTenantIdAndKey(order.tenantId, supplierContribution.supplierKey)

        return OrderSupplier(
            tenantId = order.tenantId,
            orderId = order.id,
            supplierId = supplier.id,
            key = supplier.key,
            supplyType = SupplyType.SELFBILLING,
            createdBy = createdBy,
            lastModifiedBy = createdBy,
            properties = supplierContribution.properties,
        )
    }

    private suspend fun toCommissionSupplier(
        order: Order,
        commissionSupplier: OrderSupplierRequestDomainDto,
        createdBy: UUID,
    ): OrderSupplier {
        val currency = commissionSupplier.commissionCurrency!!
        val commissionNetAmount = commissionSupplier.commissionRate?.let {
            RoundingUtil.round(order.totalNetAmount * it, currency)
        } ?: commissionSupplier.commissionAmount!!

        val supplier = supplierLoadService.findByTenantIdAndKey(order.tenantId, commissionSupplier.supplierKey)

        return OrderSupplier(
            tenantId = order.tenantId,
            orderId = order.id,
            supplierId = supplier.id,
            key = supplier.key,
            supplyType = SupplyType.COMMISSION,

            commissionNetRate = commissionSupplier.commissionRate,
            commissionNetAmount = commissionNetAmount,
            commissionCurrency = currency,
            taxes = listOf(taxService.toTaxInformation(commissionSupplier.taxInformationDomainDto!!, commissionNetAmount, currency)),
            properties = commissionSupplier.properties,

            createdBy = createdBy,
            lastModifiedBy = createdBy,
        )
    }

    private suspend fun deleteOrderSuppliersByOrderId(
        orderId: UUID,
    ) {
        r2dbcEntityTemplate.delete(
            Query.query(Criteria.where(PersistenceFieldConstants.ORDER_ID).`is`(orderId)),
            OrderSupplier::class.java,
        ).awaitSingle()
    }

    private suspend fun createOrderModel(
        orderDomainDto: OrderDomainDto,
        tenantId: UUID,
        businessSegmentId: UUID,
        customer: Customer,
        createdById: UUID,
    ): Order {
        val order =
            Order(
                tenantId = tenantId,
                customerId = customer.id,
                createdBy = createdById,
                lastModifiedBy = createdById,
                currency = orderDomainDto.orderCurrency,
                description = orderDomainDto.description,
                orderDate = orderDomainDto.orderDate,
                processingStatus = ProcessingStatus.INIT,
                financingType = orderDomainDto.financingType,
                properties = orderDomainDto.properties,
                key = orderDomainDto.key,
                title = orderDomainDto.title,
                businessSegmentId = businessSegmentId,
                billingAddressId = fetchBillingAddress(tenantId, customer, orderDomainDto.billingAddressKey).addressId,
                shippingAddressId = orderDomainDto.shippingAddressKey?.let {
                    fetchShippingAddressByKey(tenantId, customer, it)
                }?.addressId,
                orderStatus = OrderStatus.INIT,
                totalNetAmount = BigDecimal.ZERO,
                totalTaxAmount = BigDecimal.ZERO,
                totalGrossAmount = BigDecimal.ZERO,
                paymentDueDate = orderDomainDto.paymentDueDate,
                deliveryDate = orderDomainDto.deliveryDate,
                agreedDepositAmount = orderDomainDto.agreedDepositAmount,
            )

        columnInformationService.updateColumnInformation(tenantId, orderDomainDto.key, "order")

        return r2dbcEntityTemplate.insert(order).awaitSingle()
    }

    private suspend fun fetchShippingAddressByKey(
        tenantId: UUID,
        customer: Customer,
        shippingAddressKey: String,
    ): CustomerAddress = addressLoadService.findActiveCustomerAddressByKeyAndType(
        tenantId,
        customer.id,
        shippingAddressKey,
        AddressType.SHIPPING,
    )

    private suspend fun fetchBillingAddress(
        tenantId: UUID,
        customer: Customer,
        billingAddressKey: String?,
    ): CustomerAddress = if (billingAddressKey != null) {
        fetchBillingAddressByKey(tenantId, customer, billingAddressKey)
    } else {
        fetchBillingAddressLatest(tenantId, customer)
    }

    private suspend fun fetchBillingAddressLatest(
        tenantId: UUID,
        customer: Customer,
    ): CustomerAddress = addressLoadService.findLatestCustomerAddress(tenantId, customer.id, AddressType.BILLING)

    private suspend fun fetchBillingAddressByKey(
        tenantId: UUID,
        customer: Customer,
        billingAddressKey: String,
    ): CustomerAddress = addressLoadService.findActiveCustomerAddressByKeyAndType(
        tenantId,
        customer.id,
        billingAddressKey,
        AddressType.BILLING,
    )

    suspend fun updateOrderForBalanceCaseInit(
        lastModifiedBy: UUID,
        order: Order,
    ): Order {
        val orderToUpdate = orderLoadService.findById(order.id)

        return r2dbcEntityTemplate.update(
            orderToUpdate.copy(
                lastModifiedBy = lastModifiedBy,
                balanceCaseId = order.balanceCaseId,
                version = order.version,
            ),
        ).awaitSingle()
    }

    suspend fun updateOrderStatus(
        lastModifiedBy: UUID,
        orderId: UUID,
        orderStatus: OrderStatus,
    ): Order {
        val orderToUpdate = orderLoadService.findById(orderId)

        return r2dbcEntityTemplate.update(
            orderToUpdate.copy(
                lastModifiedBy = lastModifiedBy,
                orderStatus = orderStatus,
            ),
        ).awaitSingle()
    }

    suspend fun updateOrderProcessingStatus(
        lastModifiedBy: UUID,
        orderId: UUID,
        processingStatus: ProcessingStatus,
        additionalInformation: String? = null,
    ) {
        val orderToUpdate = orderLoadService.findById(orderId)

        r2dbcEntityTemplate.update(orderToUpdate.copy(lastModifiedBy = lastModifiedBy, processingStatus = processingStatus))
            .awaitSingle()

        OrderHistoryEntry(
            number = (orderHistoryEntryLoadService.findMaxNumberByOrderId(orderId) + 1),
            orderId = orderId,
            processingStatus = processingStatus,
            additionalInformation = additionalInformation,
            tenantId = orderToUpdate.tenantId,
            createdBy = lastModifiedBy,
        ).let {
            orderHistoryService.createHistoryEntry(it)
        }

        countNotificationService.notifyUpdate()
    }

    suspend fun updateOrder(
        lastModifiedBy: UUID,
        originalOrder: Order,
        orderDomainDto: OrderDomainDto,
    ) {
        // delete original linked entities
        orderItemService.deleteOrderItemsByOrderId(originalOrder.id, withoutUpdateAmounts = true)
        deleteOrderSuppliersByOrderId(originalOrder.id)

        val businessSegment =
            businessSegmentLoadService.findBusinessSegment(originalOrder.tenantId, orderDomainDto.businessSegmentKey)

        val customer = customerLoadService.findByTenantIdAndKey(originalOrder.tenantId, orderDomainDto.customerKey)

        checkIfUpdateIsAllowed(orderDomainDto, originalOrder, customer, businessSegment)

        val updatedOrder = createOrderModelForFullUpdate(
            originalOrder = originalOrder,
            orderDomainDto = orderDomainDto,
            businessSegmentId = businessSegment.id,
            customer = customer,
            modifiedById = lastModifiedBy,
        )

        orderDomainDto.supplierContributions.map { toOrderSupplier(originalOrder, lastModifiedBy, it) }
            .forEach { createOrderSupplier(it) }

        orderDomainDto.items.forEach {
            orderItemService.addOrderItem(
                userId = lastModifiedBy,
                order = updatedOrder,
                orderItemRequestDomainDto = it,
                withoutUpdateAmounts = true,
            )
        }

        r2dbcEntityTemplate.update(
            updatedOrder,
        ).awaitSingle()

        orderSumUpdateService.updateTotalAmounts(updatedOrder, lastModifiedBy)
    }

    private suspend fun createOrderModelForFullUpdate(
        originalOrder: Order,
        orderDomainDto: OrderDomainDto,
        businessSegmentId: UUID,
        customer: Customer,
        modifiedById: UUID,
    ): Order = Order(
        tenantId = originalOrder.tenantId,
        customerId = originalOrder.customerId,
        createdBy = originalOrder.createdBy,
        lastModifiedBy = modifiedById,
        currency = orderDomainDto.orderCurrency,
        description = orderDomainDto.description,
        orderDate = orderDomainDto.orderDate,
        processingStatus = originalOrder.processingStatus,
        financingType = orderDomainDto.financingType,
        properties = orderDomainDto.properties,
        key = originalOrder.key,
        title = orderDomainDto.title,
        businessSegmentId = businessSegmentId,
        billingAddressId = fetchBillingAddress(originalOrder.tenantId, customer, orderDomainDto.billingAddressKey).addressId,
        shippingAddressId = orderDomainDto.shippingAddressKey?.let {
            fetchShippingAddressByKey(originalOrder.tenantId, customer, it)
        }?.addressId,
        orderStatus = originalOrder.orderStatus,
        totalNetAmount = originalOrder.totalNetAmount,
        totalTaxAmount = originalOrder.totalTaxAmount,
        totalGrossAmount = originalOrder.totalGrossAmount,
        paymentDueDate = orderDomainDto.paymentDueDate,
        deliveryDate = orderDomainDto.deliveryDate,
        agreedDepositAmount = orderDomainDto.agreedDepositAmount,
    )

    suspend fun updateOrderPartial(
        lastModifiedBy: UUID,
        originalOrder: Order,
        orderDomainDto: OrderDomainDto,
    ) {
        // delete original linked entities
        if (orderDomainDto.items.isNotEmpty()) {
            orderItemService.deleteOrderItemsByOrderId(originalOrder.id, withoutUpdateAmounts = true)
        }
        if (orderDomainDto.supplierKeys.isNotEmpty()) {
            deleteOrderSuppliersByOrderId(originalOrder.id)
        }

        val businessSegment =
            businessSegmentLoadService.findBusinessSegment(originalOrder.tenantId, orderDomainDto.businessSegmentKey)

        val customer = customerLoadService.findByTenantIdAndKey(originalOrder.tenantId, orderDomainDto.customerKey)
        val customerBillingAddress = fetchBillingAddressLatest(originalOrder.tenantId, customer)

        checkIfUpdateIsAllowed(orderDomainDto, originalOrder, customer, businessSegment)

        // customer can be changed - but one customer always contains !
        if (orderDomainDto.billingAddressKey != null) {
            if (orderDomainDto.billingAddressKey != customerBillingAddress.key) {
                throw IllegalStateException(
                    "order domain billing address key ${orderDomainDto.billingAddressKey} and customer billing " +
                        "address key ${customerBillingAddress.key} must be the same",
                )
            }
        }

        val updateOrder = createOrderModelForPartialUpdate(
            originalOrder = originalOrder,
            orderDomainDto = orderDomainDto,
            businessSegmentId = businessSegment.id,
            customer = customer,
            billingAddressId = customerBillingAddress.addressId,
            modifiedById = lastModifiedBy,
        )

        if (orderDomainDto.supplierContributions.isNotEmpty()) {
            orderDomainDto.supplierContributions.map { toOrderSupplier(originalOrder, lastModifiedBy, it) }
                .forEach { createOrderSupplier(it) }
        }

        if (orderDomainDto.items.isNotEmpty()) {
            orderDomainDto.items.forEach {
                orderItemService.addOrderItem(
                    userId = lastModifiedBy,
                    order = updateOrder,
                    orderItemRequestDomainDto = it,
                    withoutUpdateAmounts = true,
                )
            }
        }

        val updatedOrder = r2dbcEntityTemplate.update(updateOrder).awaitSingle()

        orderSumUpdateService.updateTotalAmounts(updatedOrder, lastModifiedBy)
    }

    private suspend fun createOrderSuppliers(
        tenantId: UUID,
        createdBy: UUID,
        orderDomainDto: OrderDomainDto,
    ) {
        // load updated order
        val order = orderLoadService.findByTenantIdAndKey(tenantId, orderDomainDto.key)

        orderDomainDto.supplierContributions.map { toOrderSupplier(order, createdBy, it) }.forEach { createOrderSupplier(it) }
    }

    private fun checkIfUpdateIsAllowed(
        orderDomainDto: OrderDomainDto,
        originalOrder: Order,
        customer: Customer,
        businessSegment: BusinessSegment,
    ) {
        if (orderDomainDto.key != originalOrder.key) {
            error("Order key cannot be changed")
        }

        if (originalOrder.orderStatus != OrderStatus.INIT) {
            if (customer.id != originalOrder.customerId) {
                error("Customer cannot be changed")
            }

            if (businessSegment.id != originalOrder.businessSegmentId) {
                error("Business segment cannot be changed")
            }

            if (originalOrder.agreedDepositAmount != orderDomainDto.agreedDepositAmount) {
                error("Agreed deposit amount cannot be changed")
            }
        }
    }

    private suspend fun createOrderModelForPartialUpdate(
        originalOrder: Order,
        orderDomainDto: OrderDomainDto,
        businessSegmentId: UUID,
        billingAddressId: UUID,
        customer: Customer,
        modifiedById: UUID,
    ): Order = Order(
        tenantId = originalOrder.tenantId,
        id = originalOrder.id,
        version = orderDomainDto.version,
        customerId = customer.id,
        createdBy = originalOrder.createdBy,
        lastModifiedBy = modifiedById,
        currency = orderDomainDto.orderCurrency,
        description = orderDomainDto.description ?: originalOrder.description,
        orderDate = orderDomainDto.orderDate,
        processingStatus = originalOrder.processingStatus,
        financingType = orderDomainDto.financingType,
        properties = orderDomainDto.properties.ifEmpty {
            originalOrder.properties
        },
        key = originalOrder.key,
        title = orderDomainDto.title ?: originalOrder.title,
        businessSegmentId = businessSegmentId,
        billingAddressId = billingAddressId,
        shippingAddressId = if (orderDomainDto.shippingAddressKey != null) {
            fetchShippingAddressByKey(originalOrder.tenantId, customer, orderDomainDto.shippingAddressKey!!).addressId
        } else {
            originalOrder.shippingAddressId
        },
        orderStatus = originalOrder.orderStatus,
        totalNetAmount = originalOrder.totalNetAmount,
        totalTaxAmount = originalOrder.totalTaxAmount,
        totalGrossAmount = originalOrder.totalGrossAmount,
        paymentDueDate = orderDomainDto.paymentDueDate,
        deliveryDate = orderDomainDto.deliveryDate,
        agreedDepositAmount = orderDomainDto.agreedDepositAmount ?: originalOrder.agreedDepositAmount,
    )
}
