package com.klosesoft.billingsolution.domain.logic.service.common

import com.klosesoft.billingsolution.domain.model.valueobject.DocumentType
import com.klosesoft.billingsolution.domain.model.valueobject.MailAttachment
import com.klosesoft.billingsolution.persistence.model.entity.Order
import com.klosesoft.billingsolution.persistence.model.entity.Subscription
import io.github.oshai.kotlinlogging.KotlinLogging
import jakarta.activation.DataHandler
import jakarta.mail.Message
import jakarta.mail.Session
import jakarta.mail.Transport
import jakarta.mail.internet.InternetAddress
import jakarta.mail.internet.MimeBodyPart
import jakarta.mail.internet.MimeMessage
import jakarta.mail.internet.MimeMultipart
import jakarta.mail.util.ByteArrayDataSource
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service

@Service
class MailService(private val sentArchiveService: SentArchiveService) {

    private val logger = KotlinLogging.logger {}

    @Value("\${mail.from}")
    private lateinit var fromAddress: String

    @Value("\${mail.username}")
    private lateinit var username: String

    @Value("\${mail.password}")
    private lateinit var password: String

    @Value("\${mail.smtp.host}")
    private lateinit var smtpHost: String

    @Value("\${mail.smtp.port}")
    private lateinit var smtpPort: String

    @Value("\${mail.smtp.localhost}")
    private lateinit var smtpLocalhost: String

    suspend fun sendInvoiceMail(
        to: String,
        subject: String,
        body: String,
        attachments: List<MailAttachment>,
        order: Order?,
        subscription: Subscription?,
        documentType: DocumentType,
    ) {
        val properties = System.getProperties()
        properties.setProperty("mail.smtp.host", smtpHost)
        properties.setProperty("mail.smtp.port", smtpPort)
        properties.setProperty("mail.smtp.localhost", smtpLocalhost)
        properties.setProperty("mail.smtp.auth", "true")
        if (smtpPort.contains("465")) {
            properties.setProperty("mail.smtp.ssl.enable", "true")
        }

        val session = Session.getInstance(properties)

        try {
            val message = MimeMessage(session)

            message.setFrom(InternetAddress(fromAddress))
            to.split(";").forEach { message.addRecipient(Message.RecipientType.TO, InternetAddress(it)) }
            message.subject = subject

            val multipart = MimeMultipart()

            val textPart = MimeBodyPart()
            textPart.setText(body)
            multipart.addBodyPart(textPart)

            for (attachment in attachments) {
                val attachmentPart = MimeBodyPart()
                val attachmentDataSource = ByteArrayDataSource(attachment.content, attachment.contentType)
                attachmentPart.dataHandler = DataHandler(attachmentDataSource)
                attachmentPart.fileName = attachment.filename
                multipart.addBodyPart(attachmentPart)
            }

            message.setContent(multipart)

            Transport.send(message, username, password)

            sentArchiveService.archiveSentDocumentMail(
                emailTo = to,
                emailHeader = subject,
                emailBody = body,
                attachments = attachments,
                order = order,
                subscription = subscription,
                documentType = documentType,
            )
        } catch (e: Exception) {
            logger.error { "Error sending email: ${e.message}" }
            throw e
        }
    }
}
