package com.klosesoft.billingsolution.domain.logic.service.common

import com.klosesoft.billingsolution.common.utils.BillingSolutionConstants
import com.klosesoft.billingsolution.common.utils.CacheKeys
import com.klosesoft.billingsolution.domain.model.valueobject.WorkflowType
import com.klosesoft.billingsolution.persistence.model.entity.WorkflowDefinition
import com.klosesoft.billingsolution.persistence.service.TenantLoadService
import com.klosesoft.billingsolution.persistence.service.WorkflowDefinitionLoadService
import kotlinx.coroutines.reactor.awaitSingle
import org.springframework.cache.annotation.CacheEvict
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate
import org.springframework.stereotype.Service
import java.util.UUID

@Service
class WorkflowDefinitionService(
    private val r2dbcEntityTemplate: R2dbcEntityTemplate,
    private val workflowDefinitionLoadService: WorkflowDefinitionLoadService,
    private val tenantLoadService: TenantLoadService,
) {

    suspend fun createWorkflowDefinition(
        tenantKey: String,
        workflowType: WorkflowType,
        name: String,
        description: String?,
        bpmnXml: String,
        deploymentId: String?,
        createdBy: UUID = BillingSolutionConstants.SYSTEM_USER_ID,
    ): WorkflowDefinition {
        val tenantId = tenantLoadService.fetchTenantId(tenantKey)
        val nextVersion = workflowDefinitionLoadService.getNextVersionNumber(tenantId, workflowType)
        
        // Deactivate previous active workflow
        val existingActiveWorkflow = workflowDefinitionLoadService.findActiveByTenantIdAndWorkflowType(tenantId, workflowType)
        if (existingActiveWorkflow != null) {
            updateWorkflowDefinition(
                existingActiveWorkflow.copy(
                    isActive = false,
                    lastModifiedBy = createdBy
                )
            )
        }

        val workflowDefinition = WorkflowDefinition(
            workflowType = workflowType,
            name = name,
            description = description,
            bpmnXml = bpmnXml,
            workflowVersion = nextVersion,
            isActive = true,
            deploymentId = deploymentId,
            processDefinitionKey = WorkflowDefinition.generateProcessDefinitionKey(tenantKey, workflowType),
            tenantId = tenantId,
            key = WorkflowDefinition.generateKey(tenantKey, workflowType, nextVersion),
            createdBy = createdBy,
            lastModifiedBy = createdBy,
        )

        return r2dbcEntityTemplate.insert(workflowDefinition).awaitSingle()
    }

    @CacheEvict(
        cacheNames = [CacheKeys.WORKFLOW_DEFINITIONS_BY_TENANT_AND_TYPE],
        allEntries = true,
    )
    suspend fun updateWorkflowDefinition(
        workflowDefinition: WorkflowDefinition,
    ): WorkflowDefinition {
        return r2dbcEntityTemplate.update(workflowDefinition).awaitSingle()
    }


}
