import { Injectable } from '@angular/core'

@Injectable({ providedIn: 'root' })
export class LocaleService {
  private locale = 'de-DE'

  getLocale(): string {
    return this.locale
  }

  setLocale(locale: string): void {
    this.locale = locale
  }

  getDecimalSeparator(): string {
    const numberWithDecimalSeparator = 1.1
    return Intl.NumberFormat(this.locale).format(numberWithDecimalSeparator).charAt(1)
  }
}
