/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { SendingStatus } from './sendingStatus';
import { TargetType } from './targetType';
import { SentArchiveType } from './sentArchiveType';


export interface WebPortalSentArchiveListEntry { 
    /**
     * The key of the sent archive entry
     */
    key: string;
    /**
     * The date and time of the sent archive entry
     */
    sentAt: string;
    type: SentArchiveType;
    /**
     * The key of the target
     */
    targetKey: string;
    targetType: TargetType;
    sendingStatus: SendingStatus;
}
export namespace WebPortalSentArchiveListEntry {
}


