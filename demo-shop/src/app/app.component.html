@if (sessionService.isLoggedIn()) {
  <mat-toolbar color="primary" style="padding-left: 0 !important">
    <a routerLink="/startpage">
      <img [src]="logoPath" alt="Brand Logo" style="max-width: 180px; max-height: 60px" />
    </a>
    <div class="toolbar-center">
      <a mat-icon-button routerLink="/">
        <mat-icon>home</mat-icon>
      </a>
    </div>
    <a mat-icon-button routerLink="/cart" style="margin-right: 30px">
      <mat-icon
        [matBadge]="this.cartService.getCartItemsCount()"
        [matBadgeHidden]="this.cartService.getCartItemsCount() == 0"
        matBadgeOverlap="false"
        matBadgeColor="accent"
        aria-hidden="false"
        >shopping_cart
      </mat-icon>
    </a>
    <a mat-button routerLink="/user-settings">
      <span class="user-icon">{{ username }}</span>
    </a>
    <button mat-icon-button (click)="logout()">
      <mat-icon>logout</mat-icon>
    </button>
  </mat-toolbar>

  <mat-sidenav-container class="sidenav-container" hasBackdrop="false" style="height: calc(100vh - 52px)">
    <mat-sidenav #sidenav mode="side" opened="false" class="sidenav" role="navigation"></mat-sidenav>
    <mat-sidenav-content>
      <router-outlet></router-outlet>
      <!--      <div class="footer">-->
      <!--        <span style="font-size: 12px; margin-left: 10px; height: 30px">Your Demo Shop &copy; 2024</span>-->
      <!--      </div>-->
    </mat-sidenav-content>
  </mat-sidenav-container>
} @else {
  <div class="flex-container">
    <mat-toolbar color="primary">
      <span>Demo Shop</span>
    </mat-toolbar>
    <div class="outer-div">
      <app-login></app-login>
    </div>
  </div>
}
