import { Routes } from '@angular/router';
import { Documentation } from './documentation/documentation';
import { Crud } from './crud/crud';
import { Empty } from './empty/empty';
import { AuthGuard } from '../guards/auth.guard';

export default [
    { path: 'documentation', component: Documentation, canActivate: [AuthGuard] },
    { path: 'crud', component: Crud, canActivate: [AuthGuard] },
    { path: 'empty', component: Empty, canActivate: [AuthGuard] },
    { path: '**', redirectTo: '/notfound' }
] as Routes;
