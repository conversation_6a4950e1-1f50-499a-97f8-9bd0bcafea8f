/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { WebPortalCustomerDetailsData } from './webPortalCustomerDetailsData';
import { WebPortalSubscriptionDetailsData } from './webPortalSubscriptionDetailsData';
import { WebPortalSubscriptionBalanceCase } from './webPortalSubscriptionBalanceCase';


export interface WebPortalSubscriptionDetails { 
    customerData: WebPortalCustomerDetailsData;
    subscriptionData: WebPortalSubscriptionDetailsData;
    balanceCaseData?: WebPortalSubscriptionBalanceCase;
}
export namespace WebPortalSubscriptionDetails {
}
