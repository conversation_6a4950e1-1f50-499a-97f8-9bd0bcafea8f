/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { SubscriptionStatus } from './subscriptionStatus';
import { ProcessingStatus } from './processingStatus';


export interface WebPortalSubscriptionStatusResponse { 
    /**
     * The key of the subscription
     */
    key: string;
    status: SubscriptionStatus;
    processingStatus: ProcessingStatus;
}
export namespace WebPortalSubscriptionStatusResponse {
}


