/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/* tslint:disable:no-unused-variable member-ordering */

import { Inject, Injectable, Optional }                      from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams,
         HttpResponse, HttpEvent, HttpParameterCodec, HttpContext 
        }       from '@angular/common/http';
import { CustomHttpParameterCodec }                          from '../encoder';
import { Observable }                                        from 'rxjs';

// @ts-ignore
import { CreateNoteRequest } from '../model/createNoteRequest';
// @ts-ignore
import { IsSubscriptionActivatable200Response } from '../model/isSubscriptionActivatable200Response';
// @ts-ignore
import { IsSubscriptionCancelable200Response } from '../model/isSubscriptionCancelable200Response';
// @ts-ignore
import { IsSubscriptionPausable200Response } from '../model/isSubscriptionPausable200Response';
// @ts-ignore
import { UpdateNoteRequest } from '../model/updateNoteRequest';
// @ts-ignore
import { WebPortalBalanceCasesPagedResponse } from '../model/webPortalBalanceCasesPagedResponse';
// @ts-ignore
import { WebPortalNotesListEntry } from '../model/webPortalNotesListEntry';
// @ts-ignore
import { WebPortalSubscriptionDetailsData } from '../model/webPortalSubscriptionDetailsData';
// @ts-ignore
import { WebPortalSubscriptionDetailsResponse } from '../model/webPortalSubscriptionDetailsResponse';
// @ts-ignore
import { WebPortalSubscriptionHistoryEntryPagedResponse } from '../model/webPortalSubscriptionHistoryEntryPagedResponse';
// @ts-ignore
import { WebPortalSubscriptionItemsPagedResponse } from '../model/webPortalSubscriptionItemsPagedResponse';
// @ts-ignore
import { WebPortalSubscriptionStatusResponse } from '../model/webPortalSubscriptionStatusResponse';
// @ts-ignore
import { WebPortalSubscriptionsPagedResponse } from '../model/webPortalSubscriptionsPagedResponse';

// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS }                     from '../variables';
import { Configuration }                                     from '../configuration';



@Injectable({
  providedIn: 'root'
})
export class WebPortalSubscriptionService {

    protected basePath = 'http://localhost:8080';
    public defaultHeaders = new HttpHeaders();
    public configuration = new Configuration();
    public encoder: HttpParameterCodec;

    constructor(protected httpClient: HttpClient, @Optional()@Inject(BASE_PATH) basePath: string|string[], @Optional() configuration: Configuration) {
        if (configuration) {
            this.configuration = configuration;
        }
        if (typeof this.configuration.basePath !== 'string') {
            const firstBasePath = Array.isArray(basePath) ? basePath[0] : undefined;
            if (firstBasePath != undefined) {
                basePath = firstBasePath;
            }

            if (typeof basePath !== 'string') {
                basePath = this.basePath;
            }
            this.configuration.basePath = basePath;
        }
        this.encoder = this.configuration.encoder || new CustomHttpParameterCodec();
    }


    // @ts-ignore
    private addToHttpParams(httpParams: HttpParams, value: any, key?: string): HttpParams {
        if (typeof value === "object" && value instanceof Date === false) {
            httpParams = this.addToHttpParamsRecursive(httpParams, value);
        } else {
            httpParams = this.addToHttpParamsRecursive(httpParams, value, key);
        }
        return httpParams;
    }

    private addToHttpParamsRecursive(httpParams: HttpParams, value?: any, key?: string): HttpParams {
        if (value == null) {
            return httpParams;
        }

        if (typeof value === "object") {
            if (Array.isArray(value)) {
                (value as any[]).forEach( elem => httpParams = this.addToHttpParamsRecursive(httpParams, elem, key));
            } else if (value instanceof Date) {
                if (key != null) {
                    httpParams = httpParams.append(key, (value as Date).toISOString().substring(0, 10));
                } else {
                   throw Error("key may not be null if value is Date");
                }
            } else {
                Object.keys(value).forEach( k => httpParams = this.addToHttpParamsRecursive(
                    httpParams, value[k], key != null ? `${key}.${k}` : k));
            }
        } else if (key != null) {
            httpParams = httpParams.append(key, value);
        } else {
            throw Error("key may not be null if value is not object or array");
        }
        return httpParams;
    }

    /**
     * Activate subscription by key
     * @param xTenantKey The key of the tenant
     * @param subscriptionKey 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public activateSubscription(xTenantKey: string, subscriptionKey: string, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<WebPortalSubscriptionStatusResponse>;
    public activateSubscription(xTenantKey: string, subscriptionKey: string, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<WebPortalSubscriptionStatusResponse>>;
    public activateSubscription(xTenantKey: string, subscriptionKey: string, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<WebPortalSubscriptionStatusResponse>>;
    public activateSubscription(xTenantKey: string, subscriptionKey: string, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (xTenantKey === null || xTenantKey === undefined) {
            throw new Error('Required parameter xTenantKey was null or undefined when calling activateSubscription.');
        }
        if (subscriptionKey === null || subscriptionKey === undefined) {
            throw new Error('Required parameter subscriptionKey was null or undefined when calling activateSubscription.');
        }

        let localVarHeaders = this.defaultHeaders;
        if (xTenantKey !== undefined && xTenantKey !== null) {
            localVarHeaders = localVarHeaders.set('x-tenant-key', String(xTenantKey));
        }

        let localVarHttpHeaderAcceptSelected: string | undefined = options && options.httpHeaderAccept;
        if (localVarHttpHeaderAcceptSelected === undefined) {
            // to determine the Accept header
            const httpHeaderAccepts: string[] = [
                'application/json'
            ];
            localVarHttpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        }
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        let localVarHttpContext: HttpContext | undefined = options && options.context;
        if (localVarHttpContext === undefined) {
            localVarHttpContext = new HttpContext();
        }

        let localVarTransferCache: boolean | undefined = options && options.transferCache;
        if (localVarTransferCache === undefined) {
            localVarTransferCache = true;
        }


        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/webportal/v1/subscriptions/${this.configuration.encodeParam({name: "subscriptionKey", value: subscriptionKey, in: "path", style: "simple", explode: false, dataType: "string", dataFormat: undefined})}/activate`;
        return this.httpClient.request<WebPortalSubscriptionStatusResponse>('post', `${this.configuration.basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                responseType: <any>responseType_,
                withCredentials: this.configuration.withCredentials,
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Add a note to a subscription
     * @param xTenantKey The key of the tenant
     * @param subscriptionKey 
     * @param createNoteRequest 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public addSubscriptionNote(xTenantKey: string, subscriptionKey: string, createNoteRequest: CreateNoteRequest, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<WebPortalNotesListEntry>;
    public addSubscriptionNote(xTenantKey: string, subscriptionKey: string, createNoteRequest: CreateNoteRequest, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<WebPortalNotesListEntry>>;
    public addSubscriptionNote(xTenantKey: string, subscriptionKey: string, createNoteRequest: CreateNoteRequest, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<WebPortalNotesListEntry>>;
    public addSubscriptionNote(xTenantKey: string, subscriptionKey: string, createNoteRequest: CreateNoteRequest, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (xTenantKey === null || xTenantKey === undefined) {
            throw new Error('Required parameter xTenantKey was null or undefined when calling addSubscriptionNote.');
        }
        if (subscriptionKey === null || subscriptionKey === undefined) {
            throw new Error('Required parameter subscriptionKey was null or undefined when calling addSubscriptionNote.');
        }
        if (createNoteRequest === null || createNoteRequest === undefined) {
            throw new Error('Required parameter createNoteRequest was null or undefined when calling addSubscriptionNote.');
        }

        let localVarHeaders = this.defaultHeaders;
        if (xTenantKey !== undefined && xTenantKey !== null) {
            localVarHeaders = localVarHeaders.set('x-tenant-key', String(xTenantKey));
        }

        let localVarHttpHeaderAcceptSelected: string | undefined = options && options.httpHeaderAccept;
        if (localVarHttpHeaderAcceptSelected === undefined) {
            // to determine the Accept header
            const httpHeaderAccepts: string[] = [
                'application/json'
            ];
            localVarHttpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        }
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        let localVarHttpContext: HttpContext | undefined = options && options.context;
        if (localVarHttpContext === undefined) {
            localVarHttpContext = new HttpContext();
        }

        let localVarTransferCache: boolean | undefined = options && options.transferCache;
        if (localVarTransferCache === undefined) {
            localVarTransferCache = true;
        }


        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/webportal/v1/subscriptions/${this.configuration.encodeParam({name: "subscriptionKey", value: subscriptionKey, in: "path", style: "simple", explode: false, dataType: "string", dataFormat: undefined})}/notes`;
        return this.httpClient.request<WebPortalNotesListEntry>('post', `${this.configuration.basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                body: createNoteRequest,
                responseType: <any>responseType_,
                withCredentials: this.configuration.withCredentials,
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Cancel subscription by key
     * @param xTenantKey The key of the tenant
     * @param subscriptionKey 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public cancelSubscription(xTenantKey: string, subscriptionKey: string, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<WebPortalSubscriptionStatusResponse>;
    public cancelSubscription(xTenantKey: string, subscriptionKey: string, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<WebPortalSubscriptionStatusResponse>>;
    public cancelSubscription(xTenantKey: string, subscriptionKey: string, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<WebPortalSubscriptionStatusResponse>>;
    public cancelSubscription(xTenantKey: string, subscriptionKey: string, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (xTenantKey === null || xTenantKey === undefined) {
            throw new Error('Required parameter xTenantKey was null or undefined when calling cancelSubscription.');
        }
        if (subscriptionKey === null || subscriptionKey === undefined) {
            throw new Error('Required parameter subscriptionKey was null or undefined when calling cancelSubscription.');
        }

        let localVarHeaders = this.defaultHeaders;
        if (xTenantKey !== undefined && xTenantKey !== null) {
            localVarHeaders = localVarHeaders.set('x-tenant-key', String(xTenantKey));
        }

        let localVarHttpHeaderAcceptSelected: string | undefined = options && options.httpHeaderAccept;
        if (localVarHttpHeaderAcceptSelected === undefined) {
            // to determine the Accept header
            const httpHeaderAccepts: string[] = [
                'application/json'
            ];
            localVarHttpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        }
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        let localVarHttpContext: HttpContext | undefined = options && options.context;
        if (localVarHttpContext === undefined) {
            localVarHttpContext = new HttpContext();
        }

        let localVarTransferCache: boolean | undefined = options && options.transferCache;
        if (localVarTransferCache === undefined) {
            localVarTransferCache = true;
        }


        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/webportal/v1/subscriptions/${this.configuration.encodeParam({name: "subscriptionKey", value: subscriptionKey, in: "path", style: "simple", explode: false, dataType: "string", dataFormat: undefined})}/cancel`;
        return this.httpClient.request<WebPortalSubscriptionStatusResponse>('post', `${this.configuration.basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                responseType: <any>responseType_,
                withCredentials: this.configuration.withCredentials,
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Create a new subscription
     * @param xTenantKey The key of the tenant
     * @param webPortalSubscriptionDetailsData 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public createSubscription(xTenantKey: string, webPortalSubscriptionDetailsData: WebPortalSubscriptionDetailsData, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<WebPortalSubscriptionDetailsResponse>;
    public createSubscription(xTenantKey: string, webPortalSubscriptionDetailsData: WebPortalSubscriptionDetailsData, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<WebPortalSubscriptionDetailsResponse>>;
    public createSubscription(xTenantKey: string, webPortalSubscriptionDetailsData: WebPortalSubscriptionDetailsData, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<WebPortalSubscriptionDetailsResponse>>;
    public createSubscription(xTenantKey: string, webPortalSubscriptionDetailsData: WebPortalSubscriptionDetailsData, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (xTenantKey === null || xTenantKey === undefined) {
            throw new Error('Required parameter xTenantKey was null or undefined when calling createSubscription.');
        }
        if (webPortalSubscriptionDetailsData === null || webPortalSubscriptionDetailsData === undefined) {
            throw new Error('Required parameter webPortalSubscriptionDetailsData was null or undefined when calling createSubscription.');
        }

        let localVarHeaders = this.defaultHeaders;
        if (xTenantKey !== undefined && xTenantKey !== null) {
            localVarHeaders = localVarHeaders.set('x-tenant-key', String(xTenantKey));
        }

        let localVarHttpHeaderAcceptSelected: string | undefined = options && options.httpHeaderAccept;
        if (localVarHttpHeaderAcceptSelected === undefined) {
            // to determine the Accept header
            const httpHeaderAccepts: string[] = [
                'application/json'
            ];
            localVarHttpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        }
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        let localVarHttpContext: HttpContext | undefined = options && options.context;
        if (localVarHttpContext === undefined) {
            localVarHttpContext = new HttpContext();
        }

        let localVarTransferCache: boolean | undefined = options && options.transferCache;
        if (localVarTransferCache === undefined) {
            localVarTransferCache = true;
        }


        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/webportal/v1/subscriptions`;
        return this.httpClient.request<WebPortalSubscriptionDetailsResponse>('post', `${this.configuration.basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                body: webPortalSubscriptionDetailsData,
                responseType: <any>responseType_,
                withCredentials: this.configuration.withCredentials,
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Delete a specific subscription
     * @param xTenantKey The key of the tenant
     * @param subscriptionKey 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public deleteSubscription(xTenantKey: string, subscriptionKey: string, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: undefined, context?: HttpContext, transferCache?: boolean}): Observable<any>;
    public deleteSubscription(xTenantKey: string, subscriptionKey: string, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: undefined, context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<any>>;
    public deleteSubscription(xTenantKey: string, subscriptionKey: string, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: undefined, context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<any>>;
    public deleteSubscription(xTenantKey: string, subscriptionKey: string, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: undefined, context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (xTenantKey === null || xTenantKey === undefined) {
            throw new Error('Required parameter xTenantKey was null or undefined when calling deleteSubscription.');
        }
        if (subscriptionKey === null || subscriptionKey === undefined) {
            throw new Error('Required parameter subscriptionKey was null or undefined when calling deleteSubscription.');
        }

        let localVarHeaders = this.defaultHeaders;
        if (xTenantKey !== undefined && xTenantKey !== null) {
            localVarHeaders = localVarHeaders.set('x-tenant-key', String(xTenantKey));
        }

        let localVarHttpHeaderAcceptSelected: string | undefined = options && options.httpHeaderAccept;
        if (localVarHttpHeaderAcceptSelected === undefined) {
            // to determine the Accept header
            const httpHeaderAccepts: string[] = [
            ];
            localVarHttpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        }
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        let localVarHttpContext: HttpContext | undefined = options && options.context;
        if (localVarHttpContext === undefined) {
            localVarHttpContext = new HttpContext();
        }

        let localVarTransferCache: boolean | undefined = options && options.transferCache;
        if (localVarTransferCache === undefined) {
            localVarTransferCache = true;
        }


        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/webportal/v1/subscriptions/${this.configuration.encodeParam({name: "subscriptionKey", value: subscriptionKey, in: "path", style: "simple", explode: false, dataType: "string", dataFormat: undefined})}`;
        return this.httpClient.request<any>('delete', `${this.configuration.basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                responseType: <any>responseType_,
                withCredentials: this.configuration.withCredentials,
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Delete a subscription note
     * @param xTenantKey The key of the tenant
     * @param subscriptionKey 
     * @param noteKey 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public deleteSubscriptionNote(xTenantKey: string, subscriptionKey: string, noteKey: string, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: undefined, context?: HttpContext, transferCache?: boolean}): Observable<any>;
    public deleteSubscriptionNote(xTenantKey: string, subscriptionKey: string, noteKey: string, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: undefined, context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<any>>;
    public deleteSubscriptionNote(xTenantKey: string, subscriptionKey: string, noteKey: string, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: undefined, context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<any>>;
    public deleteSubscriptionNote(xTenantKey: string, subscriptionKey: string, noteKey: string, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: undefined, context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (xTenantKey === null || xTenantKey === undefined) {
            throw new Error('Required parameter xTenantKey was null or undefined when calling deleteSubscriptionNote.');
        }
        if (subscriptionKey === null || subscriptionKey === undefined) {
            throw new Error('Required parameter subscriptionKey was null or undefined when calling deleteSubscriptionNote.');
        }
        if (noteKey === null || noteKey === undefined) {
            throw new Error('Required parameter noteKey was null or undefined when calling deleteSubscriptionNote.');
        }

        let localVarHeaders = this.defaultHeaders;
        if (xTenantKey !== undefined && xTenantKey !== null) {
            localVarHeaders = localVarHeaders.set('x-tenant-key', String(xTenantKey));
        }

        let localVarHttpHeaderAcceptSelected: string | undefined = options && options.httpHeaderAccept;
        if (localVarHttpHeaderAcceptSelected === undefined) {
            // to determine the Accept header
            const httpHeaderAccepts: string[] = [
            ];
            localVarHttpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        }
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        let localVarHttpContext: HttpContext | undefined = options && options.context;
        if (localVarHttpContext === undefined) {
            localVarHttpContext = new HttpContext();
        }

        let localVarTransferCache: boolean | undefined = options && options.transferCache;
        if (localVarTransferCache === undefined) {
            localVarTransferCache = true;
        }


        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/webportal/v1/subscriptions/${this.configuration.encodeParam({name: "subscriptionKey", value: subscriptionKey, in: "path", style: "simple", explode: false, dataType: "string", dataFormat: undefined})}/notes/${this.configuration.encodeParam({name: "noteKey", value: noteKey, in: "path", style: "simple", explode: false, dataType: "string", dataFormat: undefined})}`;
        return this.httpClient.request<any>('delete', `${this.configuration.basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                responseType: <any>responseType_,
                withCredentials: this.configuration.withCredentials,
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Check if a subscription is activatable
     * @param xTenantKey The key of the tenant
     * @param subscriptionKey 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public isSubscriptionActivatable(xTenantKey: string, subscriptionKey: string, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<IsSubscriptionActivatable200Response>;
    public isSubscriptionActivatable(xTenantKey: string, subscriptionKey: string, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<IsSubscriptionActivatable200Response>>;
    public isSubscriptionActivatable(xTenantKey: string, subscriptionKey: string, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<IsSubscriptionActivatable200Response>>;
    public isSubscriptionActivatable(xTenantKey: string, subscriptionKey: string, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (xTenantKey === null || xTenantKey === undefined) {
            throw new Error('Required parameter xTenantKey was null or undefined when calling isSubscriptionActivatable.');
        }
        if (subscriptionKey === null || subscriptionKey === undefined) {
            throw new Error('Required parameter subscriptionKey was null or undefined when calling isSubscriptionActivatable.');
        }

        let localVarHeaders = this.defaultHeaders;
        if (xTenantKey !== undefined && xTenantKey !== null) {
            localVarHeaders = localVarHeaders.set('x-tenant-key', String(xTenantKey));
        }

        let localVarHttpHeaderAcceptSelected: string | undefined = options && options.httpHeaderAccept;
        if (localVarHttpHeaderAcceptSelected === undefined) {
            // to determine the Accept header
            const httpHeaderAccepts: string[] = [
                'application/json'
            ];
            localVarHttpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        }
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        let localVarHttpContext: HttpContext | undefined = options && options.context;
        if (localVarHttpContext === undefined) {
            localVarHttpContext = new HttpContext();
        }

        let localVarTransferCache: boolean | undefined = options && options.transferCache;
        if (localVarTransferCache === undefined) {
            localVarTransferCache = true;
        }


        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/webportal/v1/subscriptions/${this.configuration.encodeParam({name: "subscriptionKey", value: subscriptionKey, in: "path", style: "simple", explode: false, dataType: "string", dataFormat: undefined})}/isActivatable`;
        return this.httpClient.request<IsSubscriptionActivatable200Response>('get', `${this.configuration.basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                responseType: <any>responseType_,
                withCredentials: this.configuration.withCredentials,
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Check if a subscription is cancelable
     * @param xTenantKey The key of the tenant
     * @param subscriptionKey 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public isSubscriptionCancelable(xTenantKey: string, subscriptionKey: string, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<IsSubscriptionCancelable200Response>;
    public isSubscriptionCancelable(xTenantKey: string, subscriptionKey: string, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<IsSubscriptionCancelable200Response>>;
    public isSubscriptionCancelable(xTenantKey: string, subscriptionKey: string, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<IsSubscriptionCancelable200Response>>;
    public isSubscriptionCancelable(xTenantKey: string, subscriptionKey: string, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (xTenantKey === null || xTenantKey === undefined) {
            throw new Error('Required parameter xTenantKey was null or undefined when calling isSubscriptionCancelable.');
        }
        if (subscriptionKey === null || subscriptionKey === undefined) {
            throw new Error('Required parameter subscriptionKey was null or undefined when calling isSubscriptionCancelable.');
        }

        let localVarHeaders = this.defaultHeaders;
        if (xTenantKey !== undefined && xTenantKey !== null) {
            localVarHeaders = localVarHeaders.set('x-tenant-key', String(xTenantKey));
        }

        let localVarHttpHeaderAcceptSelected: string | undefined = options && options.httpHeaderAccept;
        if (localVarHttpHeaderAcceptSelected === undefined) {
            // to determine the Accept header
            const httpHeaderAccepts: string[] = [
                'application/json'
            ];
            localVarHttpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        }
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        let localVarHttpContext: HttpContext | undefined = options && options.context;
        if (localVarHttpContext === undefined) {
            localVarHttpContext = new HttpContext();
        }

        let localVarTransferCache: boolean | undefined = options && options.transferCache;
        if (localVarTransferCache === undefined) {
            localVarTransferCache = true;
        }


        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/webportal/v1/subscriptions/${this.configuration.encodeParam({name: "subscriptionKey", value: subscriptionKey, in: "path", style: "simple", explode: false, dataType: "string", dataFormat: undefined})}/isCancelable`;
        return this.httpClient.request<IsSubscriptionCancelable200Response>('get', `${this.configuration.basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                responseType: <any>responseType_,
                withCredentials: this.configuration.withCredentials,
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Check if a subscription is pausable
     * @param xTenantKey The key of the tenant
     * @param subscriptionKey 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public isSubscriptionPausable(xTenantKey: string, subscriptionKey: string, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<IsSubscriptionPausable200Response>;
    public isSubscriptionPausable(xTenantKey: string, subscriptionKey: string, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<IsSubscriptionPausable200Response>>;
    public isSubscriptionPausable(xTenantKey: string, subscriptionKey: string, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<IsSubscriptionPausable200Response>>;
    public isSubscriptionPausable(xTenantKey: string, subscriptionKey: string, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (xTenantKey === null || xTenantKey === undefined) {
            throw new Error('Required parameter xTenantKey was null or undefined when calling isSubscriptionPausable.');
        }
        if (subscriptionKey === null || subscriptionKey === undefined) {
            throw new Error('Required parameter subscriptionKey was null or undefined when calling isSubscriptionPausable.');
        }

        let localVarHeaders = this.defaultHeaders;
        if (xTenantKey !== undefined && xTenantKey !== null) {
            localVarHeaders = localVarHeaders.set('x-tenant-key', String(xTenantKey));
        }

        let localVarHttpHeaderAcceptSelected: string | undefined = options && options.httpHeaderAccept;
        if (localVarHttpHeaderAcceptSelected === undefined) {
            // to determine the Accept header
            const httpHeaderAccepts: string[] = [
                'application/json'
            ];
            localVarHttpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        }
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        let localVarHttpContext: HttpContext | undefined = options && options.context;
        if (localVarHttpContext === undefined) {
            localVarHttpContext = new HttpContext();
        }

        let localVarTransferCache: boolean | undefined = options && options.transferCache;
        if (localVarTransferCache === undefined) {
            localVarTransferCache = true;
        }


        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/webportal/v1/subscriptions/${this.configuration.encodeParam({name: "subscriptionKey", value: subscriptionKey, in: "path", style: "simple", explode: false, dataType: "string", dataFormat: undefined})}/isPausable`;
        return this.httpClient.request<IsSubscriptionPausable200Response>('get', `${this.configuration.basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                responseType: <any>responseType_,
                withCredentials: this.configuration.withCredentials,
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Get a specific subscription
     * @param xTenantKey The key of the tenant
     * @param subscriptionKey 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public loadSubscription(xTenantKey: string, subscriptionKey: string, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<WebPortalSubscriptionDetailsResponse>;
    public loadSubscription(xTenantKey: string, subscriptionKey: string, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<WebPortalSubscriptionDetailsResponse>>;
    public loadSubscription(xTenantKey: string, subscriptionKey: string, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<WebPortalSubscriptionDetailsResponse>>;
    public loadSubscription(xTenantKey: string, subscriptionKey: string, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (xTenantKey === null || xTenantKey === undefined) {
            throw new Error('Required parameter xTenantKey was null or undefined when calling loadSubscription.');
        }
        if (subscriptionKey === null || subscriptionKey === undefined) {
            throw new Error('Required parameter subscriptionKey was null or undefined when calling loadSubscription.');
        }

        let localVarHeaders = this.defaultHeaders;
        if (xTenantKey !== undefined && xTenantKey !== null) {
            localVarHeaders = localVarHeaders.set('x-tenant-key', String(xTenantKey));
        }

        let localVarHttpHeaderAcceptSelected: string | undefined = options && options.httpHeaderAccept;
        if (localVarHttpHeaderAcceptSelected === undefined) {
            // to determine the Accept header
            const httpHeaderAccepts: string[] = [
                'application/json'
            ];
            localVarHttpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        }
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        let localVarHttpContext: HttpContext | undefined = options && options.context;
        if (localVarHttpContext === undefined) {
            localVarHttpContext = new HttpContext();
        }

        let localVarTransferCache: boolean | undefined = options && options.transferCache;
        if (localVarTransferCache === undefined) {
            localVarTransferCache = true;
        }


        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/webportal/v1/subscriptions/${this.configuration.encodeParam({name: "subscriptionKey", value: subscriptionKey, in: "path", style: "simple", explode: false, dataType: "string", dataFormat: undefined})}`;
        return this.httpClient.request<WebPortalSubscriptionDetailsResponse>('get', `${this.configuration.basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                responseType: <any>responseType_,
                withCredentials: this.configuration.withCredentials,
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Load paged balance cases of a specific subscription with pagination
     * @param xTenantKey The key of the tenant
     * @param subscriptionKey 
     * @param page The index of the page to retrieve
     * @param size The number of items per page
     * @param sort The field to sort the balance cases by.
     * @param direction The direction to sort by. Supported values are \&#39;asc\&#39;, \&#39;desc\&#39;
     * @param filter The filter to apply on the balance cases. This can be any attribute of the balance case.
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public loadSubscriptionBalanceCases(xTenantKey: string, subscriptionKey: string, page?: number, size?: number, sort?: string, direction?: string, filter?: string, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<WebPortalBalanceCasesPagedResponse>;
    public loadSubscriptionBalanceCases(xTenantKey: string, subscriptionKey: string, page?: number, size?: number, sort?: string, direction?: string, filter?: string, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<WebPortalBalanceCasesPagedResponse>>;
    public loadSubscriptionBalanceCases(xTenantKey: string, subscriptionKey: string, page?: number, size?: number, sort?: string, direction?: string, filter?: string, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<WebPortalBalanceCasesPagedResponse>>;
    public loadSubscriptionBalanceCases(xTenantKey: string, subscriptionKey: string, page?: number, size?: number, sort?: string, direction?: string, filter?: string, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (xTenantKey === null || xTenantKey === undefined) {
            throw new Error('Required parameter xTenantKey was null or undefined when calling loadSubscriptionBalanceCases.');
        }
        if (subscriptionKey === null || subscriptionKey === undefined) {
            throw new Error('Required parameter subscriptionKey was null or undefined when calling loadSubscriptionBalanceCases.');
        }

        let localVarQueryParameters = new HttpParams({encoder: this.encoder});
        if (page !== undefined && page !== null) {
          localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
            <any>page, 'page');
        }
        if (size !== undefined && size !== null) {
          localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
            <any>size, 'size');
        }
        if (sort !== undefined && sort !== null) {
          localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
            <any>sort, 'sort');
        }
        if (direction !== undefined && direction !== null) {
          localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
            <any>direction, 'direction');
        }
        if (filter !== undefined && filter !== null) {
          localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
            <any>filter, 'filter');
        }

        let localVarHeaders = this.defaultHeaders;
        if (xTenantKey !== undefined && xTenantKey !== null) {
            localVarHeaders = localVarHeaders.set('x-tenant-key', String(xTenantKey));
        }

        let localVarHttpHeaderAcceptSelected: string | undefined = options && options.httpHeaderAccept;
        if (localVarHttpHeaderAcceptSelected === undefined) {
            // to determine the Accept header
            const httpHeaderAccepts: string[] = [
                'application/json'
            ];
            localVarHttpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        }
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        let localVarHttpContext: HttpContext | undefined = options && options.context;
        if (localVarHttpContext === undefined) {
            localVarHttpContext = new HttpContext();
        }

        let localVarTransferCache: boolean | undefined = options && options.transferCache;
        if (localVarTransferCache === undefined) {
            localVarTransferCache = true;
        }


        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/webportal/v1/subscriptions/${this.configuration.encodeParam({name: "subscriptionKey", value: subscriptionKey, in: "path", style: "simple", explode: false, dataType: "string", dataFormat: undefined})}/balance-cases`;
        return this.httpClient.request<WebPortalBalanceCasesPagedResponse>('get', `${this.configuration.basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                params: localVarQueryParameters,
                responseType: <any>responseType_,
                withCredentials: this.configuration.withCredentials,
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Load paged history entries of a specific subscription with pagination
     * @param xTenantKey The key of the tenant
     * @param subscriptionKey 
     * @param page The index of the page to retrieve
     * @param size The number of items per page
     * @param sort The field to sort the history entries by.
     * @param direction The direction to sort by. Supported values are \&#39;asc\&#39;, \&#39;desc\&#39;
     * @param filter The filter to apply on the history entries. This can be any attribute of the history entry.
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public loadSubscriptionHistoryEntries(xTenantKey: string, subscriptionKey: string, page?: number, size?: number, sort?: string, direction?: string, filter?: string, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<WebPortalSubscriptionHistoryEntryPagedResponse>;
    public loadSubscriptionHistoryEntries(xTenantKey: string, subscriptionKey: string, page?: number, size?: number, sort?: string, direction?: string, filter?: string, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<WebPortalSubscriptionHistoryEntryPagedResponse>>;
    public loadSubscriptionHistoryEntries(xTenantKey: string, subscriptionKey: string, page?: number, size?: number, sort?: string, direction?: string, filter?: string, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<WebPortalSubscriptionHistoryEntryPagedResponse>>;
    public loadSubscriptionHistoryEntries(xTenantKey: string, subscriptionKey: string, page?: number, size?: number, sort?: string, direction?: string, filter?: string, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (xTenantKey === null || xTenantKey === undefined) {
            throw new Error('Required parameter xTenantKey was null or undefined when calling loadSubscriptionHistoryEntries.');
        }
        if (subscriptionKey === null || subscriptionKey === undefined) {
            throw new Error('Required parameter subscriptionKey was null or undefined when calling loadSubscriptionHistoryEntries.');
        }

        let localVarQueryParameters = new HttpParams({encoder: this.encoder});
        if (page !== undefined && page !== null) {
          localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
            <any>page, 'page');
        }
        if (size !== undefined && size !== null) {
          localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
            <any>size, 'size');
        }
        if (sort !== undefined && sort !== null) {
          localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
            <any>sort, 'sort');
        }
        if (direction !== undefined && direction !== null) {
          localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
            <any>direction, 'direction');
        }
        if (filter !== undefined && filter !== null) {
          localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
            <any>filter, 'filter');
        }

        let localVarHeaders = this.defaultHeaders;
        if (xTenantKey !== undefined && xTenantKey !== null) {
            localVarHeaders = localVarHeaders.set('x-tenant-key', String(xTenantKey));
        }

        let localVarHttpHeaderAcceptSelected: string | undefined = options && options.httpHeaderAccept;
        if (localVarHttpHeaderAcceptSelected === undefined) {
            // to determine the Accept header
            const httpHeaderAccepts: string[] = [
                'application/json'
            ];
            localVarHttpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        }
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        let localVarHttpContext: HttpContext | undefined = options && options.context;
        if (localVarHttpContext === undefined) {
            localVarHttpContext = new HttpContext();
        }

        let localVarTransferCache: boolean | undefined = options && options.transferCache;
        if (localVarTransferCache === undefined) {
            localVarTransferCache = true;
        }


        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/webportal/v1/subscriptions/${this.configuration.encodeParam({name: "subscriptionKey", value: subscriptionKey, in: "path", style: "simple", explode: false, dataType: "string", dataFormat: undefined})}/history-entries`;
        return this.httpClient.request<WebPortalSubscriptionHistoryEntryPagedResponse>('get', `${this.configuration.basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                params: localVarQueryParameters,
                responseType: <any>responseType_,
                withCredentials: this.configuration.withCredentials,
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Load paged items of a specific subscription with pagination
     * @param xTenantKey The key of the tenant
     * @param subscriptionKey 
     * @param page The index of the page to retrieve
     * @param size The number of items per page
     * @param sort The field to sort the items by.
     * @param direction The direction to sort by. Supported values are \&#39;asc\&#39;, \&#39;desc\&#39;
     * @param filter The filter to apply on the subscription items. This can be any attribute of the subscription items.
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public loadSubscriptionItems(xTenantKey: string, subscriptionKey: string, page?: number, size?: number, sort?: string, direction?: string, filter?: string, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<WebPortalSubscriptionItemsPagedResponse>;
    public loadSubscriptionItems(xTenantKey: string, subscriptionKey: string, page?: number, size?: number, sort?: string, direction?: string, filter?: string, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<WebPortalSubscriptionItemsPagedResponse>>;
    public loadSubscriptionItems(xTenantKey: string, subscriptionKey: string, page?: number, size?: number, sort?: string, direction?: string, filter?: string, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<WebPortalSubscriptionItemsPagedResponse>>;
    public loadSubscriptionItems(xTenantKey: string, subscriptionKey: string, page?: number, size?: number, sort?: string, direction?: string, filter?: string, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (xTenantKey === null || xTenantKey === undefined) {
            throw new Error('Required parameter xTenantKey was null or undefined when calling loadSubscriptionItems.');
        }
        if (subscriptionKey === null || subscriptionKey === undefined) {
            throw new Error('Required parameter subscriptionKey was null or undefined when calling loadSubscriptionItems.');
        }

        let localVarQueryParameters = new HttpParams({encoder: this.encoder});
        if (page !== undefined && page !== null) {
          localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
            <any>page, 'page');
        }
        if (size !== undefined && size !== null) {
          localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
            <any>size, 'size');
        }
        if (sort !== undefined && sort !== null) {
          localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
            <any>sort, 'sort');
        }
        if (direction !== undefined && direction !== null) {
          localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
            <any>direction, 'direction');
        }
        if (filter !== undefined && filter !== null) {
          localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
            <any>filter, 'filter');
        }

        let localVarHeaders = this.defaultHeaders;
        if (xTenantKey !== undefined && xTenantKey !== null) {
            localVarHeaders = localVarHeaders.set('x-tenant-key', String(xTenantKey));
        }

        let localVarHttpHeaderAcceptSelected: string | undefined = options && options.httpHeaderAccept;
        if (localVarHttpHeaderAcceptSelected === undefined) {
            // to determine the Accept header
            const httpHeaderAccepts: string[] = [
                'application/json'
            ];
            localVarHttpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        }
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        let localVarHttpContext: HttpContext | undefined = options && options.context;
        if (localVarHttpContext === undefined) {
            localVarHttpContext = new HttpContext();
        }

        let localVarTransferCache: boolean | undefined = options && options.transferCache;
        if (localVarTransferCache === undefined) {
            localVarTransferCache = true;
        }


        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/webportal/v1/subscriptions/${this.configuration.encodeParam({name: "subscriptionKey", value: subscriptionKey, in: "path", style: "simple", explode: false, dataType: "string", dataFormat: undefined})}/items`;
        return this.httpClient.request<WebPortalSubscriptionItemsPagedResponse>('get', `${this.configuration.basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                params: localVarQueryParameters,
                responseType: <any>responseType_,
                withCredentials: this.configuration.withCredentials,
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Load all subscriptions
     * @param xTenantKey The key of the tenant
     * @param page The index of the page to retrieve
     * @param size The number of items per page
     * @param sort The field to sort the subscriptions by. Supported values are \&#39;createdAt\&#39;, \&#39;name\&#39;, \&#39;customerKey\&#39;
     * @param direction The direction to sort by. Supported values are \&#39;asc\&#39;, \&#39;desc\&#39;
     * @param filter The filter to apply on the subscriptions. This can be any attribute of the subscription.
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public loadSubscriptions(xTenantKey: string, page?: number, size?: number, sort?: string, direction?: string, filter?: string, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<WebPortalSubscriptionsPagedResponse>;
    public loadSubscriptions(xTenantKey: string, page?: number, size?: number, sort?: string, direction?: string, filter?: string, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<WebPortalSubscriptionsPagedResponse>>;
    public loadSubscriptions(xTenantKey: string, page?: number, size?: number, sort?: string, direction?: string, filter?: string, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<WebPortalSubscriptionsPagedResponse>>;
    public loadSubscriptions(xTenantKey: string, page?: number, size?: number, sort?: string, direction?: string, filter?: string, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (xTenantKey === null || xTenantKey === undefined) {
            throw new Error('Required parameter xTenantKey was null or undefined when calling loadSubscriptions.');
        }

        let localVarQueryParameters = new HttpParams({encoder: this.encoder});
        if (page !== undefined && page !== null) {
          localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
            <any>page, 'page');
        }
        if (size !== undefined && size !== null) {
          localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
            <any>size, 'size');
        }
        if (sort !== undefined && sort !== null) {
          localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
            <any>sort, 'sort');
        }
        if (direction !== undefined && direction !== null) {
          localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
            <any>direction, 'direction');
        }
        if (filter !== undefined && filter !== null) {
          localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
            <any>filter, 'filter');
        }

        let localVarHeaders = this.defaultHeaders;
        if (xTenantKey !== undefined && xTenantKey !== null) {
            localVarHeaders = localVarHeaders.set('x-tenant-key', String(xTenantKey));
        }

        let localVarHttpHeaderAcceptSelected: string | undefined = options && options.httpHeaderAccept;
        if (localVarHttpHeaderAcceptSelected === undefined) {
            // to determine the Accept header
            const httpHeaderAccepts: string[] = [
                'application/json'
            ];
            localVarHttpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        }
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        let localVarHttpContext: HttpContext | undefined = options && options.context;
        if (localVarHttpContext === undefined) {
            localVarHttpContext = new HttpContext();
        }

        let localVarTransferCache: boolean | undefined = options && options.transferCache;
        if (localVarTransferCache === undefined) {
            localVarTransferCache = true;
        }


        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/webportal/v1/subscriptions`;
        return this.httpClient.request<WebPortalSubscriptionsPagedResponse>('get', `${this.configuration.basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                params: localVarQueryParameters,
                responseType: <any>responseType_,
                withCredentials: this.configuration.withCredentials,
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Pause subscription by key
     * @param xTenantKey The key of the tenant
     * @param subscriptionKey 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public pauseSubscription(xTenantKey: string, subscriptionKey: string, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<WebPortalSubscriptionStatusResponse>;
    public pauseSubscription(xTenantKey: string, subscriptionKey: string, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<WebPortalSubscriptionStatusResponse>>;
    public pauseSubscription(xTenantKey: string, subscriptionKey: string, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<WebPortalSubscriptionStatusResponse>>;
    public pauseSubscription(xTenantKey: string, subscriptionKey: string, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (xTenantKey === null || xTenantKey === undefined) {
            throw new Error('Required parameter xTenantKey was null or undefined when calling pauseSubscription.');
        }
        if (subscriptionKey === null || subscriptionKey === undefined) {
            throw new Error('Required parameter subscriptionKey was null or undefined when calling pauseSubscription.');
        }

        let localVarHeaders = this.defaultHeaders;
        if (xTenantKey !== undefined && xTenantKey !== null) {
            localVarHeaders = localVarHeaders.set('x-tenant-key', String(xTenantKey));
        }

        let localVarHttpHeaderAcceptSelected: string | undefined = options && options.httpHeaderAccept;
        if (localVarHttpHeaderAcceptSelected === undefined) {
            // to determine the Accept header
            const httpHeaderAccepts: string[] = [
                'application/json'
            ];
            localVarHttpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        }
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        let localVarHttpContext: HttpContext | undefined = options && options.context;
        if (localVarHttpContext === undefined) {
            localVarHttpContext = new HttpContext();
        }

        let localVarTransferCache: boolean | undefined = options && options.transferCache;
        if (localVarTransferCache === undefined) {
            localVarTransferCache = true;
        }


        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/webportal/v1/subscriptions/${this.configuration.encodeParam({name: "subscriptionKey", value: subscriptionKey, in: "path", style: "simple", explode: false, dataType: "string", dataFormat: undefined})}/pause`;
        return this.httpClient.request<WebPortalSubscriptionStatusResponse>('post', `${this.configuration.basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                responseType: <any>responseType_,
                withCredentials: this.configuration.withCredentials,
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Update a specific subscription
     * @param xTenantKey The key of the tenant
     * @param subscriptionKey 
     * @param webPortalSubscriptionDetailsData 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public updateSubscription(xTenantKey: string, subscriptionKey: string, webPortalSubscriptionDetailsData: WebPortalSubscriptionDetailsData, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<WebPortalSubscriptionDetailsResponse>;
    public updateSubscription(xTenantKey: string, subscriptionKey: string, webPortalSubscriptionDetailsData: WebPortalSubscriptionDetailsData, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<WebPortalSubscriptionDetailsResponse>>;
    public updateSubscription(xTenantKey: string, subscriptionKey: string, webPortalSubscriptionDetailsData: WebPortalSubscriptionDetailsData, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<WebPortalSubscriptionDetailsResponse>>;
    public updateSubscription(xTenantKey: string, subscriptionKey: string, webPortalSubscriptionDetailsData: WebPortalSubscriptionDetailsData, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (xTenantKey === null || xTenantKey === undefined) {
            throw new Error('Required parameter xTenantKey was null or undefined when calling updateSubscription.');
        }
        if (subscriptionKey === null || subscriptionKey === undefined) {
            throw new Error('Required parameter subscriptionKey was null or undefined when calling updateSubscription.');
        }
        if (webPortalSubscriptionDetailsData === null || webPortalSubscriptionDetailsData === undefined) {
            throw new Error('Required parameter webPortalSubscriptionDetailsData was null or undefined when calling updateSubscription.');
        }

        let localVarHeaders = this.defaultHeaders;
        if (xTenantKey !== undefined && xTenantKey !== null) {
            localVarHeaders = localVarHeaders.set('x-tenant-key', String(xTenantKey));
        }

        let localVarHttpHeaderAcceptSelected: string | undefined = options && options.httpHeaderAccept;
        if (localVarHttpHeaderAcceptSelected === undefined) {
            // to determine the Accept header
            const httpHeaderAccepts: string[] = [
                'application/json'
            ];
            localVarHttpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        }
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        let localVarHttpContext: HttpContext | undefined = options && options.context;
        if (localVarHttpContext === undefined) {
            localVarHttpContext = new HttpContext();
        }

        let localVarTransferCache: boolean | undefined = options && options.transferCache;
        if (localVarTransferCache === undefined) {
            localVarTransferCache = true;
        }


        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/webportal/v1/subscriptions/${this.configuration.encodeParam({name: "subscriptionKey", value: subscriptionKey, in: "path", style: "simple", explode: false, dataType: "string", dataFormat: undefined})}`;
        return this.httpClient.request<WebPortalSubscriptionDetailsResponse>('patch', `${this.configuration.basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                body: webPortalSubscriptionDetailsData,
                responseType: <any>responseType_,
                withCredentials: this.configuration.withCredentials,
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Update a subscription note
     * @param xTenantKey The key of the tenant
     * @param subscriptionKey 
     * @param noteKey 
     * @param updateNoteRequest 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public updateSubscriptionNote(xTenantKey: string, subscriptionKey: string, noteKey: string, updateNoteRequest: UpdateNoteRequest, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<WebPortalNotesListEntry>;
    public updateSubscriptionNote(xTenantKey: string, subscriptionKey: string, noteKey: string, updateNoteRequest: UpdateNoteRequest, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<WebPortalNotesListEntry>>;
    public updateSubscriptionNote(xTenantKey: string, subscriptionKey: string, noteKey: string, updateNoteRequest: UpdateNoteRequest, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<WebPortalNotesListEntry>>;
    public updateSubscriptionNote(xTenantKey: string, subscriptionKey: string, noteKey: string, updateNoteRequest: UpdateNoteRequest, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (xTenantKey === null || xTenantKey === undefined) {
            throw new Error('Required parameter xTenantKey was null or undefined when calling updateSubscriptionNote.');
        }
        if (subscriptionKey === null || subscriptionKey === undefined) {
            throw new Error('Required parameter subscriptionKey was null or undefined when calling updateSubscriptionNote.');
        }
        if (noteKey === null || noteKey === undefined) {
            throw new Error('Required parameter noteKey was null or undefined when calling updateSubscriptionNote.');
        }
        if (updateNoteRequest === null || updateNoteRequest === undefined) {
            throw new Error('Required parameter updateNoteRequest was null or undefined when calling updateSubscriptionNote.');
        }

        let localVarHeaders = this.defaultHeaders;
        if (xTenantKey !== undefined && xTenantKey !== null) {
            localVarHeaders = localVarHeaders.set('x-tenant-key', String(xTenantKey));
        }

        let localVarHttpHeaderAcceptSelected: string | undefined = options && options.httpHeaderAccept;
        if (localVarHttpHeaderAcceptSelected === undefined) {
            // to determine the Accept header
            const httpHeaderAccepts: string[] = [
                'application/json'
            ];
            localVarHttpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        }
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        let localVarHttpContext: HttpContext | undefined = options && options.context;
        if (localVarHttpContext === undefined) {
            localVarHttpContext = new HttpContext();
        }

        let localVarTransferCache: boolean | undefined = options && options.transferCache;
        if (localVarTransferCache === undefined) {
            localVarTransferCache = true;
        }


        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/webportal/v1/subscriptions/${this.configuration.encodeParam({name: "subscriptionKey", value: subscriptionKey, in: "path", style: "simple", explode: false, dataType: "string", dataFormat: undefined})}/notes/${this.configuration.encodeParam({name: "noteKey", value: noteKey, in: "path", style: "simple", explode: false, dataType: "string", dataFormat: undefined})}`;
        return this.httpClient.request<WebPortalNotesListEntry>('put', `${this.configuration.basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                body: updateNoteRequest,
                responseType: <any>responseType_,
                withCredentials: this.configuration.withCredentials,
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

}
