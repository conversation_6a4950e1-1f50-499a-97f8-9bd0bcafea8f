openapi: "3.0.3"
info:
  version: 1.0.0
  title: billing-solution frontend
servers:
  - url: http://localhost:8080/
tags:
  - name: webPortalCustomer
    description: Operations about customers
  - name: webPortalSupplier
    description: Operations about suppliers
  - name: webPortalOrder
    description: Operations about orders
  - name: webPortalDocument
    description: Operations about documents
  - name: webPortalPaymentTransaction
    description: Operations about payment transactions
  - name: webPortalPaymentAssignment
    description: Operations about payment payment assignments
  - name: webPortalBookingRule
    description: Operations about booking rules
  - name: webPortalPostingRecord
    description: Operations about posting records
  - name: webPortalBookingAccount
    description: Operations about booking accounts
  - name: webPortalCurrencyExchangeRates
    description: Operations about currency exchange rates
  - name: webPortalWorkflow
    description: Operations about workflow management
  - name: webPortalApproval
    description: Operations about approvals
  - name: webPortalReport
    description: Operations about reports
  - name: webPortalSentArchive
    description: Operations about sent archive
  - name: webPortalUser
    description: Operations about users
  - name: webPortalRole
    description: Operations about roles
  - name: webPortalSettlementReport
    description: Operations about settlement reports
  - name: webPortalBalanceCase
    description: Operations about balance cases
  - name: webPortalPaymentAccount
    description: Operations about payment accounts
  - name: webPortalBusinessSegment
    description: Operations about business segments
  - name: webPortalPaymentCategorizationRule
    description: Operations about payment categorization rules
  - name: webPortalInternal
    description: Operations about internal stuff
  - name: webPortalTranslation
    description: Operations about translations
  - name: webPortalNotes
    description: Operations about notes
  - name: webPortalPredefinedItems
    description: Operations about predefined items
  - name: webPortalSubscription
    description: Operations about subscriptions
paths:
  /webportal/v1/customers:
    get:
      tags:
        - webPortalCustomer
      summary: Load all customers
      operationId: loadCustomers
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the orders by. Supported values are 'createdAt', 'firstName', 'lastName', 'companyName'
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on the customers. This can be any attribute of the customer.
          schema:
            type: string
      responses:
        '200':
          description: Page of customers
          content:
            application/json:
              schema:
                $ref: './component/webportal/customerlist/WebPortalCustomersPagedResponse.yaml'
    post:
      tags:
        - webPortalCustomer
      summary: Create a new customer
      operationId: createCustomer
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/webportal/customerdetails/WebPortalCustomerDetailsData.yaml'
      responses:
        '201':
          description: The customer was successfully created
          content:
            application/json:
              schema:
                $ref: './component/webportal/customerdetails/WebPortalCustomerDetailsResponse.yaml'
        '400':
          description: Invalid input
        '404':
          description: Customer not found
  /webportal/v1/customers/{customer_key}:
    get:
      tags:
        - webPortalCustomer
      operationId: loadCustomer
      summary: Get a specific customer
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: customer_key
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: './component/webportal/customerdetails/WebPortalCustomerDetailsResponse.yaml'
        '404':
          description: Customer not found
    patch:
      tags:
        - webPortalCustomer
      summary: Update a specific customer
      operationId: updateCustomer
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: customer_key
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/webportal/customerdetails/WebPortalCustomerDetailsData.yaml'
      responses:
        '200':
          description: The customer was successfully updated
          content:
            application/json:
              schema:
                $ref: './component/webportal/customerdetails/WebPortalCustomerDetailsResponse.yaml'
        '400':
          description: Invalid input
        '404':
          description: Customer not found
  /webportal/v1/customers/{customer_key}/billingaddress:
    put:
      tags:
        - webPortalCustomer
      summary: Update the billing address of a specific customer
      operationId: updateCustomerBillingAddress
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: customer_key
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/model/Address.yaml'
      responses:
        '200':
          description: The customer was successfully updated
          content:
            application/json:
              schema:
                $ref: '././component/model/Address.yaml'
        '400':
          description: Invalid input
        '404':
          description: Customer not found
  /webportal/v1/customers/{customer_key}/shippingaddresses/{address_key}:
    put:
      tags:
        - webPortalCustomer
      summary: Update the shipping address of a specific customer
      operationId: updateCustomerShippingAddress
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: customer_key
          in: path
          required: true
          schema:
            type: string
        - name: address_key
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/model/Address.yaml'
      responses:
        '200':
          description: Address was successfully updated
          content:
            application/json:
              schema:
                $ref: '././component/model/Address.yaml'
        '400':
          description: Invalid input
        '404':
          description: Customer not found
  /webportal/v1/customers/{customer_key}/shippingaddresses/{address_key}/status:
    put:
      tags:
        - webPortalCustomer
      summary: Update the status of a specific shipping address
      operationId: updateCustomerAddressStatus
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: customer_key
          in: path
          required: true
          schema:
            type: string
        - name: address_key
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/webportal/customerdetails/WebPortalCustomerDetailsCustomerAddressStatusUpdateRequest.yaml'
      responses:
        '200':
          description: The customer was successfully updated
          content:
            application/json:
              schema:
                $ref: './component/webportal/customerdetails/WebPortalCustomerDetailsResponse.yaml'
        '400':
          description: Invalid input
        '404':
          description: Customer not found
  /webportal/v1/customers/{customer_key}/shippingaddresses:
    get:
      tags:
        - webPortalCustomer
      summary: Load all shipping addresses of a specific customer
      operationId: loadCustomerShippingAddresses
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: customer_key
          in: path
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the addresses by. Supported values are 'createdAt', 'addressLine1', 'city', 'state', 'country'
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on the addresses. This can be any attribute of the address.
          schema:
            type: string
      responses:
        '200':
          description: Page of shipping addresses
          content:
            application/json:
              schema:
                $ref: './component/webportal/customerdetails/WebPortalCustomerShippingAddressesPagedResponse.yaml'
    post:
      tags:
        - webPortalCustomer
      summary: Creates a new shipping address for a specific customer
      operationId: addCustomerShippingAddress
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: customer_key
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/model/Address.yaml'
      responses:
        '200':
          description: The shipping address was successfully created
          content:
            application/json:
              schema:
                $ref: '././component/model/Address.yaml'
        '400':
          description: Invalid input
        '404':
          description: Customer not found
  /webportal/v1/suppliers:
    get:
      tags:
        - webPortalSupplier
      summary: Load all suppliers
      operationId: loadSuppliers
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the orders by. Supported values are 'createdAt', 'companyName'
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on the suppliers. This can be any attribute of the supplier.
          schema:
            type: string
      responses:
        '200':
          description: Page of suppliers
          content:
            application/json:
              schema:
                $ref: './component/webportal/supplierlist/WebPortalSuppliersPagedResponse.yaml'
    post:
      tags:
        - webPortalSupplier
      summary: Create a new supplier
      operationId: createSupplier
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/webportal/supplierdetails/WebPortalSupplierDetailsData.yaml'
      responses:
        '201':
          description: The supplier was successfully created
          content:
            application/json:
              schema:
                $ref: './component/webportal/supplierdetails/WebPortalSupplierDetailsResponse.yaml'
        '400':
          description: Invalid input
  /webportal/v1/suppliers/{supplier_key}:
    get:
      tags:
        - webPortalSupplier
      operationId: loadSupplier
      summary: Get a specific supplier
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: supplier_key
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: './component/webportal/supplierdetails/WebPortalSupplierDetailsResponse.yaml'
        '404':
          description: Customer not found
    patch:
      tags:
        - webPortalSupplier
      summary: Update a specific supplier
      operationId: updateSupplier
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: supplier_key
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/webportal/supplierdetails/WebPortalSupplierDetailsData.yaml'
      responses:
        '200':
          description: The supplier was successfully updated
          content:
            application/json:
              schema:
                $ref: './component/webportal/supplierdetails/WebPortalSupplierDetailsResponse.yaml'
        '400':
          description: Invalid input
        '404':
          description: Supplier not found
  /webportal/v1/suppliers/{supplier_key}/billingaddress:
    put:
      tags:
        - webPortalSupplier
      summary: Update the billing address of a specific supplier
      operationId: updateSupplierBillingAddress
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: supplier_key
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/model/Address.yaml'
      responses:
        '200':
          description: The customer was successfully updated
          content:
            application/json:
              schema:
                $ref: '././component/model/Address.yaml'
        '400':
          description: Invalid input
        '404':
          description: Supplier not found
  /webportal/v1/orders:
    get:
      tags:
        - webPortalOrder
      summary: Load all orders by creation date (descending)
      operationId: loadOrders
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the orders by. Supported values are 'key', 'orderDate', 'orderState'
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on the orders. This can be any attribute of the order.
          schema:
            type: string
      responses:
        '200':
          description: Page of orders
          content:
            application/json:
              schema:
                $ref: './component/webportal/orderlist/WebPortalOrdersPagedResponse.yaml'
        '400':
          description: Invalid input
  /webportal/v1/orders/keys:
    get:
      tags:
        - webPortalOrder
      summary: Load all order keys by key (ascending)
      operationId: loadOrderKeys
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the orders by. Supported values are 'key', 'orderDate', 'orderState'
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on the orders. This can be any attribute of the order.
          schema:
            type: string
      responses:
        '200':
          description: Page of order keys
          content:
            application/json:
              schema:
                $ref: './component/webportal/orderlist/WebPortalKeysPagedResponse.yaml'
        '400':
          description: Invalid input
    post:
      tags:
        - webPortalOrder
      summary: Create a new order
      operationId: createOrder
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/webportal/ordercapture/WebPortalOrderCapture.yaml'
      responses:
        '202':
          description: The order creation was successfully accepted
        '400':
          description: Invalid input
  /webportal/v1/businesssegments/{business_segment_key}/ordertemplates:
    get:
      tags:
        - webPortalOrder
      summary: Load all order templates of a business segment
      operationId: loadOrderTemplates
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: business_segment_key
          in: path
          description: The key of the business segment
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the order templates by. Supported values are 'title', 'createdAt'
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on the order templates. This can be any attribute of the order template.
          schema:
            type: string
      responses:
        '200':
          description: Page of order templates
          content:
            application/json:
              schema:
                $ref: './component/webportal/ordertemplate/WebPortalOrderTemplatesPagedResponse.yaml'
        '400':
          description: Invalid input
    post:
      tags:
        - webPortalOrder
      summary: Create a new order template
      operationId: createOrderTemplate
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: business_segment_key
          in: path
          description: The key of the business segment
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/webportal/ordertemplate/WebPortalOrderTemplateRequest.yaml'
      responses:
        '200':
          description: The order template was successfully created
        '400':
          description: Invalid input
  /webportal/v1/businesssegments/{business_segment_key}/ordertemplates/{key}:
    put:
      tags:
        - webPortalOrder
      summary: Update a specific order template
      operationId: updateOrderTemplate
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: business_segment_key
          in: path
          description: The key of the business segment
          required: true
          schema:
            type: string
        - name: key
          in: path
          description: The key of the order template
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/webportal/ordertemplate/WebPortalOrderTemplateRequest.yaml'
      responses:
        '200':
          description: The order template was successfully updated
        '400':
          description: Invalid input
  /webportal/v1/businesssegments/{business_segment_key}/ordertemplates/{order_template_key}/items:
    get:
      tags:
        - webPortalOrder
      summary: Load all order template items by order template key
      operationId: loadOrderTemplateItems
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: business_segment_key
          in: path
          description: The key of the business segment
          required: true
          schema:
            type: string
        - name: order_template_key
          in: path
          description: The key of the order template
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the order template items by. Supported values are 'title', 'createdAt'
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on the order template items. This can be any attribute of the order template.
          schema:
            type: string
      responses:
        '200':
          description: Page of order template items
          content:
            application/json:
              schema:
                $ref: './component/webportal/ordertemplate/WebPortalOrderTemplateItemsPagedResponse.yaml'
        '400':
          description: Invalid input
  /webportal/v1/bookingrules:
    get:
      tags:
        - webPortalBookingRule
      summary: Load all booking rules by description
      operationId: loadBookingRules
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the items by. Supported values are TODO
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on the order items. This can be any attribute of the order items.
          schema:
            type: string
      responses:
        '200':
          description: Page of booking rules
          content:
            application/json:
              schema:
                $ref: './component/webportal/bookingrulelist/WebPortalBookingRulesPagedResponse.yaml'
        '400':
          description: Invalid input
    post:
      tags:
        - webPortalBookingRule
      summary: Create a new booking rule
      operationId: createBookingRule
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/webportal/bookingruledetails/WebPortalBookingRuleDetails.yaml'
      responses:
        '201':
          description: The booking rule was successfully created
          content:
            application/json:
              schema:
                $ref: './component/webportal/bookingruledetails/WebPortalBookingRuleDetails.yaml'
        '400':
          description: Invalid input
  /webportal/v1/bookingrules/{bookingrule_key}:
    get:
      tags:
        - webPortalBookingRule
      summary: load booking rule by key
      operationId: loadBookingRule
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: bookingrule_key
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: './component/webportal/bookingruledetails/WebPortalBookingRuleDetails.yaml'
        '404':
          description: Booking rule not found
    put:
      tags:
        - webPortalBookingRule
      summary: Update a specific booking rule
      operationId: updateBookingRule
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: bookingrule_key
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/webportal/bookingruledetails/WebPortalBookingRuleDetails.yaml'
      responses:
        '200':
          description: The booking rule was successfully updated
          content:
            application/json:
              schema:
                $ref: './component/webportal/bookingruledetails/WebPortalBookingRuleDetails.yaml'
        '400':
          description: Invalid input
        '404':
          description: Booking rule not found
  /webportal/v1/paymenttransactions:
    get:
      tags:
        - webPortalPaymentTransaction
      summary: Load all payment transactions
      operationId: loadPaymentTransactions
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the items by. Supported values are TODO
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on the order items. This can be any attribute of the order items.
          schema:
            type: string
      responses:
        '200':
          description: Page of payment transactions
          content:
            application/json:
              schema:
                $ref: './component/webportal/paymenttransaction/list/WebPortalPaymentTransactionsPagedResponse.yaml'
        '400':
          description: Invalid input
  /webportal/v1/paymenttransactions/count-open:
    get:
      tags:
        - webPortalPaymentTransaction
      summary: Count open payment transactions
      operationId: countOpenPaymentTransactions
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Count of open payment transactions
          content:
            application/json:
              schema:
                type: integer
                format: int64
  /webportal/v1/paymenttransactions/{payment_transaction_key}:
    get:
      tags:
        - webPortalPaymentTransaction
      summary: Load a specific payment transaction
      operationId: loadPaymentTransactionClearing
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: payment_transaction_key
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: './component/webportal/paymenttransaction/details/WebPortalPaymentTransactionDetails.yaml'
        '404':
          description: Payment transaction not found
  /webportal/v1/paymentassignments:
    get:
      tags:
        - webPortalPaymentAssignment
      summary: Load all payment assignments
      operationId: loadPaymentAssignments
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the items by. Supported values are TODO
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on the order items. This can be any attribute of the order items.
          schema:
            type: string
      responses:
        '200':
          description: Page of payment assignments
          content:
            application/json:
              schema:
                $ref: './component/webportal/paymentassignment/list/WebPortalPaymentAssignmentsPagedResponse.yaml'
        '400':
          description: Invalid input
    post:
      tags:
        - webPortalPaymentAssignment
      summary: Create a new payment assignment
      operationId: createPaymentAssignment
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/webportal/paymentassignment/details/WebPortalPaymentAssignmentDetails.yaml'
      responses:
        '200':
          description: The payment assignment was successfully created
        '202':
          description: The payment assignment was successfully accepted
  /webportal/v1/payment-assignments/{payment_assignment_key}:
    get:
      tags:
        - webPortalPaymentAssignment
      summary: Load a specific payment assignment
      operationId: loadPaymentAssignment
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: payment_assignment_key
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: './component/webportal/paymentassignment/details/WebPortalPaymentAssignmentDetails.yaml'
        '404':
          description: Payment assignment not found
  /webportal/v1/payment-assignments/{payment_assignment_key}/reverse:
    post:
      tags:
        - webPortalPaymentAssignment
      summary: Reverse a specific payment assignment
      operationId: reversePaymentAssignment
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: payment_assignment_key
          in: path
          required: true
          schema:
            type: string
      responses:
        '201':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: './component/webportal/paymentassignment/details/WebPortalPaymentAssignmentDetails.yaml'
        '404':
          description: Payment assignment not found
  /webportal/v1/booking-accounts:
    get:
      tags:
        - webPortalBookingAccount
      summary: Load all booking accounts by account number (asc)
      operationId: loadBookingAccounts
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the items by. Supported values are TODO
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on. This can be any attribute of the booking account.
          schema:
            type: string
      responses:
        '200':
          description: Page of booking accounts
          content:
            application/json:
              schema:
                $ref: './component/webportal/bookingaccountlist/WebPortalBookingAccountsPagedResponse.yaml'
        '400':
          description: Invalid input
    post:
      tags:
        - webPortalBookingAccount
      summary: Create a new booking account
      operationId: createBookingAccount
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/webportal/bookingaccountdetails/WebPortalBookingAccountDetails.yaml'
      responses:
        '201':
          description: The booking account was successfully created
          content:
            application/json:
              schema:
                $ref: './component/webportal/bookingaccountdetails/WebPortalBookingAccountDetails.yaml'
        '400':
          description: Invalid input
  /webportal/v1/booking-accounts/{key}:
    get:
      tags:
        - webPortalBookingAccount
      summary: Load details of a specific booking account
      operationId: loadBookingAccount
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: key
          in: path
          required: true
          description: The key of the booking account
          schema:
            type: string
      responses:
        '200':
          description: Details of the booking account
          content:
            application/json:
              schema:
                $ref: './component/webportal/bookingaccountdetails/WebPortalBookingAccountDetails.yaml'
        '400':
          description: Invalid input
    put:
      tags:
        - webPortalBookingAccount
      summary: Update a specific booking account
      operationId: updateBookingAccount
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: key
          in: path
          required: true
          description: The key of the booking account
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/webportal/bookingaccountdetails/WebPortalBookingAccountDetails.yaml'
      responses:
        '200':
          description: The booking account was successfully updated
          content:
            application/json:
              schema:
                $ref: './component/webportal/bookingaccountdetails/WebPortalBookingAccountDetails.yaml'
        '400':
          description: Invalid input
  /webportal/v1/currencyexchangerates:
    get:
      tags:
        - webPortalCurrencyExchangeRates
      summary: Load all currency exchange rates by validFrom (desc)
      operationId: loadCurrencyExchangeRates
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the items by. Supported values are TODO
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The rsql filter to apply on. This can be any attribute of the currency exchange rate.
          schema:
            type: string
      responses:
        '200':
          description: Page of currency exchange rates
          content:
            application/json:
              schema:
                $ref: './component/webportal/currencyexchangeratelist/WebPortalCurrencyExchangeRatesPagedResponse.yaml'
        '400':
          description: Invalid input
  /webportal/v1/manualbooking:
    post:
      tags:
        - webPortalPostingRecord
      summary: Create a new manual booking
      operationId: createManualBooking
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/webportal/manualbooking/WebPortalManualBookingDetails.yaml'
      responses:
        '201':
          description: The manual booking was successfully created
          content:
            application/json:
              schema:
                $ref: './component/webportal/manualbooking/WebPortalManualBookingDetails.yaml'
        '400':
          description: Invalid input
  /webportal/v1/postingrecords:
    get:
      tags:
        - webPortalPostingRecord
      summary: Load all posting records by creation date (descending)
      operationId: loadPostingRecords
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the items by. Supported values are TODO
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on the order items. This can be any attribute of the order items.
          schema:
            type: string
      responses:
        '200':
          description: Page of posting records
          content:
            application/json:
              schema:
                $ref: './component/webportal/postingrecordlist/WebPortalPostingRecordsPagedResponse.yaml'
        '400':
          description: Invalid input
  /webportal/v1/postingrecords/{posting_record_key}:
    get:
      tags:
        - webPortalPostingRecord
      summary: Load a specific posting record
      operationId: loadPostingRecord
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: posting_record_key
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: './component/webportal/postingrecorddetails/WebPortalPostingRecordDetails.yaml'
        '404':
          description: Posting record not found
  /webportal/v1/postingrecords/{posting_record_key}/entries:
    get:
      tags:
        - webPortalPostingRecord
      summary: Load paged entries of a specific posting record with pagination
      operationId: loadPostingRecordEntries
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: posting_record_key
          in: path
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the items by. Supported values are TODO
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on the order items. This can be any attribute of the order items.
          schema:
            type: string
      responses:
        '200':
          description: A list of posting record entries
          content:
            application/json:
              schema:
                $ref: './component/webportal/postingrecorddetails/WebPortalPostingRecordEntriesPagedResponse.yaml'
        '400':
          description: Invalid input
        '404':
          description: Order not found
  /webportal/v1/businesssegments:
    get:
      tags:
        - webPortalBusinessSegment
      summary: Load all business segments
      operationId: loadBusinessSegments
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the items by
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on the business segments. This can be any attribute of the business segment.
          schema:
            type: string
      responses:
        '200':
          description: Page of business segments
          content:
            application/json:
              schema:
                $ref: './component/webportal/businesssegmentlist/WebPortalBusinessSegmentsPagedResponse.yaml'
        '400':
          description: Invalid input
    post:
      tags:
        - webPortalBusinessSegment
      summary: Create a new business segment
      operationId: createBusinessSegment
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: key
          in: path
          required: true
          schema:
            type: string
        - name: invoiceNumberPattern
          in: query
          required: true
          schema:
            type: string
            description: The invoice number pattern
        - name: vatId
          in: query
          required: true
          schema:
            type: string
            description: The VAT ID
        - name: originatorAddressKey
          in: query
          required: true
          schema:
            type: string
            description: The key of the originator address
        - name: version
          in: query
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                logo:
                  type: string
                  format: binary
              required:
                - logo
      responses:
        '201':
          description: The business segment was successfully created
          content:
            application/json:
              schema:
                $ref: './component/webportal/businesssegmentdetails/WebPortalBusinessSegmentDetails.yaml'
        '400':
          description: Invalid input
  /webportal/v1/businesssegments/{segment_key}:
    get:
      tags:
        - webPortalBusinessSegment
      summary: Load a specific business segment
      operationId: loadBusinessSegment
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: segment_key
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: './component/webportal/businesssegmentdetails/WebPortalBusinessSegmentDetails.yaml'
        '404':
          description: Business segment not found
    put:
      tags:
        - webPortalBusinessSegment
      summary: Update a specific business segment
      operationId: updateBusinessSegment
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: segment_key
          in: path
          required: true
          schema:
            type: string
        - name: invoiceNumberPattern
          in: query
          required: true
          schema:
            type: string
            description: The invoice number pattern
        - name: vatId
          in: query
          required: true
          schema:
            type: string
            description: The VAT ID
        - name: originatorAddressKey
          in: query
          required: true
          schema:
            type: string
            description: The key of the originator address
        - name: version
          in: query
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                logo:
                  type: string
                  format: binary
      responses:
        '200':
          description: The business segment was successfully updated
          content:
            application/json:
              schema:
                $ref: './component/webportal/businesssegmentdetails/WebPortalBusinessSegmentDetails.yaml'
        '400':
          description: Invalid input
        '404':
          description: Business segment not found
  /webportal/v1/orders/{order_key}:
    get:
      tags:
        - webPortalOrder
      summary: load order by key
      operationId: loadOrder
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: order_key
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: './component/webportal/orderdetails/WebPortalOrderDetails.yaml'
        '404':
          description: Order not found
  /webportal/v1/orders/{order_key}/update:
    get:
      tags:
        - webPortalOrder
      summary: load order to update by key
      operationId: loadOrderUpdate
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: order_key
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: './component/webportal/orderdetails/WebPortalOrderDetails.yaml'
        '404':
          description: Order not found
    post:
      tags:
        - webPortalOrder
      summary: Updates an existing order
      operationId: updateOrder
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: order_key
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/webportal/orderdetails/WebPortalOrderUpdateDetails.yaml'
      responses:
        '202':
          description: The order update was successfully accepted
        '400':
          description: Invalid input
  /webportal/v1/orders/{order_key}/items:
    get:
      tags:
        - webPortalOrder
      summary: Load paged items of a specific order with pagination
      operationId: loadOrderItems
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: order_key
          in: path
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the items by. Supported values are TODO
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on the order items. This can be any attribute of the order items.
          schema:
            type: string
      responses:
        '200':
          description: Page of order items
          content:
            application/json:
              schema:
                $ref: './component/webportal/orderdetails/WebPortalOrderItemsPagedResponse.yaml'
        '400':
          description: Invalid input
        '404':
          description: Order not found
  /webportal/v1/orders/{order_key}/historyentries:
    get:
      tags:
        - webPortalOrder
      summary: Load all history entries of a specific order with pagination
      operationId: loadOrderHistoryEntries
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: order_key
          in: path
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the items by. Supported values are TODO
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on the order history entries. This can be any attribute of the order history entries.
          schema:
            type: string
      responses:
        '200':
          description: Page of history entries
          content:
            application/json:
              schema:
                $ref: './component/webportal/orderdetails/WebPortalOrderHistoryEntryPagedResponse.yaml'
        '400':
          description: Invalid input
        '404':
          description: Order not found
  /webportal/v1/orders/{order_key}/notes:
    get:
      tags:
        - webPortalOrder
      summary: Load all notes of a specific order with pagination
      operationId: loadOrderNotes
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: order_key
          in: path
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the items by. Supported values are TODO
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on the order history entries. This can be any attribute of the order history entries.
          schema:
            type: string
      responses:
        '200':
          description: Page of notes
          content:
            application/json:
              schema:
                $ref: './component/webportal/orderdetails/WebPortalOrderNotePagedResponse.yaml'
        '400':
          description: Invalid input
        '404':
          description: Order not found
    post:
      tags:
        - webPortalOrder
      summary: Creates a new note for a specific order
      operationId: addOrderNote
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: order_key
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/model/note/CreateNoteRequest.yaml'
      responses:
        '200':
          description: The note was successfully created
          content:
            application/json:
              schema:
                $ref: '././component/model/note/NoteResponse.yaml'
        '400':
          description: Invalid input
        '404':
          description: Order not found
  /webportal/v1/orders/{order_key}/notes/{note_key}:
    put:
      tags:
        - webPortalOrder
      summary: Update a note for a specific order
      operationId: updateOrderNote
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: order_key
          in: path
          required: true
          schema:
            type: string
        - name: note_key
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/model/note/UpdateNoteRequest.yaml'
      responses:
        '200':
          description: The note was successfully created
          content:
            application/json:
              schema:
                $ref: '././component/model/note/NoteResponse.yaml'
        '400':
          description: Invalid input
        '404':
          description: Order not found
    delete:
      tags:
        - webPortalOrder
      summary: Delete a note for a specific order
      operationId: deleteOrderNote
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: order_key
          in: path
          required: true
          schema:
            type: string
        - name: note_key
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: The note was successfully deleted
        '400':
          description: Invalid input
        '404':
          description: Order not found
  /webportal/v1/orders/{order_key}/postingrecords:
    get:
      tags:
        - webPortalOrder
      summary: Load all posting records of a specific order with pagination
      operationId: loadOrderPostingRecords
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: order_key
          in: path
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the items by. Supported values are TODO
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on the order items. This can be any attribute of the order items.
          schema:
            type: string
      responses:
        '200':
          description: Page of posting records
          content:
            application/json:
              schema:
                $ref: './component/webportal/orderdetails/WebPortalOrderPostingRecordsPagedResponse.yaml'
        '400':
          description: Invalid input
        '404':
          description: Order not found
  /webportal/v1/orders/{order_key}/balanceCaseItems:
    get:
      tags:
        - webPortalOrder
      summary: Load all balance case items of a specific order with pagination
      operationId: loadOrderBalanceCaseItems
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: order_key
          in: path
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the items by. Supported values are TODO
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on the order items. This can be any attribute of the order items.
          schema:
            type: string
      responses:
        '200':
          description: Page of posting records
          content:
            application/json:
              schema:
                $ref: './component/webportal/orderdetails/WebPortalOrderBalanceCaseItemsPagedResponse.yaml'
        '400':
          description: Invalid input
        '404':
          description: Order not found
  /webportal/v1/orders/{order_key}/documents:
    get:
      tags:
        - webPortalOrder
      summary: Load all documents of a specific order with pagination
      operationId: loadOrderDocuments
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: order_key
          in: path
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the items by. Supported values are TODO
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
      responses:
        '200':
          description: Page of documents
          content:
            application/json:
              schema:
                $ref: './component/webportal/documentlist/WebPortalDocumentsPagedResponse.yaml'
        '400':
          description: Invalid input
  /webportal/v1/orders/{order_key}/suppliers:
    get:
      tags:
        - webPortalOrder
      summary: Load all documents of a specific order with pagination
      operationId: loadOrderSuppliers
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: order_key
          in: path
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the items by. Supported values are TODO
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on the suppliers. This can be any attribute of the suppliers.
          schema:
            type: string
      responses:
        '200':
          description: Page of suppliers
          content:
            application/json:
              schema:
                $ref: './component/webportal/supplierlist/WebPortalSuppliersPagedResponse.yaml'
        '400':
          description: Invalid input
  /webportal/v1/orders/{order_key}/cancel:
    post:
      tags:
        - webPortalOrder
      summary: cancel order by Id
      operationId: cancelOrder
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: order_key
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Order was successfully canceled
          content:
            application/json:
              schema:
                $ref: './component/webportal/orderdetails/WebPortalOrderStatusResponse.yaml'
        '202':
          description: Request accepted
        '404':
          description: Order not found
  /webportal/v1/orders/{order_key}/finalize:
    post:
      summary: finalize an order
      operationId: finalizeOrder
      tags:
        - webPortalOrder
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: order_key
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Order was successfully finalized
          content:
            application/json:
              schema:
                $ref: './component/webportal/orderdetails/WebPortalOrderStatusResponse.yaml'
        '202':
          description: Request accepted
        '404':
          description: Order not found
  /webportal/v1/orders/{order_key}/isFinalizable:
    get:
      summary: Check if an order is finalizable
      operationId: isFinalizable
      tags:
        - webPortalOrder
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: order_key
          in: path
          description: The key of the order
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Order is finalizable
          content:
            application/json:
              schema:
                type: object
                properties:
                  isFinalizable:
                    type: boolean
                    description: Indicates if the order is finalizable
        '404':
          description: Order not found
  /webportal/v1/orders/{order_key}/isCancelable:
    get:
      summary: Check if an order is cancelable
      operationId: isCancelable
      tags:
        - webPortalOrder
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: order_key
          in: path
          description: The key of the order
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Order is cancelable
          content:
            application/json:
              schema:
                type: object
                properties:
                  isCancelable:
                    type: boolean
                    description: Indicates if the order is cancelable
        '404':
          description: Order not found
  /webportal/v1/orders/{order_key}/isUpdatable:
    get:
      summary: Check if an order is updatable
      operationId: isUpdatable
      tags:
        - webPortalOrder
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: order_key
          in: path
          description: The key of the order
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Order is updatable
          content:
            application/json:
              schema:
                type: object
                properties:
                  isUpdatable:
                    type: boolean
                    description: Indicates if the order is updatable
        '404':
          description: Order not found
  /webportal/v1/documents:
    get:
      tags:
        - webPortalDocument
      summary: Load all documents by creation date (descending)
      operationId: loadDocuments
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the items by. Supported values are TODO
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on the documents. This can be any attribute of the document.
          schema:
            type: string
      responses:
        '200':
          description: Page of documents
          content:
            application/json:
              schema:
                $ref: './component/webportal/documentlist/WebPortalDocumentsPagedResponse.yaml'
        '400':
          description: Invalid input
  /webportal/v1/documents/{key}:
    get:
      tags:
        - webPortalDocument
      summary: Load a specific document
      operationId: loadDocument
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: key
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Document
          content:
            application/json:
              schema:
                $ref: './component/webportal/documentdetails/WebPortalDocumentDetails.yaml'
        '400':
          description: Invalid input
  /webportal/v1/documents/{document_key}/items:
    get:
      tags:
        - webPortalDocument
      summary: Load paged items of a specific document with pagination
      operationId: loadDocumentItems
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: document_key
          in: path
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the items by.
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on the document items. This can be any attribute of the document items.
          schema:
            type: string
      responses:
        '200':
          description: Page of document items
          content:
            application/json:
              schema:
                $ref: './component/webportal/documentdetails/WebPortalDocumentItemsPagedResponse.yaml'
        '400':
          description: Invalid input
        '404':
          description: Order not found
  /webportal/v1/documents/keys:
    get:
      tags:
        - webPortalDocument
      summary: Load all document keys by key (ascending)
      operationId: loadDocumentKeys
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the documents by. Supported values is 'key',
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on the documents. This can be any attribute of the order.
          schema:
            type: string
      responses:
        '200':
          description: Page of document keys
          content:
            application/json:
              schema:
                $ref: './component/webportal/orderlist/WebPortalKeysPagedResponse.yaml'
        '400':
          description: Invalid input
  /webportal/v1/documents/{document_key}/file:
    get:
      summary: Get a specific document file
      operationId: loadDocumentFile
      tags:
        - webPortalDocument
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: order_key
          in: query
          description: The key of the order (required if document belongs to an order)
          required: false
          schema:
            type: string
        - name: subscription_key
          in: query
          description: The key of the subscription (required if document belongs to a subscription)
          required: false
          schema:
            type: string
        - name: document_key
          in: path
          description: The key of the document
          required: true
          schema:
            type: string
        - name: format
          in: query
          description: Format of the document
          schema:
            $ref: './component/model/DocumentFormat.yaml'
        - name: pdfCopy
          in: query
          description: Get only PDF copy of the document?
          schema:
            type: boolean
            default: false
      responses:
        '200':
          description: Successful operation
          content:
            application/pdf:
              schema:
                type: string
                format: byte
        '400':
          description: Either order_key or subscription_key must be provided
        '404':
          description: Order, subscription, document or file not found
  /webportal/v1/workflow:
    get:
      summary: Get a definition of the current workflow
      operationId: loadWorkflowDefinition
      tags:
        - webPortalWorkflow
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: workflowType
          in: query
          description: The type of workflow to load
          required: false
          schema:
            $ref: './component/model/WorkflowType.yaml'
      responses:
        '200':
          description: Successful operation
          content:
            application/xml:
              schema:
                type: string
  /webportal/v1/sent-archive-entries:
    get:
      tags:
        - webPortalSentArchive
      summary: Load all sent archive entries by creation date (descending)
      operationId: loadSentArchiveEntries
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the items by. Supported values are TODO
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on the sent archive entries. This can be any attribute of the sent archive entry.
          schema:
            type: string
      responses:
        '200':
          description: Page of sent archive entries
          content:
            application/json:
              schema:
                $ref: './component/webportal/sentarchivelist/WebPortalSentArchiveEntriesPagedResponse.yaml'
        '400':
          description: Invalid input
  /webportal/v1/sent-archive-entries/{key}:
    get:
      tags:
        - webPortalSentArchive
      summary: Load a specific sent archive entry
      operationId: loadSentArchiveEntry
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: key
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: './component/webportal/sentarchivedetails/WebPortalSentArchiveEntryDetails.yaml'
        '404':
          description: Sent Archive not found
  /webportal/v1/sent-archive-entries/{key}/resend:
    post:
      tags:
        - webPortalSentArchive
      summary: Resend a specific sent archive entry
      operationId: resendSentArchiveEntry
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: key
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Sent archive entry was successfully resent
        '400':
          description: Invalid input
  /webportal/v1/notifications:
    get:
      tags:
        - webPortalNotification
      summary: Load all notifications by creation date (descending)
      operationId: loadNotifications
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the items by. Supported values are TODO
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on the order items. This can be any attribute of the order items.
          schema:
            type: string
      responses:
        '200':
          description: Page of notifications
          content:
            application/json:
              schema:
                $ref: './component/webportal/notificationlist/WebPortalNotificationsPagedResponse.yaml'
        '400':
          description: Invalid input
  /webportal/v1/notifications/{key}/resend:
    post:
      tags:
        - webPortalNotification
      summary: Resend a specific notification
      operationId: resendNotification
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: key
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Notifications was successfully resent
        '400':
          description: Invalid input
  /webportal/v1/approvals:
    get:
      tags:
        - webPortalApproval
      summary: Load all approvals by creation date (descending)
      operationId: loadApprovals
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the items by. Supported values are TODO
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on the approvals. This can be any attribute of the approvals.
          schema:
            type: string
      responses:
        '200':
          description: Page of approvals
          content:
            application/json:
              schema:
                $ref: './component/webportal/approvallist/WebPortalApprovalsPagedResponse.yaml'
        '400':
          description: Invalid input
  /webportal/v1/approvals/count:
    get:
      tags:
        - webPortalApproval
      summary: Count open approvals
      operationId: countOpenApprovals
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Count of open approvals
          content:
            application/json:
              schema:
                type: integer
                format: int64
  /webportal/v1/approvals/{approval_key}:
    get:
      tags:
        - webPortalApproval
      summary: Load a specific approval
      operationId: loadApproval
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: approval_key
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: './component/webportal/approvaldetails/WebPortalApprovalDetails.yaml'
        '404':
          description: Approval not found
  /webportal/v1/approvals/{approval_key}/approve:
    post:
      tags:
        - webPortalApproval
      summary: Approve a specific approval
      operationId: approveApproval
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: approval_key
          in: path
          required: true
          schema:
            type: string
        - name: body
          in: query
          description: The body of the request
          required: true
          schema:
            type: object
            properties:
              comment:
                type: string
              version:
                type: integer
                format: int64
            required:
              - version
      responses:
        '200':
          description: Successful operation
        '404':
          description: Approval not found
  /webportal/v1/approvals/{approval_key}/reject:
    post:
      tags:
        - webPortalApproval
      summary: Reject a specific approval
      operationId: rejectApproval
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: approval_key
          in: path
          required: true
          schema:
            type: string
        - name: body
          in: query
          description: The body of the request
          required: true
          schema:
            type: object
            properties:
              comment:
                type: string
              version:
                type: integer
                format: int64
            required:
              - version
      responses:
        '200':
          description: Successful operation
        '404':
          description: Approval not found
  /webportal/v1/predefined-items:
    get:
      tags:
        - webPortalPredefinedItem
      summary: Load all predefined items by creation date (descending)
      operationId: loadPredefinedItems
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the items by. Supported values are TODO
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on the predefined items. This can be any attribute of the predefined items.
          schema:
            type: string
      responses:
        '200':
          description: Page of predefined items
          content:
            application/json:
              schema:
                $ref: './component/webportal/predefineditemlist/WebPortalPredefinedItemsPagedResponse.yaml'
        '400':
          description: Invalid input
  /webportal/v1/reports:
    get:
      tags:
        - webPortalReport
      summary: Load all reports by creation date (descending)
      operationId: loadReports
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the items by. Supported values are TODO
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on the reports. This can be any attribute of the reports.
          schema:
            type: string
      responses:
        '200':
          description: Page of reports
          content:
            application/json:
              schema:
                $ref: './component/webportal/reportlist/WebPortalReportsPagedResponse.yaml'
        '400':
          description: Invalid input
  /webportal/v1/reports/{report_key}:
    get:
      summary: Get a specific report file
      operationId: loadReportFile
      tags:
        - webPortalReport
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: report_key
          in: path
          description: The key of the report
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Report file
          content:
            application/*:
              schema:
                type: string
                format: binary
        '404':
          description: Order, document or file not found
  /webportal/v1/notification-receivers:
    get:
      tags:
        - webPortalNotification
      summary: Load all notification receivers by creation date (descending)
      operationId: loadNotificationReceivers
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the items by. Supported values are TODO
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on the notification receivers. This can be any attribute of the notifications receivers.
          schema:
            type: string
      responses:
        '200':
          description: Page of notification receivers
          content:
            application/json:
              schema:
                $ref: './component/webportal/notificationreceiverlist/WebPortalNotificationReceiversPagedResponse.yaml'
        '400':
          description: Invalid input
    post:
      tags:
        - webPortalNotification
      summary: Create a new notification receiver
      operationId: createNotificationReceiver
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/webportal/notificationreceiverdetails/WebPortalNotificationReceiverDetails.yaml'
      responses:
        '201':
          description: The notification receiver was successfully created
          content:
            application/json:
              schema:
                $ref: './component/webportal/notificationreceiverdetails/WebPortalNotificationReceiverDetails.yaml'
        '400':
          description: Invalid input
  /webportal/v1/notification-receivers/{key}:
    get:
      tags:
        - webPortalNotification
      summary: Load a specific notification receiver
      operationId: loadNotificationReceiver
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: key
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: './component/webportal/notificationreceiverdetails/WebPortalNotificationReceiverDetails.yaml'
        '404':
          description: Notification receiver not found
    put:
      tags:
        - webPortalNotification
      summary: Update a specific notification receiver
      operationId: updateNotificationReceiver
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: key
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/webportal/notificationreceiverdetails/WebPortalNotificationReceiverDetails.yaml'
      responses:
        '200':
          description: The notification receiver was successfully updated
          content:
            application/json:
              schema:
                $ref: './component/webportal/notificationreceiverdetails/WebPortalNotificationReceiverDetails.yaml'
        '400':
          description: Invalid input
        '404':
          description: Notification receiver not found
  /webportal/v1/report-configurations:
    get:
      tags:
        - webPortalReport
      summary: Load all report configurations by creation date (descending)
      operationId: loadReportConfigurations
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the items by. Supported values are TODO
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on the report configurations. This can be any attribute of the report configurations.
          schema:
            type: string
      responses:
        '200':
          description: Page of report configurations
          content:
            application/json:
              schema:
                $ref: './component/webportal/reportconfigurationlist/WebPortalReportConfigurationsPagedResponse.yaml'
        '400':
          description: Invalid input
    post:
      tags:
        - webPortalReport
      summary: Create a new report configuration
      operationId: createReportConfiguration
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/webportal/reportconfigurationdetails/WebPortalReportConfigurationDetails.yaml'
      responses:
        '201':
          description: The report configuration was successfully created
          content:
            application/json:
              schema:
                $ref: './component/webportal/reportconfigurationdetails/WebPortalReportConfigurationDetails.yaml'
        '400':
          description: Invalid input
  /webportal/v1/report-configurations/{key}:
    get:
      tags:
        - webPortalReport
      summary: Load a specific report configuration
      operationId: loadReportConfiguration
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: key
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: './component/webportal/reportconfigurationdetails/WebPortalReportConfigurationDetails.yaml'
        '404':
          description: Report configuration not found
    put:
      tags:
        - webPortalReport
      summary: Update a specific report configuration
      operationId: updateReportConfiguration
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: key
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/webportal/reportconfigurationdetails/WebPortalReportConfigurationDetails.yaml'
      responses:
        '200':
          description: The report configuration was successfully updated
          content:
            application/json:
              schema:
                $ref: './component/webportal/reportconfigurationdetails/WebPortalReportConfigurationDetails.yaml'
        '400':
          description: Invalid input
        '404':
          description: Report configuration not found
  /webportal/v1/users:
    get:
      tags:
        - webPortalUser
      summary: Load all users by creation date (descending)
      operationId: loadUsers
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the items by. Supported values are TODO
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on the users. This can be any attribute of the user.
          schema:
            type: string
      responses:
        '200':
          description: Page of users
          content:
            application/json:
              schema:
                $ref: './component/webportal/userlist/WebPortalUsersPagedResponse.yaml'
        '400':
          description: Invalid input
    post:
      tags:
        - webPortalUser
      summary: Create a new user
      operationId: createUser
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/webportal/userdetails/WebPortalUserDetails.yaml'
      responses:
        '201':
          description: The user was successfully created
          content:
            application/json:
              schema:
                $ref: './component/webportal/userdetails/WebPortalUserDetails.yaml'
        '400':
          description: Invalid input
  /webportal/v1/users/{key}:
    get:
      tags:
        - webPortalUser
      summary: Load a specific user
      operationId: loadUser
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: key
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: './component/webportal/userdetails/WebPortalUserDetails.yaml'
        '404':
          description: User not found
    put:
      tags:
        - webPortalUser
      summary: Update a specific user
      operationId: updateUser
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: key
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/webportal/userdetails/WebPortalUserDetails.yaml'
      responses:
        '200':
          description: The user was successfully updated
          content:
            application/json:
              schema:
                $ref: './component/webportal/userdetails/WebPortalUserDetails.yaml'
        '400':
          description: Invalid input
        '404':
          description: User not found
  /webportal/v1/roles:
    get:
      tags:
        - webPortalRole
      summary: Load all roles by creation date (descending)
      operationId: loadRoles
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the items by. Supported values are TODO
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on the roles. This can be any attribute of the role.
          schema:
            type: string
      responses:
        '200':
          description: Page of roles
          content:
            application/json:
              schema:
                $ref: './component/webportal/rolelist/WebPortalRolesPagedResponse.yaml'
        '400':
          description: Invalid input
    post:
      tags:
        - webPortalRole
      summary: Create a new role
      operationId: createRole
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/webportal/roledetails/WebPortalRoleDetails.yaml'
      responses:
        '201':
          description: The role was successfully created
          content:
            application/json:
              schema:
                $ref: './component/webportal/roledetails/WebPortalRoleDetails.yaml'
        '400':
          description: Invalid input
  /webportal/v1/roles/{key}:
    get:
      tags:
        - webPortalRole
      summary: Load a specific role
      operationId: loadRole
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: key
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: './component/webportal/roledetails/WebPortalRoleDetails.yaml'
        '404':
          description: Role not found
    put:
      tags:
        - webPortalRole
      summary: Update a specific role
      operationId: updateRole
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: key
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/webportal/roledetails/WebPortalRoleDetails.yaml'
      responses:
        '200':
          description: The role was successfully updated
          content:
            application/json:
              schema:
                $ref: './component/webportal/roledetails/WebPortalRoleDetails.yaml'
        '400':
          description: Invalid input
        '404':
          description: Role not found
  /webportal/v1/user/current:
    get:
      tags:
        - webPortalUser
      operationId: loadCurrentUser
      summary: Get the current user details
      parameters:
        - name: x-tenant-key
          in: query
          description: The key of the tenant
          required: false
          schema:
            type: string
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: './component/webportal/userdetails/WebPortalCurrentUserResponse.yaml'
        '404':
          description: User not found
  /webportal/v1/user/current/last-tenant:
    post:
      tags:
        - webPortalUser
      operationId: saveLastTenant
      summary: Save the last tenant of the current user
      parameters:
        - name: last-used-tenant
          in: query
          description: The key of the last tenant
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful operation
        '404':
          description: User not found
  /webportal/v1/user/profile:
    get:
      tags:
        - webPortalUser
      operationId: loadUserProfile
      summary: Get the current user profile details
      parameters:
        - name: x-tenant-key
          in: query
          description: The key of the tenant
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: './component/webportal/userdetails/WebPortalUserProfile.yaml'
        '404':
          description: User not found
    post:
      tags:
        - webPortalUser
      operationId: updateUserProfile
      summary: Update the user profile
      parameters:
        - name: x-tenant-key
          in: query
          description: The key of the tenant
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/webportal/userdetails/WebPortalUserProfile.yaml'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: './component/webportal/userdetails/WebPortalUserProfile.yaml'
        '400':
          description: Invalid input
  /webportal/v1/balance-cases:
    get:
      tags:
        - webPortalBalanceCase
      summary: Load all balance cases by creation date (descending)
      operationId: loadBalanceCases
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the items by. Supported values are TODO
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply. This can be any attribute of the object.
          schema:
            type: string
      responses:
        '200':
          description: Paged response
          content:
            application/json:
              schema:
                $ref: './component/webportal/balancecaselist/WebPortalBalanceCasesPagedResponse.yaml'
        '400':
          description: Invalid input
  /webportal/v1/balance-cases/count-open:
    get:
      tags:
        - webPortalBalanceCase
      summary: Count open items
      operationId: countOpenItems
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Count of open balance case item
          content:
            application/json:
              schema:
                type: integer
                format: int64
  /webportal/v1/balance-cases/{key}:
    get:
      tags:
        - webPortalBalanceCase
      summary: Load details of balance case
      operationId: loadBalanceCase
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: key
          in: path
          required: true
          description: The key of the balance case
          schema:
            type: string
      responses:
        '200':
          description: Balance case details
          content:
            application/json:
              schema:
                $ref: './component/webportal/balancecasedetails/WebPortalBalanceCaseDetailsResponse.yaml'
        '400':
          description: Invalid input
        '404':
          description: Balance case not found
  /webportal/v1/balance-cases/{key}/balance-case-items:
    get:
      tags:
        - webPortalBalanceCase
      summary: Load all balance case items by creation date (descending)
      operationId: loadBalanceCaseItems
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the items by. Supported values are TODO
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply. This can be any attribute of the object.
          schema:
            type: string
        - name: key
          in: path
          required: true
          description: The key of the balance case
          schema:
            type: string
      responses:
        '200':
          description: Paged response
          content:
            application/json:
              schema:
                $ref: './component/webportal/balancecasedetails/WebPortalBalanceCaseItemsPagedResponse.yaml'
        '400':
          description: Invalid input
        '404':
          description: Balance case not found
  /webportal/v1/settlement-reports:
    get:
      tags:
        - webPortalSettlementReport
      summary: Load all settlement reports by creation date (descending)
      operationId: loadSettlementReports
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the items by. Supported values are TODO
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply. This can be any attribute of the object.
          schema:
            type: string
      responses:
        '200':
          description: Paged response
          content:
            application/json:
              schema:
                $ref: './component/webportal/settlementreportlist/WebPortalSettlementReportsPagedResponse.yaml'
        '400':
          description: Invalid input
  /webportal/v1/settlement-reports/{key}:
    get:
      tags:
        - webPortalSettlementReport
      summary: Load details of settlement report
      operationId: loadSettlementReport
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: key
          in: path
          required: true
          description: The key of the settlement report
          schema:
            type: string
      responses:
        '200':
          description: Settlement report details
          content:
            application/json:
              schema:
                $ref: './component/webportal/settlementreportdetails/WebPortalSettlementReportDetailsResponse.yaml'
        '400':
          description: Invalid input
        '404':
          description: Settlement report not found
  /webportal/v1/settlement-reports/{key}/settlement-report-items:
    get:
      tags:
        - webPortalSettlementReport
      summary: Load all settlement report items by creation date (descending)
      operationId: loadSettlementReportItems
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the items by. Supported values are TODO
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply. This can be any attribute of the object.
          schema:
            type: string
        - name: key
          in: path
          required: true
          description: The key of the settlement report
          schema:
            type: string
      responses:
        '200':
          description: Paged response
          content:
            application/json:
              schema:
                $ref: './component/webportal/settlementreportdetails/WebPortalSettlementReportItemsPagedResponse.yaml'
        '400':
          description: Invalid input
        '404':
          description: Settlement report not found
  /webportal/v1/settlement-report-files:
    post:
      tags:
        - webPortalSettlementReport
      summary: "Upload a settlement report file (CAMT.053 / MT940)"
      operationId: uploadSettlementReportFile
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                report:
                  type: string
                  format: binary
              required:
                - report
      responses:
        '200':
          description: The settlement report file was processed successfully
        '400':
          description: Invalid input
  /webportal/v1/payment-accounts:
    get:
      tags:
        - webPortalPaymentAccount
      summary: Load all payment accounts by account id (asc)
      operationId: loadPaymentAccounts
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the items by. Supported values are TODO
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on. This can be any attribute of the object.
          schema:
            type: string
      responses:
        '200':
          description: Page of payment accounts
          content:
            application/json:
              schema:
                $ref: './component/webportal/paymentaccountlist/WebPortalPaymentAccountsPagedResponse.yaml'
        '400':
          description: Invalid input
    post:
      tags:
        - webPortalPaymentAccount
      summary: Create a new payment account
      operationId: createPaymentAccount
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/webportal/paymentaccountdetails/WebPortalPaymentAccountDetails.yaml'
      responses:
        '201':
          description: The payment account was successfully created
          content:
            application/json:
              schema:
                $ref: './component/webportal/paymentaccountdetails/WebPortalPaymentAccountDetails.yaml'
        '400':
          description: Invalid input
  /webportal/v1/payment-accounts/{key}:
    get:
      tags:
        - webPortalPaymentAccount
      summary: Load details of payment account
      operationId: loadPaymentAccount
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: key
          in: path
          required: true
          description: The key of the payment account
          schema:
            type: string
      responses:
        '200':
          description: Details of the payment account
          content:
            application/json:
              schema:
                $ref: './component/webportal/paymentaccountdetails/WebPortalPaymentAccountDetails.yaml'
        '400':
          description: Invalid input
    put:
      tags:
        - webPortalPaymentAccount
      summary: Update a specific payment account
      operationId: updatePaymentAccount
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: key
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/webportal/paymentaccountdetails/WebPortalPaymentAccountDetails.yaml'
      responses:
        '200':
          description: The payment account was successfully updated
          content:
            application/json:
              schema:
                $ref: './component/webportal/paymentaccountdetails/WebPortalPaymentAccountDetails.yaml'
        '400':
          description: Invalid input
        '404':
          description: Payment account not found
  /webportal/v1/payment-categorization-rules:
    get:
      tags:
        - webPortalPaymentCategorizationRule
      summary: Load all payment categorization rules by name (asc)
      operationId: loadPaymentCategorizationRules
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the items by. Supported values are TODO
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on. This can be any attribute of the object.
          schema:
            type: string
      responses:
        '200':
          description: Page of payment categorization rules
          content:
            application/json:
              schema:
                $ref: './component/webportal/paymentcategorizationrulelist/WebPortalPaymentCategorizationRulesPagedResponse.yaml'
        '400':
          description: Invalid input
    post:
      tags:
        - webPortalPaymentCategorizationRule
      summary: Create a new payment categorization rule
      operationId: createPaymentCategorizationRule
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/webportal/paymentcategorizationruledetails/WebPortalPaymentCategorizationRuleDetails.yaml'
      responses:
        '201':
          description: The payment categorization rule was successfully created
          content:
            application/json:
              schema:
                $ref: './component/webportal/paymentcategorizationruledetails/WebPortalPaymentCategorizationRuleDetails.yaml'
        '400':
          description: Invalid input
  /webportal/v1/payment-categorization-rules/{key}:
    get:
      tags:
        - webPortalPaymentCategorizationRule
      summary: load payment categorization rule by key
      operationId: loadPaymentCategorizationRule
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: key
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: './component/webportal/paymentcategorizationruledetails/WebPortalPaymentCategorizationRuleDetails.yaml'
        '404':
          description: Booking rule not found
    put:
      tags:
        - webPortalPaymentCategorizationRule
      summary: Update a specific payment categorization rule
      operationId: updatePaymentCategorizationRule
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: key
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/webportal/paymentcategorizationruledetails/WebPortalPaymentCategorizationRuleDetails.yaml'
      responses:
        '200':
          description: The payment categorization rule was successfully updated
          content:
            application/json:
              schema:
                $ref: './component/webportal/paymentcategorizationruledetails/WebPortalPaymentCategorizationRuleDetails.yaml'
        '400':
          description: Invalid input
        '404':
          description: Payment categorization rule not found
  /webportal/v1/notes:
    get:
      tags:
        - webPortalNotes
      summary: Load all notes by creation date (descending)
      operationId: loadNotes
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the items by. Supported values are TODO
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on. This can be any attribute of the object.
          schema:
            type: string
      responses:
        '200':
          description: Page of notes
          content:
            application/json:
              schema:
                $ref: './component/webportal/notelist/WebPortalNotesPagedResponse.yaml'
        '400':
          description: Invalid input
  /webportal/v1/translations:
    get:
      tags:
        - webPortalTranslation
      summary: Load all translations by creation date (descending)
      operationId: loadTranslations
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the items by. Supported values are TODO
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on. This can be any attribute of the object.
          schema:
            type: string
      responses:
        '200':
          description: Page of translations
          content:
            application/json:
              schema:
                $ref: './component/webportal/translationlist/WebPortalTranslationsPagedResponse.yaml'
        '400':
          description: Invalid input
    post:
      tags:
        - webPortalTranslation
      summary: Create a new translation
      operationId: createTranslation
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/webportal/translationdetails/WebPortalTranslationDetails.yaml'
      responses:
        '201':
          description: The translation was successfully created
          content:
            application/json:
              schema:
                $ref: './component/webportal/translationdetails/WebPortalTranslationDetails.yaml'
        '400':
          description: Invalid input
  /webportal/v1/translations/{key}:
    get:
      tags:
        - webPortalTranslation
      summary: load translation by key
      operationId: loadTranslation
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: key
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: './component/webportal/translationdetails/WebPortalTranslationDetails.yaml'
        '404':
          description: Translation not found
    put:
      tags:
        - webPortalTranslation
      summary: Update a specific translation
      operationId: updateTranslation
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: key
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/webportal/translationdetails/WebPortalTranslationDetails.yaml'
      responses:
        '200':
          description: The translation was successfully updated
          content:
            application/json:
              schema:
                $ref: './component/webportal/translationdetails/WebPortalTranslationDetails.yaml'
        '400':
          description: Invalid input
        '404':
          description: Translation not found
  /webportal/v1/translations-keys:
    get:
      tags:
        - webPortalTranslation
      summary: Load all translation keys for tenant
      operationId: loadTranslationKeys
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: type
          in: query
          description: The type of the key
          required: true
          schema:
            $ref: './component/model/TranslationType.yaml'
      responses:
        '200':
          description: All translation keys
          content:
            application/json:
              schema:
                $ref: './component/webportal/translationkeylist/WebPortalTranslationKeysResponse.yaml'
        '400':
          description: Invalid input
  /webportal/v1/failed-jobs:
    get:
      tags:
        - webPortalInternal
      summary: Load all failed jobs by creation date (desc)
      operationId: loadFailedJobs
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the items by. Supported values are TODO
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on. This can be any attribute of the object.
          schema:
            type: string
      responses:
        '200':
          description: Page of failed jobs
          content:
            application/json:
              schema:
                $ref: './component/webportal/failedjoblist/WebPortalFailedJobsPagedResponse.yaml'
        '400':
          description: Invalid input
  /webportal/v1/failed-jobs/{key}/retry:
    post:
      tags:
        - webPortalInternal
      summary: Retry a failed job
      operationId: retryFailedJob
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: key
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Job was successfully retried
        '400':
          description: Invalid input
  /webportal/v1/history-activities:
    get:
      tags:
        - webPortalInternal
      summary: Load all historic activities by start date (desc)
      operationId: loadHistoricActivities
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the items by. Supported values are TODO
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on. This can be any attribute of the object.
          schema:
            type: string
      responses:
        '200':
          description: Page of historic activities
          content:
            application/json:
              schema:
                $ref: './component/webportal/historicactivitylist/WebPortalHistoricActivitiesPagedResponse.yaml'
        '400':
          description: Invalid input
  /webportal/v1/subscriptions:
    get:
      tags:
        - webPortalSubscription
      summary: Load all subscriptions
      operationId: loadSubscriptions
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the subscriptions by. Supported values are 'createdAt', 'name', 'customerKey'
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on the subscriptions. This can be any attribute of the subscription.
          schema:
            type: string
      responses:
        '200':
          description: Page of subscriptions
          content:
            application/json:
              schema:
                $ref: './component/webportal/subscriptionlist/WebPortalSubscriptionsPagedResponse.yaml'
        '400':
          description: Invalid input
    post:
      tags:
        - webPortalSubscription
      summary: Create a new subscription
      operationId: createSubscription
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/webportal/subscriptiondetails/WebPortalSubscriptionDetailsData.yaml'
      responses:
        '201':
          description: The subscription was successfully created
          content:
            application/json:
              schema:
                $ref: './component/webportal/subscriptiondetails/WebPortalSubscriptionDetailsResponse.yaml'
        '400':
          description: Invalid input
  /webportal/v1/subscriptions/{subscription_key}:
    get:
      tags:
        - webPortalSubscription
      operationId: loadSubscription
      summary: Get a specific subscription
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: subscription_key
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: './component/webportal/subscriptiondetails/WebPortalSubscriptionDetailsResponse.yaml'
        '404':
          description: Subscription not found
    patch:
      tags:
        - webPortalSubscription
      summary: Update a specific subscription
      operationId: updateSubscription
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: subscription_key
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/webportal/subscriptiondetails/WebPortalSubscriptionDetailsData.yaml'
      responses:
        '200':
          description: The subscription was successfully updated
          content:
            application/json:
              schema:
                $ref: './component/webportal/subscriptiondetails/WebPortalSubscriptionDetailsResponse.yaml'
        '400':
          description: Invalid input
        '404':
          description: Subscription not found
    delete:
      tags:
        - webPortalSubscription
      summary: Delete a specific subscription
      operationId: deleteSubscription
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: subscription_key
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: Subscription deleted successfully
        '404':
          description: Subscription not found
  /webportal/v1/subscriptions/{subscription_key}/items:
    get:
      tags:
        - webPortalSubscription
      summary: Load paged items of a specific subscription with pagination
      operationId: loadSubscriptionItems
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: subscription_key
          in: path
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the items by.
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on the subscription items. This can be any attribute of the subscription items.
          schema:
            type: string
      responses:
        '200':
          description: Page of subscription items
          content:
            application/json:
              schema:
                $ref: './component/webportal/subscriptiondetails/WebPortalSubscriptionItemsPagedResponse.yaml'
        '400':
          description: Invalid input
        '404':
          description: Subscription not found
  /webportal/v1/subscriptions/{subscription_key}/history-entries:
    get:
      tags:
        - webPortalSubscription
      summary: Load paged history entries of a specific subscription with pagination
      operationId: loadSubscriptionHistoryEntries
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: subscription_key
          in: path
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the history entries by.
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on the history entries. This can be any attribute of the history entry.
          schema:
            type: string
      responses:
        '200':
          description: Page of subscription history entries
          content:
            application/json:
              schema:
                $ref: './component/webportal/subscriptionhistory/WebPortalSubscriptionHistoryEntryPagedResponse.yaml'
        '400':
          description: Invalid input
        '404':
          description: Subscription not found
  /webportal/v1/subscriptions/{subscription_key}/balance-cases:
    get:
      tags:
        - webPortalSubscription
      summary: Load paged balance cases of a specific subscription with pagination
      operationId: loadSubscriptionBalanceCases
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: subscription_key
          in: path
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the balance cases by.
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on the balance cases. This can be any attribute of the balance case.
          schema:
            type: string
      responses:
        '200':
          description: Page of subscription balance cases
          content:
            application/json:
              schema:
                $ref: './component/webportal/balancecaselist/WebPortalBalanceCasesPagedResponse.yaml'
        '400':
          description: Invalid input
        '404':
          description: Subscription not found
  /webportal/v1/subscriptions/{subscription_key}/cancel:
    post:
      tags:
        - webPortalSubscription
      summary: Cancel subscription by key
      operationId: cancelSubscription
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: subscription_key
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Subscription was successfully cancelled
          content:
            application/json:
              schema:
                $ref: './component/webportal/subscriptiondetails/WebPortalSubscriptionStatusResponse.yaml'
        '202':
          description: Request accepted
        '404':
          description: Subscription not found
  /webportal/v1/subscriptions/{subscription_key}/activate:
    post:
      tags:
        - webPortalSubscription
      summary: Activate subscription by key
      operationId: activateSubscription
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: subscription_key
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Subscription was successfully activated
          content:
            application/json:
              schema:
                $ref: './component/webportal/subscriptiondetails/WebPortalSubscriptionStatusResponse.yaml'
        '202':
          description: Request accepted
        '404':
          description: Subscription not found
  /webportal/v1/subscriptions/{subscription_key}/pause:
    post:
      tags:
        - webPortalSubscription
      summary: Pause subscription by key
      operationId: pauseSubscription
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: subscription_key
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Subscription was successfully paused
          content:
            application/json:
              schema:
                $ref: './component/webportal/subscriptiondetails/WebPortalSubscriptionStatusResponse.yaml'
        '202':
          description: Request accepted
        '404':
          description: Subscription not found
  /webportal/v1/subscriptions/{subscription_key}/isCancelable:
    get:
      summary: Check if a subscription is cancelable
      operationId: isSubscriptionCancelable
      tags:
        - webPortalSubscription
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: subscription_key
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Subscription is cancelable
          content:
            application/json:
              schema:
                type: object
                properties:
                  isCancelable:
                    type: boolean
                    description: Indicates if the subscription is cancelable
        '404':
          description: Subscription not found
  /webportal/v1/subscriptions/{subscription_key}/isActivatable:
    get:
      summary: Check if a subscription is activatable
      operationId: isSubscriptionActivatable
      tags:
        - webPortalSubscription
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: subscription_key
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Subscription is activatable
          content:
            application/json:
              schema:
                type: object
                properties:
                  isActivatable:
                    type: boolean
                    description: Indicates if the subscription is activatable
        '404':
          description: Subscription not found
  /webportal/v1/subscriptions/{subscription_key}/isPausable:
    get:
      summary: Check if a subscription is pausable
      operationId: isSubscriptionPausable
      tags:
        - webPortalSubscription
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: subscription_key
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Subscription is pausable
          content:
            application/json:
              schema:
                type: object
                properties:
                  isPausable:
                    type: boolean
                    description: Indicates if the subscription is pausable
        '404':
          description: Subscription not found
  /webportal/v1/subscriptions/{subscription_key}/notes:
    post:
      tags:
        - webPortalSubscription
      summary: Add a note to a subscription
      operationId: addSubscriptionNote
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: subscription_key
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/model/note/CreateNoteRequest.yaml'
      responses:
        '201':
          description: Note created successfully
          content:
            application/json:
              schema:
                $ref: './component/webportal/notelist/WebPortalNotesListEntry.yaml'
        '400':
          description: Invalid input
        '404':
          description: Subscription not found
  /webportal/v1/subscriptions/{subscription_key}/notes/{note_key}:
    put:
      tags:
        - webPortalSubscription
      summary: Update a subscription note
      operationId: updateSubscriptionNote
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: subscription_key
          in: path
          required: true
          schema:
            type: string
        - name: note_key
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/model/note/UpdateNoteRequest.yaml'
      responses:
        '200':
          description: Note updated successfully
          content:
            application/json:
              schema:
                $ref: './component/webportal/notelist/WebPortalNotesListEntry.yaml'
        '400':
          description: Invalid input
        '404':
          description: Subscription or note not found
    delete:
      tags:
        - webPortalSubscription
      summary: Delete a subscription note
      operationId: deleteSubscriptionNote
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: subscription_key
          in: path
          required: true
          schema:
            type: string
        - name: note_key
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: Note deleted successfully
        '404':
          description: Subscription or note not found
