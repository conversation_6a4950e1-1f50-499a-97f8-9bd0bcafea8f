type: object
properties:
  key:
    type: string
  orderKey:
    type: string
  subscriptionKey:
    type: string
  createdAt:
    type: string
    format: date-time
  documentDate:
    type: string
    format: date
  documentType:
    $ref: '../../model/DocumentType.yaml'
  paymentStatus:
    $ref: '../../model/PaymentStatus.yaml'

required:
  - key
  - createdAt
  - documentDate
  - documentType
  - paymentStatus
