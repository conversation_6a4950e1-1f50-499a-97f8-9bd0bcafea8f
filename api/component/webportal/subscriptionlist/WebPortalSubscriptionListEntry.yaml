type: object
properties:
  key:
    type: string
    description: The unique key of the subscription
  name:
    type: string
    description: The name of the subscription
  customerKey:
    type: string
    description: The key of the customer who owns this subscription
  customerName:
    type: string
    description: The name of the customer
  status:
    $ref: '../../model/SubscriptionStatus.yaml'
  frequency:
    $ref: '../../model/SubscriptionFrequency.yaml'
  amount:
    type: number
    format: decimal
    description: The recurring amount for the subscription
  currency:
    $ref: '../../model/Currency.yaml'
  nextBillingDate:
    type: string
    format: date
    description: The next billing date for the subscription
  createdAt:
    type: string
    format: date-time
    description: When the subscription was created
required:
  - key
  - name
  - customerKey
  - status
  - frequency
  - amount
  - currency
  - nextBillingDate
