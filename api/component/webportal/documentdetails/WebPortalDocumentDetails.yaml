type: object
properties:
  key:
    type: string
  orderKey:
    type: string
  subscriptionKey:
    type: string
  createdAt:
    type: string
    format: date-time
  documentDate:
    type: string
    format: date
  documentType:
    $ref: '../../model/DocumentType.yaml'
  paymentStatus:
    $ref: '../../model/PaymentStatus.yaml'
  sumAggregations:
    type: array
    items:
      $ref: '../../model/SumAggregation.yaml'

required:
  - key
  - createdAt
  - documentDate
  - documentType
  - paymentStatus
  - sumAggregations
