type: object
properties:
  key:
    type: string
    description: The unique key of the subscription
  name:
    type: string
    description: The name of the subscription
  description:
    type: string
    description: The description of the subscription
  customerKey:
    type: string
    description: The key of the customer who owns this subscription
  status:
    $ref: '../../model/SubscriptionStatus.yaml'
  frequency:
    $ref: '../../model/SubscriptionFrequency.yaml'
  amount:
    type: number
    format: decimal
    description: The recurring amount for the subscription
  currency:
    $ref: '../../model/Currency.yaml'
  startDate:
    type: string
    format: date
    description: The start date of the subscription
  endDate:
    type: string
    format: date
    description: The end date of the subscription (optional)
  nextBillingDate:
    type: string
    format: date
    description: The next billing date for the subscription
  businessSegmentKey:
    type: string
    description: The key of the business segment for this subscription
  properties:
    type: array
    items:
      $ref: '../../model/Property.yaml'
  version:
    type: integer
    format: int64
    description: The version of the subscription for optimistic locking
required:
  - key
  - name
  - customerKey
  - status
  - frequency
  - amount
  - currency
  - startDate
  - nextBillingDate
  - businessSegmentKey
