type: object
properties:
  key:
    type: string
  orderKey:
    type: string
    nullable: true
  subscriptionKey:
    type: string
    nullable: true
  createdAt:
    type: string
    format: date-time
  status:
    $ref: '../../model/BalanceCaseStatus.yaml'
  currency:
    $ref: '../../model/Currency.yaml'
  totalDebitAmount:
    type: number
  totalCreditAmount:
    type: number
  totalBalanceAmount:
    type: number

required:
  - key
  - createdAt
  - status
  - currency
  - totalDebitAmount
  - totalCreditAmount
  - totalBalanceAmount
