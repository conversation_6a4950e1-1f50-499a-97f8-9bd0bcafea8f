type: object
properties:
  key:
    type: string
  bookingDate:
    type: string
    format: date
    description: The date of the booking
  debitCreditIndicator:
    $ref: '../../../model/DebitCreditIndicator.yaml'
  assignmentStatus:
    $ref: '../../../model/PaymentTransactionAssignmentStatus.yaml'
  text:
    type: string
    description: The text of the payment transaction
  amount:
    type: number
    description: The amount of the payment transaction
  currency:
    $ref: '../../../model/Currency.yaml'
  paymentCategory:
    $ref: '../../../model/PaymentCategory.yaml'

required:
  - key
  - bookingDate
  - debitCreditIndicator
  - assignmentStatus
  - text
  - amount
  - currency
  - paymentCategory
