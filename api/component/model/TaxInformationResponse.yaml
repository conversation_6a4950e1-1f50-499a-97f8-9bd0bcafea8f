title: Tax Information
required:
  - taxType
  - taxCode
  - taxRate
  - currency
type: object
properties:
  taxType:
    type: string
    enum:
      - VAT
    description: The type of tax (e.g. VAT)
  taxCode:
    type: string
    default: EMPTY
    minLength: 1
    maxLength: 16
    description: The tax code (e.g. D7, A9)
  taxRate:
    type: number
    example: 20.00
    minimum: 0.00
    maximum: 100.00
  totalAmount:
    type: number
    example: 20.00
    minimum: 0.00
    maximum: 100.00
  currency:
    $ref: './Currency.yaml'
description: Tax information
