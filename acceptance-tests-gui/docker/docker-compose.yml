services:
  gui:
    build:
      context: ../../app-webportal/.
      dockerfile: Dockerfile
    image: 234542553877.dkr.ecr.eu-central-1.amazonaws.com/gui:latest
    ports:
      - "8080:80"
    depends_on:
      - frontend
    networks:
      - billing-network
  greenmail:
    image: greenmail/standalone:2.0.1
    ports:
      - "3025:3025"
      - "3143:3143"
    environment:
      # LocalStack configuration: https://docs.localstack.cloud/references/configuration/
      - GREENMAIL_OPTS=-Dgreenmail.setup.test.smtp -Dgreenmail.setup.test.imap -Dgreenmail.hostname=0.0.0.0 -Dgreenmail.users=o2c:o2c,cucumber:<EMAIL>
    networks:
      - billing-network
  localstack:
    image: localstack/localstack:3.4
    environment:
      # LocalStack configuration: https://docs.localstack.cloud/references/configuration/
      - DEBUG=${DEBUG:-0}
      - SERVICES=${SERVICES:-s3}
      - AWS_DEFAULT_REGION=eu-central-1
    volumes:
      - "/var/run/docker.sock:/var/run/docker.sock"
      - "./config:/etc/localstack/init/ready.d"
    networks:
      - billing-network
  postgres:
    image: postgres:16-alpine
    environment:
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=pass
      - POSTGRES_DB=billingsolution
    networks:
      - billing-network
  frontend:
    image: 234542553877.dkr.ecr.eu-central-1.amazonaws.com/frontend:latest
    extra_hosts:
      - "host.docker.internal:host-gateway"
    ports:
      - "8081:8080"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health/readiness"]
      interval: 1s
      timeout: 1s
      retries: 45
    environment:
      - spring.profiles.active=cucumber
      - spring.datasource.url=******************************************
      - spring.datasource.username=user
      - spring.datasource.password=pass
      - spring.r2dbc.url=r2dbc:postgresql://postgres/billingsolution
      - spring.r2dbc.username=user
      - spring.r2dbc.password=pass
      - frontend.s3.url=http://localstack:4566
      - JAVA_TOOL_OPTIONS=-Djava.security.egd=file:/dev/./urandom
    depends_on:
      - postgres
      - localstack
      - greenmail
    networks:
      - billing-network

networks:
  billing-network:
