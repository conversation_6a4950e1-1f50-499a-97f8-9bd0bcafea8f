package com.klosesoft.billingsolution.common.utils

object CacheKeys {
    const val JWKSET = "jwkset"
    const val JASPERREPORTS = "jasperreports"

    const val USER_BY_KEY = "user_by_key"
    const val USER_BY_ID = "user_by_id"
    const val USER_BY_COGNITO_ID = "user_by_cognito_id"

    const val TENANTS_BY_KEY = "tenants_by_key"
    const val TENANTS_BY_ID = "tenants_by_id"
    const val TENANT_IDS_BY_KEY = "tenant_ids_by_key"
    const val TENANT_KEYS_BY_USER_ID = "tenant_keys_by_user_id"

    const val TENANT_CONFIGS_BY_APPCLIENTID = "tenant_configs_by_appclientid"
    const val TENANT_CONFIGS_BY_TENANTID = "tenant_configs_by_tenantid"

    const val WORKFLOW_DEFINITIONS_BY_TENANT_AND_TYPE = "workflow_definitions_by_tenant_and_type"

    const val NOTIFICATION_API_RECEIVER_CLIENTS = "notification_api_receiver_client"
    const val NOTIFICATION_API_RECEIVERS_BY_TENANTID = "notification_api_receiver_by_tenantid"

    const val SEQUENCE_NAME_MAPPING_BY_NAME_AND_TENANT_ID = "sequence_name_mapping_by_name_and_tenant_id"

    const val BUSINESS_SEGMENT_BY_KEY = "businessSegmentByKey"
    const val BUSINESS_SEGMENT_BY_ID = "businessSegmentById"

    const val TRANSLATIONS = "translations"
}
