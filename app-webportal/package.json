{"name": "billing-solution", "version": "0.3.4", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "format": "prettier --write \"src/app/**\"", "lint": "eslint src/app --ignore-pattern \"**/backend-api/\"", "lint-fix": "eslint src/app --fix --ignore-pattern \"**/backend-api/\"", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^20.0.5", "@angular/cdk": "^20.0.4", "@angular/common": "^20.0.5", "@angular/compiler": "^20.0.5", "@angular/core": "^20.0.5", "@angular/forms": "^20.0.5", "@angular/material": "^20.0.4", "@angular/material-experimental": "^20.0.4", "@angular/material-moment-adapter": "^20.0.4", "@angular/platform-browser": "^20.0.5", "@angular/platform-browser-dynamic": "^20.0.5", "@angular/router": "^20.0.5", "@bpmn-io/properties-panel": "^3.30.0", "@ngx-translate/core": "^16.0.4", "@ngx-translate/http-loader": "^16.0.1", "@types/pdfmake": "^0.2.11", "aws-amplify": "^6.15.1", "bpmn-js": "^18.6.2", "bpmn-js-properties-panel": "^5.37.0", "date-fns": "^4.1.0", "diagram-js": "^15.3.0", "echarts": "^5.6.0", "moment": "^2.30.1", "ngx-echarts": "^20.0.1", "pdfmake": "^0.2.20", "rxjs": "~7.8.2", "tslib": "^2.8.1", "uuid": "^11.1.0", "zone.js": "~0.15.1"}, "devDependencies": {"@angular/build": "^20.0.4", "@angular/cli": "^20.0.4", "@angular/compiler-cli": "^20.0.5", "@eslint/js": "^9.29.0", "@types/jasmine": "~5.1.8", "globals": "^16.2.0", "jasmine-core": "~5.8.0", "prettier": "^3.6.2", "typescript": "~5.8.3", "typescript-eslint": "^8.35.0"}}