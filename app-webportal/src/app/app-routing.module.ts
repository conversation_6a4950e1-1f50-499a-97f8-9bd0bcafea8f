/* eslint-disable @typescript-eslint/no-extraneous-class */
import { NgModule } from '@angular/core'
import { RouterModule, Routes } from '@angular/router'
import { DashboardComponent } from './features/dashboard/components/dashboard/dashboard.component'
import { UserSettingsComponent } from './features/administration/components/user-settings/user-settings.component'
import { OrderListComponent } from './features/billing/components/order-list/order-list.component'
import { OrderDetailsComponent } from './features/billing/components/order-details/order-details.component'
import { CustomerListComponent } from './features/billing/components/customer-list/customer-list.component'
import { CustomerDetailsComponent } from './features/billing/components/customer-details/customer-details.component'
import { PaymentTransactionListComponent } from './features/payment/components/payment-transaction-list/payment-transaction-list.component'
import { PaymentTransactionDetailsComponent } from './features/payment/components/payment-transaction-details/payment-transaction-details.component'
import { NotificationListComponent } from './features/administration/components/notification-list/notification-list.component'
import { LoginComponent } from './features/login/login.component'
import { AuthGuard } from './guards/auth.guard'
import { PostingRecordListComponent } from './features/bookkeeping/components/postingrecord-list/posting-record-list.component'
import { PostingRecordDetailsComponent } from './features/bookkeeping/components/postingrecord-details/posting-record-details.component'
import { BookingRuleListComponent } from './features/bookkeeping/components/booking-rule-list/booking-rule-list.component'
import { BookingRuleDetailsComponent } from './features/bookkeeping/components/booking-rule-details/booking-rule-details.component'
import { BookingAccountListComponent } from './features/bookkeeping/components/booking-account-list/booking-account-list.component'
import { CurrencyExchangeRateListComponent } from './features/bookkeeping/components/currency-exchange-rate-list/currency-exchange-rate-list.component'
import { WorkflowDetailsComponent } from './features/administration/components/workflow-details/workflow-details.component'
import { BusinessSegmentListComponent } from './features/billing/components/business-segment-list/business-segment-list.component'
import { BusinessSegmentDetailsComponent } from './features/billing/components/business-segment-details/business-segment-details.component'
import { ApprovalListComponent } from './features/billing/components/approval-list/approval-list.component'
import { ReportListComponent } from './features/billing/components/report-list/report-list.component'
import { UserListComponent } from './features/administration/components/user-list/user-list.component'
import { RoleListComponent } from './features/administration/components/role-list/role-list.component'
import { RoleDetailsComponent } from './features/administration/components/role-details/role-details.component'
import { UserDetailsComponent } from './features/administration/components/user-details/user-details.component'
import { PaymentAssignmentDetailsComponent } from './features/payment/components/payment-assignment-details/payment-assignment-details.component'
import { PaymentAssignmentListComponent } from './features/payment/components/payment-assignment-list/payment-assignment-list.component'
import { DocumentListComponent } from './features/billing/components/document-list/document-list.component'
import { SettlementReportListComponent } from './features/payment/components/settlement-report-list/settlement-report-list.component'
import { SettlementReportDetailsComponent } from './features/payment/components/settlement-report-details/settlement-report-details.component'
import { OpenItemListComponent } from './features/billing/components/open-item-list/open-item-list.component'
import { OpenItemDetailsComponent } from './features/billing/components/open-item-details/open-item-details.component'
import { PaymentAccountListComponent } from './features/payment/components/payment-account-list/payment-account-list.component'
import { PaymentCategorizationRuleListComponent } from './features/payment/components/payment-categorization-rule-list/payment-categorization-rule-list.component'
import { PaymentCategorizationRuleDetailsComponent } from './features/payment/components/payment-categorization-rule-details/payment-categorization-rule-details.component'
import { FailedJobListComponent } from './features/internal/components/failed-job-list/failed-job-list.component'
import { HistoricActivityListComponent } from './features/internal/components/historic-activity-list/historic-activity-list.component'
import { ApprovalDetailsComponent } from './features/billing/components/approval-details/approval-details.component'
import { ManualBookingDetailsComponent } from './features/bookkeeping/components/manual-booking-details/manual-booking-details.component'
import { ReportConfigurationListComponent } from './features/administration/components/report-configuration-list/report-configuration-list.component'
import { ReportConfigurationDetailsComponent } from './features/administration/components/report-configuration-details/report-configuration-details.component'
import { NotificationReceiverListComponent } from './features/administration/components/notification-receiver-list/notification-receiver-list.component'
import { NotificationReceiverDetailsComponent } from './features/administration/components/notification-receiver-details/notification-receiver-details.component'
import { PaymentAccountDetailsComponent } from './features/payment/components/payment-account-details/payment-account-details.component'
import { BookingAccountDetailsComponent } from './features/bookkeeping/components/booking-account-details/booking-account-details.component'
import { DocumentDetailsComponent } from './features/billing/components/document-details/document-details.component'
import { SentArchiveListComponent } from './features/administration/components/sent-archive-list/sent-archive-list.component'
import { SentArchiveDetailsComponent } from './features/administration/components/sent-archive-details/sent-archive-details.component'
import { TranslationListComponent } from './features/administration/components/translation-list/translation-list.component'
import { TranslationDetailsComponent } from './features/administration/components/translation-details/translation-details.component'
import { SuppliersListComponent } from './features/billing/components/suppliers-list/suppliers-list.component'
import { SuppliersDetailsComponent } from './features/billing/components/suppliers-details/suppliers-details.component'
import { SubscriptionListComponent } from './features/billing/components/subscription-list/subscription-list.component'
import { SubscriptionDetailsViewComponent } from './features/billing/components/subscription-details-view/subscription-details-view.component'
import { DirtyInputGuard } from './guards/dirty-input.guard'
import { OrderCaptureDetailsComponent } from './features/billing/components/order-capture-details/order-capture-details.component'
import {OrderUpdateDetailsComponent} from "./features/billing/components/order-update-details/order-update-details.component";

const routes: Routes = [
  { path: '', redirectTo: '/login', pathMatch: 'full' },
  { path: 'login', component: LoginComponent },
  { path: 'dashboard', component: DashboardComponent, canActivate: [AuthGuard] },
  { path: 'customers', component: CustomerListComponent, canActivate: [AuthGuard] },
  { path: 'customers/:key', component: CustomerDetailsComponent, canActivate: [AuthGuard] },
  { path: 'suppliers', component: SuppliersListComponent, canActivate: [AuthGuard] },
  { path: 'suppliers/:key', component: SuppliersDetailsComponent, canActivate: [AuthGuard] },
  { path: 'subscriptions', component: SubscriptionListComponent, canActivate: [AuthGuard] },
  { path: 'subscriptions/:key', component: SubscriptionDetailsViewComponent, canActivate: [AuthGuard] },
  {
    path: 'order-capture/:key',
    component: OrderCaptureDetailsComponent,
    canActivate: [AuthGuard],
    canDeactivate: [DirtyInputGuard],
  },
  { path: 'orders', component: OrderListComponent, canActivate: [AuthGuard] },
  { path: 'orders/:key', component: OrderDetailsComponent, canActivate: [AuthGuard] },
  { path: 'orders/:key/edit', component: OrderUpdateDetailsComponent, canActivate: [AuthGuard] },
  { path: 'open-items', component: OpenItemListComponent, canActivate: [AuthGuard] },
  { path: 'open-items/:key', component: OpenItemDetailsComponent, canActivate: [AuthGuard] },
  { path: 'documents', component: DocumentListComponent, canActivate: [AuthGuard] },
  { path: 'documents/:key', component: DocumentDetailsComponent, canActivate: [AuthGuard] },
  { path: 'reports', component: ReportListComponent, canActivate: [AuthGuard] },
  { path: 'business-segments', component: BusinessSegmentListComponent, canActivate: [AuthGuard] },
  {
    path: 'business-segments/:key',
    component: BusinessSegmentDetailsComponent,
    canActivate: [AuthGuard],
    canDeactivate: [DirtyInputGuard],
  },
  { path: 'approvals', component: ApprovalListComponent, canActivate: [AuthGuard] },
  {
    path: 'approvals/:key',
    component: ApprovalDetailsComponent,
    canActivate: [AuthGuard],
    canDeactivate: [DirtyInputGuard],
  },
  { path: 'payment-accounts', component: PaymentAccountListComponent, canActivate: [AuthGuard] },
  {
    path: 'payment-accounts/:key',
    component: PaymentAccountDetailsComponent,
    canActivate: [AuthGuard],
    canDeactivate: [DirtyInputGuard],
  },
  { path: 'settlement-reports', component: SettlementReportListComponent, canActivate: [AuthGuard] },
  { path: 'settlement-reports/:key', component: SettlementReportDetailsComponent, canActivate: [AuthGuard] },
  { path: 'payment-categorization-rules', component: PaymentCategorizationRuleListComponent, canActivate: [AuthGuard] },
  {
    path: 'payment-categorization-rules/:key',
    component: PaymentCategorizationRuleDetailsComponent,
    canActivate: [AuthGuard],
  },
  { path: 'payment-transactions', component: PaymentTransactionListComponent, canActivate: [AuthGuard] },
  { path: 'payment-transactions/:key', component: PaymentTransactionDetailsComponent, canActivate: [AuthGuard] },
  {
    path: 'payment-transactions/:payment-transaction-key/payment-assignments/:key',
    component: PaymentAssignmentDetailsComponent,
    canActivate: [AuthGuard],
    canDeactivate: [DirtyInputGuard],
  },
  { path: 'payment-assignments', component: PaymentAssignmentListComponent, canActivate: [AuthGuard] },
  {
    path: 'payment-assignments/:key',
    component: PaymentAssignmentDetailsComponent,
    canActivate: [AuthGuard],
    canDeactivate: [DirtyInputGuard],
  },
  { path: 'booking-accounts', component: BookingAccountListComponent, canActivate: [AuthGuard] },
  {
    path: 'booking-accounts/:key',
    component: BookingAccountDetailsComponent,
    canActivate: [AuthGuard],
    canDeactivate: [DirtyInputGuard],
  },
  { path: 'booking-rules', component: BookingRuleListComponent, canActivate: [AuthGuard] },
  {
    path: 'booking-rules/:key',
    component: BookingRuleDetailsComponent,
    canActivate: [AuthGuard],
    canDeactivate: [DirtyInputGuard],
  },
  { path: 'posting-records', component: PostingRecordListComponent, canActivate: [AuthGuard] },
  { path: 'posting-records/:key', component: PostingRecordDetailsComponent, canActivate: [AuthGuard] },
  { path: 'currency-exchange-rates', component: CurrencyExchangeRateListComponent, canActivate: [AuthGuard] },
  {
    path: 'manual-bookings/:key',
    component: ManualBookingDetailsComponent,
    canActivate: [AuthGuard],
    canDeactivate: [DirtyInputGuard],
  },
  { path: 'sent-archive', component: SentArchiveListComponent, canActivate: [AuthGuard] },
  { path: 'sent-archive/:key', component: SentArchiveDetailsComponent, canActivate: [AuthGuard] },
  { path: 'notifications', component: NotificationListComponent, canActivate: [AuthGuard] },
  { path: 'notification-receivers', component: NotificationReceiverListComponent, canActivate: [AuthGuard] },
  {
    path: 'notification-receivers/:key',
    component: NotificationReceiverDetailsComponent,
    canActivate: [AuthGuard],
    canDeactivate: [DirtyInputGuard],
  },
  { path: 'translations', component: TranslationListComponent, canActivate: [AuthGuard] },
  { path: 'translations/:key', component: TranslationDetailsComponent, canActivate: [AuthGuard] },
  { path: 'report-configurations', component: ReportConfigurationListComponent, canActivate: [AuthGuard] },
  { path: 'report-configurations/:key', component: ReportConfigurationDetailsComponent, canActivate: [AuthGuard] },
  { path: 'users', component: UserListComponent, canActivate: [AuthGuard] },
  {
    path: 'users/:key',
    component: UserDetailsComponent,
    canActivate: [AuthGuard],
    canDeactivate: [DirtyInputGuard],
  },
  { path: 'roles', component: RoleListComponent, canActivate: [AuthGuard] },
  {
    path: 'roles/:key',
    component: RoleDetailsComponent,
    canActivate: [AuthGuard],
    canDeactivate: [DirtyInputGuard],
  },
  { path: 'user-settings', component: UserSettingsComponent, canActivate: [AuthGuard] },
  { path: 'workflow', component: WorkflowDetailsComponent, canActivate: [AuthGuard] },
  { path: 'failed-jobs', component: FailedJobListComponent, canActivate: [AuthGuard] },
  { path: 'historic-activities', component: HistoricActivityListComponent, canActivate: [AuthGuard] },
]

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule],
})
export class AppRoutingModule {}

/* eslint-enable @typescript-eslint/no-extraneous-class */
