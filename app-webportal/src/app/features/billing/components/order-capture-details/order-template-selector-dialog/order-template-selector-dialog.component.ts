import { AfterViewInit, Component, Inject, ViewChild } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog'
import { WebPortalOrderService } from 'src/app/backend-api'
import { Column, DateColumn, DateTimeColumn, StringColumn } from '../../../../../shared/models/column'
import { DetailsTableComponent } from '../../../../../shared/components/details-table/details-table.component'
import { TranslateService } from '@ngx-translate/core'

@Component({
  standalone: false,
  selector: 'app-order-template-selector-dialog',
  templateUrl: './order-template-selector-dialog.component.html',
  styleUrls: ['./order-template-selector-dialog.component.css'],
})
export class OrderTemplateSelectorDialogComponent implements AfterViewInit {
  @ViewChild('tableTemplates') tableTemplates: DetailsTableComponent

  allColumns: Column[] = [
    new StringColumn('title', this.translate.instant('ORDER_TEMPLATES.TITLE'), true, 'title'),
    new StringColumn('description', this.translate.instant('ORDER_TEMPLATES.DESCRIPTION'), true, 'description'),
    new DateTimeColumn('createdAt', this.translate.instant('ORDER_TEMPLATES.CREATED_AT'), true, 'createdAt'),
  ]

  constructor(
    public dialogRef: MatDialogRef<OrderTemplateSelectorDialogComponent>,
    private webPortalOrderService: WebPortalOrderService,
    @Inject(MAT_DIALOG_DATA) public data: DialogData,
    private translate: TranslateService
  ) {}

  ngAfterViewInit() {
    this.tableTemplates.reloadData()
  }

  loadOrderTemplates($event: any) {
    const observable = this.webPortalOrderService.loadOrderTemplates(
      this.data.tenantKey,
      this.data.businessSegmentKey,
      $event.pageIndex,
      $event.pageSize,
      $event.sort,
      $event.direction,
      $event.filterText
    )
    this.tableTemplates.loadedData(observable)
  }

  onDblClick(event: any) {
    this.close(event)
  }

  onSubmit() {
    this.close(this.tableTemplates.selection)
  }

  onCancel() {
    this.dialogRef.close()
  }

  private close(row: any) {
    this.dialogRef.close(row)
  }
}

export interface DialogData {
  tenantKey: string
  businessSegmentKey: string
}
