import { ChangeDetectorRef, Component, ElementRef, ViewChild } from '@angular/core'
import {
  Address,
  BookingRuleType,
  CustomerType,
  DebitCreditIndicator,
  DocumentFormat,
  FinancingType,
  ItemCategory,
  TaxInformation,
  WebPortalBusinessSegmentService,
  WebPortalCustomerDetailsResponse,
  WebPortalCustomerListEntry,
  WebPortalCustomerService,
  WebPortalCustomersPagedResponse,
  WebPortalDocumentService,
  WebPortalOrderCapture,
  WebPortalOrderItem,
  WebPortalOrderService,
  WebPortalOrderTemplateItemRequest,
  WebPortalOrderTemplateRequest,
  WebPortalOrderTemplateResponse,
  WebPortalOrderTemplatesPagedResponse,
  WebPortalPredefinedItem,
  WebPortalPredefinedItemService,
  WebPortalPredefinedItemsPagedResponse,
} from '../../../../backend-api'
import { debounceTime, interval, map, Observable, of, startWith, takeUntil, takeWhile } from 'rxjs'
import { DetailsComponent } from '../../../../shared/components/details/details.component'
import { switchMap } from 'rxjs/operators'
import { addDays } from 'date-fns'
import { v4 as uuidv4 } from 'uuid'
import { LinkedList, LinkedListEntry } from './LinkedList'
import { AbstractControl, FormControl, ValidatorFn, Validators } from '@angular/forms'
import { LocaleService } from '../../../../services/locale.service'
import { DecimalPipe } from '@angular/common'
import { PdfViewerDialogComponent } from '../../../../shared/components/pdf-viewer-dialog/pdf-viewer-dialog.component'
import { MatDialog } from '@angular/material/dialog'
import { ConfirmDialogComponent } from '../../../../shared/components/confirm-dialog/confirm-dialog.component'
import { OrderTemplateAddDialogComponent } from './order-template-add-dialog/order-template-add-dialog.component'
import {
  DialogData,
  OrderTemplateSelectorDialogComponent,
} from './order-template-selector-dialog/order-template-selector-dialog.component'
import { CustomerSelectorDialogComponent } from '../customer-selector-dialog/customer-selector-dialog.component'
import TaxTypeEnum = TaxInformation.TaxTypeEnum

@Component({
  standalone: false,
  selector: 'app-order-capture-details',
  templateUrl: './order-capture-details.component.html',
  styleUrl: './order-capture-details.component.css',
})
export class OrderCaptureDetailsComponent extends DetailsComponent<WebPortalOrderCapture> {
  @ViewChild('customerInput') customerInput: ElementRef

  constructor(
    private webPortalOrderService: WebPortalOrderService,
    private webPortalDocumentService: WebPortalDocumentService,
    private webPortalCustomerService: WebPortalCustomerService,
    private webPortalBusinessSegmentService: WebPortalBusinessSegmentService,
    private webPortalPredefinedItemService: WebPortalPredefinedItemService,
    private personSelectorDialog: MatDialog,
    private templateSelectDialog: MatDialog,
    private templateAddDialog: MatDialog,
    override cdr: ChangeDetectorRef,
    protected localeService: LocaleService,
    protected decimalPipe: DecimalPipe,
    private dialog: MatDialog
  ) {
    super()
  }

  template: WebPortalOrderTemplateResponse = null
  customer: WebPortalCustomerListEntry = null
  billingAddress: Address = null
  orderDate: Date = new Date()
  filteredCustomers: Observable<WebPortalCustomerListEntry[]>
  filteredTemplates: Observable<WebPortalOrderTemplateResponse[]>
  filteredArticleNumberPredefinedItems: Observable<WebPortalPredefinedItem[]>
  filteredItemNamePredefinedItems: Observable<WebPortalPredefinedItem[]>
  items = new LinkedList<WebPortalOrderItem>()
  businessSegmentKeys = []
  formControls = new Map<string, any[]>()
  formValidators = new Map<string, any[]>()

  override ngOnInit() {
    super.ngOnInit()
    this.isLoading = true
    this.formControls['customer'] = [this.customer]
    this.formValidators['customer'] = this.formValidationService.requiredValidatorSet()

    this.formControls['orderDate'] = [this.orderDate]
    this.formValidators['orderDate'] = this.formValidationService.requiredValidatorSet()

    this.formControls['orderCurrency'] = [this.model.orderCurrency]
    this.formValidators['orderCurrency'] = this.formValidationService.requiredValidatorSet()

    this.formControls['businessSegmentKey'] = [this.model.businessSegmentKey]
    this.formValidators['businessSegmentKey'] = this.formValidationService.requiredValidatorSet()

    this.webPortalBusinessSegmentService
      .loadBusinessSegments(this.sessionService.getTenantKey())
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (data: any) => {
          this.businessSegmentKeys = data.items.map((item: { key: any }) => item.key)
          this.model.businessSegmentKey = this.businessSegmentKeys[0]
          this.form.controls['businessSegmentKey'].setValue(this.model.businessSegmentKey)

          this.initTemplateFilter()
        },
        error: (error: any) => {
          console.error('business segment load error', error)
        },
      })

    this.formControls['template'] = [this.template]
    this.formValidators['template'] = []

    this.initForm()

    this.addEmptyOrderItemLine()
  }

  override ngAfterViewInit() {
    super.ngAfterViewInit()

    this.filteredCustomers = this.form.get('customer').valueChanges.pipe(
      debounceTime(200),
      startWith(''),
      switchMap((value) => this.searchCustomer(value))
    )

    this.cdr.detectChanges()
  }

  searchTemplate(term: any): Observable<WebPortalOrderTemplateResponse[]> {
    const titleFilter = term && term.length > 0 ? 'title=like=' + term + '%' : null

    return this.webPortalOrderService
      .loadOrderTemplates(
        this.sessionService.getTenantKey(),
        this.form.get('businessSegmentKey').value,
        0,
        10,
        'title',
        'asc',
        titleFilter
      )
      .pipe(
        switchMap((data: any) => {
          return of(data.items)
        })
      )
  }

  private initTemplateFilter() {
    console.log('initTemplateFilter', this.model.businessSegmentKey)
    return this.webPortalOrderService
      .loadOrderTemplates(this.sessionService.getTenantKey(), this.model.businessSegmentKey, 0, 10, 'title', 'asc')
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (template: WebPortalOrderTemplatesPagedResponse) => {
          this.isLoading = false

          this.filteredTemplates = of(template.items)
          this.filteredTemplates = this.form.get('template').valueChanges.pipe(
            debounceTime(200),
            startWith(''),
            switchMap((value) => this.searchTemplate(value))
          )
        },
        error: (error: any) => {
          this.isLoading = false
          console.error('Failed to load templates', error)
        },
      })
  }

  searchCustomer(term: any): Observable<WebPortalCustomerListEntry[]> {
    if (term === '' || term === null || term === undefined || term.length < 2 || term instanceof Object) {
      return of([])
    }

    console.log('searching for customers with term', term)

    return this.webPortalCustomerService
      .loadCustomers(
        this.sessionService.getTenantKey(),
        0,
        10,
        'lastName',
        'asc',
        'lastName=like=' + term + '%,firstName=like=' + term + '%,companyName=like=' + term + '%'
      )
      .pipe(
        switchMap((data: WebPortalCustomersPagedResponse) => {
          return of(data.items)
        })
      )
  }

  onCustomerSelected(customer: WebPortalCustomerListEntry) {
    this.customer = customer
    this.loadBillingAddress(customer.key)
  }

  loadBillingAddress(customerKey: string) {
    this.webPortalCustomerService
      .loadCustomer(this.sessionService.getTenantKey(), customerKey)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (loadedCustomer: WebPortalCustomerDetailsResponse) => {
          this.billingAddress = loadedCustomer.billingAddress
        },
        error: (error: any) => {
          console.error('Failed to load billing address', error)
        },
      })
  }

  searchArticleNumber(term: any): Observable<WebPortalPredefinedItem[]> {
    if (term === '' || term === null || term === undefined || term instanceof Object) {
      return of([])
    }

    console.log('searching for predefined item with term', term)

    return this.webPortalPredefinedItemService
      .loadPredefinedItems(
        this.sessionService.getTenantKey(),
        0,
        20,
        'articleNumber',
        'asc',
        'articleNumber=like=' + term + '%'
      )
      .pipe(
        switchMap((data: WebPortalPredefinedItemsPagedResponse) => {
          return of(data.items)
        })
      )
  }

  searchItemName(term: any): Observable<WebPortalPredefinedItem[]> {
    if (term === '' || term === null || term === undefined || term instanceof Object) {
      return of([])
    }

    console.log('searching for predefined item with term', term)

    return this.webPortalPredefinedItemService
      .loadPredefinedItems(
        this.sessionService.getTenantKey(),
        0,
        20,
        'articleNumber',
        'asc',
        'name=like=%' + term + '%'
      )
      .pipe(
        switchMap((data: WebPortalPredefinedItemsPagedResponse) => {
          return of(data.items)
        })
      )
  }

  onPredefinedItemSelected(item: WebPortalPredefinedItem, valueKey: string) {
    this.form.get('articleNumber' + valueKey).setValue(item)
    this.form.get('itemName' + valueKey).setValue(item)
    this.form.get('itemDescription' + valueKey).setValue(item.description)
    this.form.get('itemQuantity' + valueKey).setValue(this.formatDecimal(item.quantity, '1.1-1'))
    this.form.get('itemNetUnitPrice' + valueKey).setValue(this.formatDecimal(item.unitNetAmount, '1.2-2'))
    this.form.get('itemTaxRate' + valueKey).setValue(item.taxInformation.taxRate)
  }

  override onUndo() {
    for (const value of this.items) {
      this.removeOrderItemLine(value)
    }

    this.addEmptyOrderItemLine()

    this.customer = null
    this.billingAddress = null
    this.orderDate = new Date()

    super.onUndo()
  }

  customerAutocompleteDisplay(customer: WebPortalCustomerListEntry): string {
    return customer
      ? customer.customerType === CustomerType.Person
        ? customer.firstName + ' ' + customer.lastName
        : customer.companyName
      : ''
  }

  templateAutocompleteDisplay(template: WebPortalOrderTemplateResponse): string {
    return template?.title
  }

  articleNumberAutocompleteDisplay(item: WebPortalPredefinedItem): string {
    return item?.articleNumber
  }

  itemNameAutocompleteDisplay(item: WebPortalPredefinedItem): string {
    return item?.name
  }

  addEmptyOrderItemLine(previous?: LinkedListEntry<WebPortalOrderItem>): void {
    const userData = this.sessionService.getCurrentUser().userData
    const newValue = {
      articleNumber: '',
      version: 0,
      category: ItemCategory.Service,
      currency: this.form.get('orderCurrency').value,
      debitCreditIndicator: DebitCreditIndicator.Debit,
      description: '',
      key: uuidv4(),
      name: '',
      quantity: 1,
      unitNetAmount: 0,
      taxInformation: {
        taxType: TaxTypeEnum.Vat,
        currency: this.form.get('orderCurrency').value,
        taxCode: 'EMPTY',
        taxRate: userData.defaultTaxRate,
      } as TaxInformation,
    }

    this.addOrderItemLine(newValue, previous)
  }

  formatDecimal(value: any, fractionDigits = '1.2-2'): string {
    return this.decimalPipe.transform(value, fractionDigits, this.localeService.getLocale())
  }

  addOrderItemLine(newValue: WebPortalOrderItem, previous?: LinkedListEntry<WebPortalOrderItem>): void {
    this.items.add(previous, newValue)

    this.form.addControl(
      'articleNumber' + newValue.key,
      new FormControl(newValue.articleNumber, this.formValidationService.requiredValidatorSet())
    )

    this.filteredArticleNumberPredefinedItems = this.form.get('articleNumber' + newValue.key).valueChanges.pipe(
      debounceTime(200),
      startWith(''),
      switchMap((value) => this.searchArticleNumber(value))
    )

    this.form.addControl(
      'itemName' + newValue.key,
      new FormControl(newValue.name, this.formValidationService.requiredValidatorSet())
    )

    this.filteredItemNamePredefinedItems = this.form.get('itemName' + newValue.key).valueChanges.pipe(
      debounceTime(200),
      startWith(''),
      switchMap((value) => this.searchItemName(value))
    )

    let quantityValidator = this.formValidationService.requiredValidatorSet()

    if (this.sessionService.isMedicalUser()) {
      quantityValidator = [
        Validators.required,
        this.formValidationService.goaFactorValidator(),
        this.formValidationService.floatValidator(),
      ]
    }

    const quantityKey = 'itemQuantity' + newValue.key
    this.form.addControl(quantityKey, new FormControl(newValue.quantity, quantityValidator))

    let descriptionValidator = this.formValidationService.stringValidatorSet(500)

    if (this.sessionService.isMedicalUser()) {
      descriptionValidator = [
        ...this.formValidationService.stringValidatorSet(500),
        this.descriptionValidator(quantityKey),
      ]
    }

    this.form.addControl('itemDescription' + newValue.key, new FormControl(newValue.description, descriptionValidator))

    this.form.get(quantityKey).valueChanges.subscribe(() => {
      this.form.get('itemDescription' + newValue.key).updateValueAndValidity()
    })

    this.form.addControl(
      'itemNetUnitPrice' + newValue.key,
      new FormControl(newValue.unitNetAmount, this.formValidationService.requiredValidatorSet())
    )

    this.form.addControl(
      'itemTaxRate' + newValue.key,
      new FormControl(newValue.taxInformation.taxRate, this.formValidationService.requiredValidatorSet())
    )
  }

  descriptionValidator(quantityKey: string): ValidatorFn {
    return (control: AbstractControl): Record<string, any> | null => {
      const quantity = this.form.get(quantityKey)

      if (quantity) {
        const quantityValue = this.formValidationService.convertToStandardFormat(quantity.value)
        if (quantityValue > 2.3) {
          return Validators.required(control)
        }
      }
      return null
    }
  }

  removeOrderItemLine(entry: LinkedListEntry<WebPortalOrderItem>): void {
    this.items.remove(entry)

    this.form.removeControl('articleNumber' + entry.value.key)
    this.form.removeControl('itemName' + entry.value.key)
    this.form.removeControl('itemDescription' + entry.value.key)
    this.form.removeControl('itemQuantity' + entry.value.key)
    this.form.removeControl('itemNetUnitPrice' + entry.value.key)
    this.form.removeControl('itemTaxRate' + entry.value.key)
  }

  override getFormControls() {
    return this.formControls
  }

  override getFormValidators() {
    return this.formValidators
  }

  override initNewModel(): WebPortalOrderCapture {
    const currency = this.sessionService.getCurrentUser().userData.currency

    return {
      agreedDepositAmount: 0,
      deliveryDate: null,
      items: [],
      orderCurrency: currency,
      paymentDueDate: null,
      properties: [],
      title: '',
      customerKey: '',
      businessSegmentKey: '',
      financingType: FinancingType.Cash,
      orderDate: null,
      key: uuidv4(),
    }
  }

  override createModel() {
    this.isLoading = true

    const deliveryDate = this.orderDate
    const paymentDueDate = addDays(this.orderDate, 30)

    this.updateItemsModelFromForm()

    const payload = {
      key: this.model.key,
      customerKey: this.form.get('customer').value.key,
      businessSegmentKey: this.form.get('businessSegmentKey').value,
      orderDate: this.form.get('orderDate').value,
      deliveryDate: deliveryDate as unknown as string,
      paymentDueDate: paymentDueDate as unknown as string,
      items: this.items.toList(),
      orderCurrency: this.form.get('orderCurrency').value,
      financingType: this.model.financingType,
    } as WebPortalOrderCapture

    console.log('create order payload', payload)

    this.webPortalOrderService
      .createOrder(this.sessionService.getTenantKey(), payload)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.showSnackBarInfo('ORDER_CAPTURE.ORDER_CAPTURED', this.model.key)

          if (this.sessionService.isMedicalUser()) {
            this.displayCreatedPdf(this.model.key)
          } else {
            this.isLoading = false
            this.onUndo()
          }
        },
        error: (error: any) => {
          this.isLoading = false
          this.showSnackBarError('ORDER_CAPTURE.ORDER_CAPTURE_FAILED', this.model.key, error)
        },
      })
  }

  displayCreatedPdf(orderKey: string) {
    this.webPortalDocumentService
      .loadDocumentKeys(this.sessionService.getTenantKey(), 0, 1, 'createdAt', 'desc', 'orderKey==' + orderKey)
      .pipe(
        switchMap((documentData: any) => {
          if (documentData.items.length > 0) {
            return of(documentData)
          } else {
            return interval(250).pipe(
              switchMap(() =>
                this.webPortalDocumentService.loadDocumentKeys(
                  this.sessionService.getTenantKey(),
                  0,
                  1,
                  'createdAt',
                  'desc',
                  'orderKey==' + orderKey
                )
              ),
              takeWhile((data: any) => data.items.length === 0, true)
            )
          }
        }),
        takeUntil(this.destroy$)
      )
      .subscribe({
        next: (documentData: any) => {
          if (documentData.items.length > 0) {
            this.openInvoicePdf(orderKey, documentData.items[0], false)
            this.isLoading = false
            this.onUndo()
          }
        },
        error: (error: any) => {
          console.error('document load error', error)
        },
      })
  }

  openInvoicePdf(
    orderKey: string,
    documentKey: string,
    openCopy: boolean,
    format: DocumentFormat = DocumentFormat.EPdf
  ) {
    return this.webPortalDocumentService
      .loadDocumentFile(this.sessionService.getTenantKey(), documentKey, orderKey, undefined, format, openCopy, 'response')
      .pipe(
        map((response) => new Blob([response.body], { type: 'application/pdf' })),
        takeUntil(this.destroy$)
      )
      .subscribe({
        next: (blob: Blob) => {
          const fileURL = URL.createObjectURL(blob)
          console.log(fileURL)
          this.dialog.open(PdfViewerDialogComponent, {
            data: { url: fileURL },
          })
        },
        error: (error: any) => {
          this.showSnackBarError('DOCUMENTS.DOWNLOAD_FAILED', documentKey, error)
        },
      })
  }

  override loadModel(): void {
    throw new Error('Method not implemented.')
  }

  override updateModel(): void {
    throw new Error('Method not implemented.')
  }

  override getFirstElementToFocus() {
    return this.customerInput
  }

  protected readonly CustomerType = CustomerType
  protected readonly BookingRuleType = BookingRuleType

  getSumOfItemNetUnitPrices(): number {
    let sum = 0
    for (const item of this.items) {
      if (item && item.value) {
        const quantityValue = this.formValidationService.convertToStandardFormat(
          this.form.get('itemQuantity' + item.value.key)?.value
        )
        const unitPriceValue = this.formValidationService.convertToStandardFormat(
          this.form.get('itemNetUnitPrice' + item.value.key)?.value
        )
        if (unitPriceValue && quantityValue) sum += unitPriceValue * quantityValue
      }
    }
    return sum
  }

  getSumOfTaxAmounts() {
    let sum = 0
    for (const item of this.items) {
      if (item && item.value) {
        const quantityValue = this.formValidationService.convertToStandardFormat(
          this.form.get('itemQuantity' + item.value.key)?.value
        )
        const unitPriceValue = this.formValidationService.convertToStandardFormat(
          this.form.get('itemNetUnitPrice' + item.value.key)?.value
        )
        const taxRateValue = this.form.get('itemTaxRate' + item.value.key)?.value
        if (unitPriceValue && taxRateValue && quantityValue) {
          sum += unitPriceValue * (parseFloat(taxRateValue) / 100) * quantityValue
        }
      }
    }
    return sum
  }

  getSumOfItemGrossAmounts() {
    return this.getSumOfItemNetUnitPrices() + this.getSumOfTaxAmounts()
  }

  onSaveTemplate() {
    if (!this.validateForm()) {
      return
    }

    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      data: {
        title: this.translate.instant('ORDER_TEMPLATES.SAVE_TEMPLATE'),
        message: this.translate.instant('ORDER_TEMPLATES.CONFIRM_SAVE_TEMPLATE', { value: this.template.title }),
      },
    })

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.updateItemsModelFromForm()
        this.webPortalOrderService
          .updateOrderTemplate(
            this.sessionService.getTenantKey(),
            this.form.get('businessSegmentKey').value,
            this.template.key,
            this.toTemplateRequest(this.template)
          )
          .pipe(takeUntil(this.destroy$))
          .subscribe({
            next: () => {
              this.showSnackBarInfo('ORDER_TEMPLATES.TEMPLATE_SAVED', this.template.title)
            },
            error: (error: any) => {
              this.showSnackBarError('ORDER_TEMPLATES.TEMPLATE_SAVE_FAILED', this.template.title, error)
            },
          })
      }
    })
  }

  validateForm(): boolean {
    this.form.markAllAsTouched()
    if (this.form.invalid) {
      this.showSnackBarInfo('MAIN.INPUT_ERROR')
      return false
    }

    if (this.items.toList().length === 0) {
      this.showSnackBarInfo('FORM.VALIDATION_ERROR')
      return false
    }

    return true
  }

  private showSnackBarInfo(title: string, entityTitle?: string) {
    const message = entityTitle ? this.translate.instant(title, { value: entityTitle }) : this.translate.instant(title)
    this.snackBar.open(message, this.translate.instant('MAIN.CLOSE'), { duration: 5000 })
  }

  private showSnackBarError(title: string, entityTitle: string, error: any) {
    this.snackBar.open(
      this.translate.instant(title, {
        value: entityTitle,
        reason: error.toString().replace('Error: ', ''),
      }),
      this.translate.instant('MAIN.CLOSE'),
      { duration: 5000 }
    )
  }

  updateItemsModelFromForm() {
    for (const item of this.items) {
      item.value.articleNumber = this.form.get('articleNumber' + item.value.key).value.articleNumber
      item.value.name = this.form.get('itemName' + item.value.key).value.name
      item.value.description = this.form.get('itemDescription' + item.value.key).value
      item.value.quantity = this.formValidationService.convertToStandardFormat(
        this.form.get('itemQuantity' + item.value.key).value
      )
      item.value.unitNetAmount = this.formValidationService.convertToStandardFormat(
        this.form.get('itemNetUnitPrice' + item.value.key).value
      )
      item.value.currency = this.form.get('orderCurrency').value
      item.value.taxInformation.taxRate = this.form.get('itemTaxRate' + item.value.key).value
    }
  }

  toTemplateRequest(template: WebPortalOrderTemplateResponse): WebPortalOrderTemplateRequest {
    return {
      key: template.key,
      title: template.title,
      description: template.description,
      currency: this.form.get('orderCurrency').value,
      items: this.items.toList().map((item) => this.toWebPortalTemplateItemRequest(item)),
    }
  }

  toWebPortalTemplateItemRequest(item: WebPortalOrderItem): WebPortalOrderTemplateItemRequest {
    return {
      articleNumber: item.articleNumber,
      category: item.category,
      currency: item.currency,
      debitCreditIndicator: item.debitCreditIndicator,
      description: item.description,
      name: item.name,
      quantity: item.quantity,
      unitNetAmount: item.unitNetAmount,
      taxInformation: {
        taxType: item.taxInformation.taxType,
        currency: item.taxInformation.currency,
        taxCode: item.taxInformation.taxCode,
        taxRate: item.taxInformation.taxRate,
      },
    }
  }

  onSaveTemplateAs() {
    if (!this.validateForm()) {
      return
    }

    this.updateItemsModelFromForm()

    const templateRequest = {
      key: 'new',
      title: 'New Template',
      description: 'New Template Description',
      currency: this.form.get('orderCurrency').value,
      items: this.items.toList().map((item) => this.toWebPortalTemplateItemRequest(item)),
    }

    const data = {
      businessSegmentKey: this.form.value.businessSegmentKey,
      orderTemplateRequest: templateRequest,
    }

    const dialogRef = this.templateAddDialog.open(OrderTemplateAddDialogComponent, {
      width: '500px',
      data,
    })

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.showSnackBarInfo('ORDER_TEMPLATES.TEMPLATE_SAVED', result.title)
      }
    })
  }

  openTemplateDialog(event: MouseEvent) {
    // prevent displaying the options menu at the same time
    event.stopPropagation()

    const data: DialogData = {
      tenantKey: this.sessionService.getTenantKey(),
      businessSegmentKey: this.form.get('businessSegmentKey').value,
    }

    const dialogRef = this.templateSelectDialog.open(OrderTemplateSelectorDialogComponent, {
      width: '700px',
      data,
    })

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.onTemplateSelect(result)
      }
    })
  }

  openCustomerDialog(event: MouseEvent) {
    // prevent displaying the options menu at the same time
    event.stopPropagation()

    const dialogRef = this.personSelectorDialog.open(CustomerSelectorDialogComponent, {
      width: '700px',
    })

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.onCustomerSelect(result)
      }
    })
  }

  onCustomerSelect(customer: WebPortalCustomerListEntry) {
    this.customer = customer
    this.form.get('customer').setValue(customer)
    this.loadBillingAddress(customer.key)
  }

  onTemplateSelect(template: WebPortalOrderTemplateResponse) {
    this.template = template
    this.form.get('template').setValue(template)

    this.webPortalOrderService
      .loadOrderTemplateItems(
        this.sessionService.getTenantKey(),
        this.form.get('businessSegmentKey').value,
        template.key
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (data: any) => {
          this.removeAllOrderItemLine()
          console.log('order template items', data)
          for (let i = 0; i < data.items.length; i++) {
            const item = data.items[i]
            const newValue = {
              key: uuidv4(),
              version: 0,
              articleNumber: item.articleNumber,
              category: item.category,
              currency: this.form.get('orderCurrency').value,
              debitCreditIndicator: item.debitCreditIndicator,
              name: item.name,
              description: item.description,
              quantity: item.quantity,
              unitNetAmount: item.unitNetAmount,
              totalNetAmount: item.totalNetAmount,
              taxInformation: {
                taxType: item.taxInformation.taxType,
                currency: item.taxInformation.currency,
                taxCode: item.taxInformation.taxCode,
                taxRate: item.taxInformation.taxRate,
              },
            }

            if (i === 0) {
              this.addOrderItemLine(newValue)
            } else {
              this.addOrderItemLine(newValue, this.items.head)
            }
            this.onPredefinedItemSelected(newValue, newValue.key)
          }
        },
        error: (error: any) => {
          console.error('order template items load error', error)
        },
      })
  }

  private removeAllOrderItemLine() {
    for (const value of this.items) {
      this.removeOrderItemLine(value)
    }
  }
}
