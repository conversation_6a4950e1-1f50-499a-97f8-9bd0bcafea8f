<h2 mat-dialog-title>{{ 'ORDERS.SELECT_ORDER' | translate }}</h2>
<div style="margin: 20px">
  <app-details-table
    #tableOrders
    [columns]="allColumns"
    initialSort="orderDate"
    initialSortDirection="desc"
    (loadDataEmitter)="loadOrders($event)"
    (dblClickEmitter)="onDblClick($event)"
    [initialShowFilter]="true"
    [withConfig]="false"
    [withExport]="false"
  ></app-details-table>

  <form (ngSubmit)="onSubmit()">
    <div mat-dialog-actions cdkTrapFocus [cdkTrapFocusAutoCapture]="true" align="center">
      <button type="submit" mat-raised-button [mat-dialog-close]="data">{{ 'MAIN.APPLY' | translate }}</button>
      <button type="button" mat-raised-button (click)="onCancel()">{{ 'MAIN.CANCEL' | translate }}</button>
    </div>
  </form>
</div>
