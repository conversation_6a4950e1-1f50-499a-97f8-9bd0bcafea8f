import { Component, ElementRef, ViewChild } from '@angular/core'
import { finalize, takeUntil } from 'rxjs'
import {
  ApproveApprovalBodyParameter,
  TargetType,
  UserRight,
  WebPortalApprovalDetails,
  WebPortalApprovalService,
} from '../../../../backend-api'
import { DetailsComponent } from '../../../../shared/components/details/details.component'

@Component({
  standalone: false,
  selector: 'app-approval-details',
  templateUrl: './approval-details.component.html',
  styleUrl: './approval-details.component.css',
})
export class ApprovalDetailsComponent extends DetailsComponent<WebPortalApprovalDetails> {
  @ViewChild('commentInput') commentInput: ElementRef

  protected readonly TargetType = TargetType

  approveTooltip() {
    return this.canApprove() ? '' : this.translate.instant('APPROVALS.SELF_APPROVAL_NOT_ALLOWED')
  }

  constructor(private webPortalApprovalService: WebPortalApprovalService) {
    super()
  }

  override getFormControls(): Record<string, any> {
    return {
      comment: [this.model?.comment],
    }
  }

  override getFormValidators(): Record<string, any> {
    return {
      comment: this.formValidationService.stringValidatorSet(1024),
    }
  }

  canApprove() {
    return this.model.author == this.sessionService.getCurrentUser().userData.key
  }

  override initNewModel(): WebPortalApprovalDetails {
    return undefined
  }

  override updateModel() {
    // nothing to do
  }

  override createModel() {
    // nothing to do
  }

  override loadModel() {
    this.isLoading = true

    this.webPortalApprovalService
      .loadApproval(this.sessionService.getTenantKey(), this.modelKey)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (data: any) => {
          this.isLoading = false
          if (data == null) {
            console.log(`approval ${this.modelKey} not found`)
            this.goBack()
          }
          console.log('loaded approval', data)
          this.model = data
          this.updateFormWithModel()

          if (this.model.approvalStatus === 'PENDING' && this.sessionService.hasRight(UserRight.ApprovalsWrite)) {
            this.onEdit()
          }
        },
        error: (error: any) => {
          this.isLoading = false
          console.error(`approval ${this.modelKey} loading error`, error)
          return
        },
      })
  }

  onApproval() {
    this.isLoading = true

    const payload = { ...this.form.value, version: this.model.version } as ApproveApprovalBodyParameter

    console.log(payload)

    this.webPortalApprovalService
      .approveApproval(this.sessionService.getTenantKey(), this.modelKey, payload, 'response')
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => (this.isLoading = false))
      )
      .subscribe({
        next: (response) => {
          this.isLoading = false
          console.log('approve approval response', response)
          if (response.status === 200) {
            this.isEditMode = false
            this.loadModel()
            this.snackBar.open(
              this.translate.instant('APPROVALS.APPROVAL_APPROVED', {
                value: this.modelKey,
              }),
              this.translate.instant('MAIN.CLOSE'),
              { duration: 3000 }
            )
          }
        },
        error: (error: any) => {
          this.isLoading = false
          console.error(error)
          this.snackBar.open(
            this.translate.instant('APPROVALS.APPROVAL_APPROVE_FAILED', {
              value: this.modelKey,
            }),
            this.translate.instant('MAIN.CLOSE'),
            { duration: 3000 }
          )
        },
      })
  }

  onReject() {
    this.isLoading = true
    const payload = {
      ...this.form.value,
      version: this.model.version,
    } as ApproveApprovalBodyParameter

    this.webPortalApprovalService
      .rejectApproval(this.sessionService.getTenantKey(), this.modelKey, payload, 'response')
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => (this.isLoading = false))
      )
      .subscribe({
        next: (response) => {
          this.isLoading = false
          console.log('reject approval response', response)
          if (response.status === 200) {
            this.isEditMode = false
            this.loadModel()
            this.snackBar.open(
              this.translate.instant('APPROVALS.APPROVAL_REJECTED', {
                value: this.modelKey,
              }),
              this.translate.instant('MAIN.CLOSE'),
              { duration: 3000 }
            )
          }
        },
        error: (error: any) => {
          this.isLoading = false
          console.error(error)
          this.snackBar.open(
            this.translate.instant('APPROVALS.APPROVAL_REJECTION_FAILED', {
              value: this.modelKey,
            }),
            this.translate.instant('MAIN.CLOSE'),
            { duration: 3000 }
          )
        },
      })
  }

  getFirstElementToFocus(): ElementRef {
    return this.commentInput
  }
}
