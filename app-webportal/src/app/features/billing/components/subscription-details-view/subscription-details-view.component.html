<div class="overlay" [class.disabled]="isLoading">
  <app-details-header
    [withRefresh]="true"
    [writeRight]="UserRight.CustomersWrite"
    [labelExist]="'SUBSCRIPTIONS.DETAILS'"
    [modelKey]="this.modelKey"
    [isEditButtonReadOnly]="!isEditable"
    (onGoBack)="goBack()"
    (onEditClick)="editSubscription()"
    (onRefresh)="reloadModels()"
  >
    @if (this.sessionService.hasRight(UserRight.CustomersWrite)) {
      <div>
        <button
          mat-stroked-button
          [disabled]="!canActivateSubscription()"
          (click)="onActivateSubscription()"
          style="margin-right: 6px"
          *ngIf="canActivateSubscription()"
        >
          <mat-icon class="colored-mat-icon">play_arrow</mat-icon>
          {{ 'SUBSCRIPTIONS.ACTIVATE' | translate }}
        </button>
        <button
          mat-stroked-button
          [disabled]="!canPauseSubscription()"
          (click)="onPauseSubscription()"
          style="margin-right: 6px"
          *ngIf="canPauseSubscription()"
        >
          <mat-icon class="colored-mat-icon">pause</mat-icon>
          {{ 'SUBSCRIPTIONS.PAUSE' | translate }}
        </button>
        <button
          mat-stroked-button
          [disabled]="!isCancelable"
          (click)="onCancelSubscription()"
          *ngIf="model?.subscriptionData?.status !== 'CANCELLED'"
        >
          <mat-icon class="colored-mat-icon">cancel</mat-icon>
          {{ 'SUBSCRIPTIONS.CANCEL' | translate }}
        </button>
      </div>
    }
  </app-details-header>

  <!-- Subscription Information Section -->
  <mat-expansion-panel expanded="true">
    <mat-expansion-panel-header>
      <mat-panel-title>
        <mat-card-title>{{ 'SUBSCRIPTIONS.SUBSCRIPTION_INFORMATION' | translate }}</mat-card-title>
      </mat-panel-title>
    </mat-expansion-panel-header>

    <div class="info-container">
      <div class="info-section">
        <h3>{{ 'SUBSCRIPTIONS.SUBSCRIPTION_INFORMATION' | translate }}</h3>
        <div class="info-grid">
          <div class="info-item">
            <label>{{ 'SUBSCRIPTIONS.KEY' | translate }}:</label>
            <span>{{ model?.subscriptionData?.key }}</span>
          </div>
          <div class="info-item">
            <label>{{ 'SUBSCRIPTIONS.NAME' | translate }}:</label>
            <span>{{ model?.subscriptionData?.name }}</span>
          </div>
          <div class="info-item">
            <label>{{ 'SUBSCRIPTIONS.STATUS' | translate }}:</label>
            <span class="status-badge" [ngClass]="'status-' + model?.subscriptionData?.status?.toLowerCase()">
              {{ 'SUBSCRIPTION_STATUS.' + model?.subscriptionData?.status | translate }}
            </span>
          </div>
          <div class="info-item">
            <label>{{ 'SUBSCRIPTIONS.FREQUENCY' | translate }}:</label>
            <span>{{ 'SUBSCRIPTION_FREQUENCY.' + model?.subscriptionData?.frequency | translate }}</span>
          </div>
          <div class="info-item">
            <label>{{ 'SUBSCRIPTIONS.AMOUNT' | translate }}:</label>
            <span class="amount">{{ model?.subscriptionData?.amount | currency:model?.subscriptionData?.currency }}</span>
          </div>
          <div class="info-item">
            <label>{{ 'SUBSCRIPTIONS.START_DATE' | translate }}:</label>
            <span>{{ model?.subscriptionData?.startDate | date }}</span>
          </div>
          <div class="info-item" *ngIf="model?.subscriptionData?.endDate">
            <label>{{ 'SUBSCRIPTIONS.END_DATE' | translate }}:</label>
            <span>{{ model?.subscriptionData?.endDate | date }}</span>
          </div>
          <div class="info-item">
            <label>{{ 'SUBSCRIPTIONS.NEXT_BILLING_DATE' | translate }}:</label>
            <span>{{ model?.subscriptionData?.nextBillingDate | date }}</span>
          </div>
          <div class="info-item full-width" *ngIf="model?.subscriptionData?.description">
            <label>{{ 'SUBSCRIPTIONS.DESCRIPTION' | translate }}:</label>
            <span>{{ model?.subscriptionData?.description }}</span>
          </div>
        </div>
      </div>

      <div class="info-section" *ngIf="model?.customer">
        <h3>{{ 'SUBSCRIPTIONS.CUSTOMER_INFORMATION' | translate }}</h3>
        <div class="info-grid">
          <div class="info-item">
            <label>{{ 'CUSTOMERS.KEY' | translate }}:</label>
            <span class="with-icon">
              <mat-icon>person</mat-icon>
              <a [routerLink]="['/customers', model?.customer?.key]">{{ model?.customer?.key }}</a>
            </span>
          </div>
          <div class="info-item">
            <label>{{ 'CUSTOMERS.NAME' | translate }}:</label>
            <span>{{ model?.customer?.customerType === 'PERSON' ?
              (model?.customer?.firstName + ' ' + model?.customer?.lastName) :
              model?.customer?.companyName }}</span>
          </div>
          <!-- Email and phone properties not available in Customer model -->
        </div>
      </div>
    </div>
  </mat-expansion-panel>

  <span style="display: block; height: 5px"></span>

  <!-- Subscription Items Section -->
  <mat-expansion-panel expanded="true">
    <mat-expansion-panel-header>
      <mat-panel-title>
        <mat-card-title>{{ 'SUBSCRIPTIONS.ITEMS' | translate }}</mat-card-title>
      </mat-panel-title>
    </mat-expansion-panel-header>

    @if (this.sessionService.hasRight(UserRight.OrdersRead)) {
      <app-details-table
        #tableSubscriptionItems
        [columns]="subscriptionItemsColumns"
        initialSort="totalNetAmount"
        initialSortDirection="desc"
        (loadDataEmitter)="loadSubscriptionItems($event)"
        [exportHeader]="'SUBSCRIPTIONS.ITEMS' | translate"
      ></app-details-table>
    }
  </mat-expansion-panel>

  <span style="display: block; height: 5px"></span>

  <!-- Tabs for detailed information -->
  <mat-expansion-panel expanded="true">
    <mat-tab-group>
      <!-- Documents Tab -->
      @if (this.sessionService.hasRight(UserRight.DocumentsRead)) {
        <mat-tab [label]="'ORDERS.DOCUMENTS' | translate">
          <app-details-table
            #tableDocuments
            [columns]="documentsColumns"
            initialSort="key"
            initialSortDirection="desc"
            (loadDataEmitter)="loadDocuments($event)"
            [exportHeader]="'ORDERS.DOCUMENTS' | translate"
          ></app-details-table>
        </mat-tab>
      }

      <!-- History Tab -->
      @if (this.sessionService.hasRight(UserRight.OrdersRead)) {
        <mat-tab [label]="'ORDERS.HISTORY' | translate">
          <app-details-table
            #tableHistory
            [columns]="historyColumns"
            initialSort="createdAt"
            initialSortDirection="desc"
            (loadDataEmitter)="loadHistory($event)"
            [exportHeader]="'ORDERS.HISTORY' | translate"
          ></app-details-table>
        </mat-tab>
      }

      <!-- Balance Information Tab -->
      @if (this.sessionService.hasRight(UserRight.OpenItemsRead)) {
        <mat-tab [label]="'BALANCE_CASES.BALANCE_INFORMATION' | translate">
          <app-details-table
            #tableBalanceInformation
            [columns]="balanceInformationColumns"
            initialSort="dueDate"
            initialSortDirection="desc"
            (loadDataEmitter)="loadBalanceInformation($event)"
            [exportHeader]="'BALANCE_CASES.BALANCE_INFORMATION' | translate"
          ></app-details-table>
        </mat-tab>
      }

      <!-- Posting Records Tab -->
      @if (this.sessionService.hasRight(UserRight.PostingRecordsRead)) {
        <mat-tab [label]="'POSTING_RECORDS.TITLE' | translate">
          <app-details-table
            #tablePostingRecords
            [columns]="postingRecordsColumns"
            initialSort="postingDate"
            initialSortDirection="desc"
            (loadDataEmitter)="loadPostingRecords($event)"
            [exportHeader]="'POSTING_RECORDS.TITLE' | translate"
          ></app-details-table>
        </mat-tab>
      }

      <!-- Notes Tab -->
      @if (this.sessionService.hasRight(UserRight.OrdersRead)) {
        <mat-tab [label]="'NOTES.TITLE' | translate">
          <div class="notes-header">
            <button mat-raised-button color="primary" (click)="onAddNote()">
              <mat-icon>add</mat-icon>
              {{ 'NOTES.ADD' | translate }}
            </button>
          </div>
          <app-details-table
            #tableNotes
            [columns]="notesColumns"
            initialSort="createdAt"
            initialSortDirection="desc"
            (loadDataEmitter)="loadNotes($event)"
            [exportHeader]="'NOTES.TITLE' | translate"
          ></app-details-table>
        </mat-tab>
      }

      <!-- Properties Tab -->
      <mat-tab [label]="'MAIN.PROPERTIES' | translate">
        <app-details-table
          #tableProperties
          [columns]="propertiesColumns"
          initialSort="key"
          initialSortDirection="asc"
          (loadDataEmitter)="loadProperties($event)"
          [exportHeader]="'MAIN.PROPERTIES' | translate"
        ></app-details-table>
      </mat-tab>
    </mat-tab-group>
  </mat-expansion-panel>
</div>
