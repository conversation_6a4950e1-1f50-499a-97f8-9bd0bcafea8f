.overlay {
  position: relative;
}

.overlay.disabled {
  pointer-events: none;
  opacity: 0.6;
}

.info-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 16px;
}

.info-section {
  background: #f9f9f9;
  border-radius: 8px;
  padding: 16px;
}

.info-section h3 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 18px;
  font-weight: 500;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-item.full-width {
  grid-column: 1 / -1;
}

.info-item label {
  font-weight: 500;
  color: #666;
  font-size: 14px;
}

.info-item span {
  font-size: 16px;
  color: #333;
}

.with-icon {
  display: flex;
  align-items: center;
  gap: 8px;
}

.with-icon mat-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
  color: #666;
}

.with-icon a {
  color: #1976d2;
  text-decoration: none;
}

.with-icon a:hover {
  text-decoration: underline;
}

.status-badge {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.status-active {
  background-color: #e8f5e8;
  color: #2e7d32;
}

.status-paused {
  background-color: #fff3e0;
  color: #f57c00;
}

.status-cancelled {
  background-color: #ffebee;
  color: #d32f2f;
}

.status-expired {
  background-color: #f3e5f5;
  color: #7b1fa2;
}

.amount {
  font-weight: 600;
  color: #1976d2;
}

.notes-header {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 16px;
  padding: 16px 0 0 0;
}

.colored-mat-icon {
  color: #1976d2;
}

/* Tab styling */
.mat-tab-group {
  margin-top: 10px;
}

.mat-tab-body-content {
  padding: 20px 0;
}

/* Responsive design */
@media (max-width: 768px) {
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .info-container {
    padding: 8px;
  }
  
  .info-section {
    padding: 12px;
  }
}

/* Loading and error states */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px;
  text-align: center;
}

.error-container mat-icon {
  font-size: 48px;
  width: 48px;
  height: 48px;
  color: #f44336;
  margin-bottom: 16px;
}

.error-message {
  color: #666;
  margin-bottom: 16px;
}

/* Action buttons styling */
button[mat-stroked-button] {
  border-color: #1976d2;
  color: #1976d2;
}

button[mat-stroked-button]:hover {
  background-color: rgba(25, 118, 210, 0.04);
}

button[mat-stroked-button]:disabled {
  border-color: #ccc;
  color: #ccc;
}

/* Table styling enhancements */
app-details-table {
  margin-top: 16px;
}

/* Expansion panel styling */
mat-expansion-panel {
  margin-bottom: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

mat-expansion-panel-header {
  height: 56px;
}

mat-panel-title {
  font-weight: 500;
  color: #333;
}

/* Details header styling */
app-details-header {
  margin-bottom: 16px;
}
