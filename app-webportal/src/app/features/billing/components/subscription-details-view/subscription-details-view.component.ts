import { Component, OnInit, ViewChild, AfterViewInit, ElementRef } from '@angular/core'
import { ActivatedRoute, Router } from '@angular/router'
import { DecimalPipe } from '@angular/common'
import { MatDialog } from '@angular/material/dialog'
import { finalize, forkJoin, of, takeUntil } from 'rxjs'
import {
  BalanceCaseItemType,
  CreateNoteRequest,
  DebitCreditIndicator,
  DocumentType,
  SubscriptionStatus,
  PaymentStatus,
  PostingRecordTargetType,
  ProcessingStatus,
  UpdateNoteRequest,
  UserRight,
  WebPortalDocumentService,
  WebPortalSubscriptionService,
  WebPortalSubscriptionDetailsResponse,
  WebPortalNotesService,
  WebPortalPostingRecordService,
} from 'src/app/backend-api'
import {
  ActionColumn,
  ActionItem,
  Column,
  CustomColumn,
  DateColumn,
  DateTimeColumn,
  DetailsLinkColumn,
  EnumColumn,
  Money<PERSON><PERSON>umn,
  <PERSON><PERSON><PERSON>umn,
} from '../../../../shared/models/column'
import { DetailsTableComponent } from '../../../../shared/components/details-table/details-table.component'
import { switchMap } from 'rxjs/operators'
import { DetailsReadOnlyComponent } from '../../../../shared/components/details/details_readonly.component'
import { SessionService } from '../../../../services/session.service'
import { TranslateService } from '@ngx-translate/core'
import { LocaleService } from '../../../../services/locale.service'
import { NoteEditDialogComponent } from '../../../../shared/components/note-dialog/note-edit-dialog.component'
import { DocumentFileService } from "../../../../services/document-file.service"
import { ConfirmDialogComponent } from '../../../../shared/components/confirm-dialog/confirm-dialog.component'
import { MatSnackBar } from '@angular/material/snack-bar'

@Component({
  standalone: false,
  selector: 'app-subscription-details-view',
  templateUrl: './subscription-details-view.component.html',
  styleUrls: ['./subscription-details-view.component.css'],
})
export class SubscriptionDetailsViewComponent extends DetailsReadOnlyComponent<WebPortalSubscriptionDetailsResponse> implements AfterViewInit {
  @ViewChild('tableSubscriptionItems') tableSubscriptionItems: DetailsTableComponent
  @ViewChild('tableHistory') tableHistory: DetailsTableComponent
  @ViewChild('tablePostingRecords') tablePostingRecords: DetailsTableComponent
  @ViewChild('tableBalanceInformation') tableBalanceInformation: DetailsTableComponent
  @ViewChild('tableNotes') tableNotes: DetailsTableComponent
  @ViewChild('tableDocuments') tableDocuments: DetailsTableComponent
  @ViewChild('tableProperties') tableProperties: DetailsTableComponent

  isCancelable = false
  isActivatable = false
  isPausable = false
  isFinalizable = false
  isEditable = false

  billingAddress: any
  shippingAddress: any

  // Column definitions - will be initialized in constructor
  subscriptionItemsColumns: Column[] = []

  historyColumns: Column[] = []
  postingRecordsColumns: Column[] = []
  balanceInformationColumns: Column[] = []
  notesColumns: Column[] = []
  documentsColumns: Column[] = []
  propertiesColumns: Column[] = []

  constructor(
    private dialog: MatDialog,
    private webPortalSubscriptionService: WebPortalSubscriptionService,
    private webPortalDocumentService: WebPortalDocumentService,
    private webPortalNotesService: WebPortalNotesService,
    private webPortalPostingRecordService: WebPortalPostingRecordService,
    private decimalPipe: DecimalPipe,
    private localeService: LocaleService,
    private fileDownloadService: DocumentFileService,
    protected override snackBar: MatSnackBar,
    sessionService: SessionService,
    translate: TranslateService,
    router: Router,
    route: ActivatedRoute
  ) {
    super()
    this.initializeColumns()
  }

  private initializeColumns() {
    this.subscriptionItemsColumns = [
      new StringColumn('articleNumber', this.translate.instant('SUBSCRIPTION_ITEMS.ARTICLE_NUMBER'), true, 'articleNumber'),
      new StringColumn('name', this.translate.instant('SUBSCRIPTION_ITEMS.NAME'), true, 'name'),
      new StringColumn('description', this.translate.instant('SUBSCRIPTION_ITEMS.DESCRIPTION'), false, 'description'),
      new StringColumn('itemGroup', this.translate.instant('SUBSCRIPTION_ITEMS.ITEM_GROUP'), false, 'itemGroup'),
      new StringColumn('quantity', this.translate.instant('SUBSCRIPTION_ITEMS.QUANTITY'), true, 'quantity'),
      new MoneyColumn('unitNetAmount', this.translate.instant('SUBSCRIPTION_ITEMS.UNIT_NET_AMOUNT'), true, 'unitNetAmount', 'currency'),
      new MoneyColumn('totalNetAmount', this.translate.instant('SUBSCRIPTION_ITEMS.TOTAL_NET_AMOUNT'), true, 'totalNetAmount', 'currency'),
      new EnumColumn('debitCreditIndicator', this.translate.instant('SUBSCRIPTION_ITEMS.DEBIT_CREDIT'), true, 'debitCreditIndicator', DebitCreditIndicator, 'DebitCreditIndicator'),
    ]

    this.historyColumns = [
      new StringColumn('number', this.translate.instant('COMMON.NUMBER'), true, 'number'),
      new EnumColumn('processingStatus', this.translate.instant('COMMON.PROCESSING_STATUS'), true, 'processingStatus', ProcessingStatus, 'ProcessingStatus'),
      new DateTimeColumn('createdAt', this.translate.instant('COMMON.CREATED_AT'), true, 'createdAt'),
      new StringColumn('createdBy', this.translate.instant('COMMON.CREATED_BY'), true, 'createdBy'),
      new StringColumn('additionalInformation', this.translate.instant('COMMON.ADDITIONAL_INFORMATION'), false, 'additionalInformation'),
    ]

    this.postingRecordsColumns = [
      new StringColumn('key', this.translate.instant('POSTING_RECORDS.KEY'), true, 'key'),
      new DateColumn('postingDate', this.translate.instant('POSTING_RECORDS.POSTING_DATE'), true, 'postingDate'),
      new StringColumn('bookingAccountKey', this.translate.instant('POSTING_RECORDS.BOOKING_ACCOUNT'), true, 'bookingAccountKey'),
      new MoneyColumn('amount', this.translate.instant('POSTING_RECORDS.AMOUNT'), true, 'amount', 'currency'),
      new EnumColumn('debitCreditIndicator', this.translate.instant('POSTING_RECORDS.DEBIT_CREDIT'), true, 'debitCreditIndicator', DebitCreditIndicator, 'DebitCreditIndicator'),
      new StringColumn('description', this.translate.instant('POSTING_RECORDS.DESCRIPTION'), false, 'description'),
    ]

    this.balanceInformationColumns = [
      new StringColumn('key', this.translate.instant('BALANCE_CASES.KEY'), true, 'key'),
      new EnumColumn('itemType', this.translate.instant('BALANCE_CASES.ITEM_TYPE'), true, 'itemType', BalanceCaseItemType, 'BalanceCaseItemType'),
      new DateColumn('dueDate', this.translate.instant('BALANCE_CASES.DUE_DATE'), true, 'dueDate'),
      new MoneyColumn('originalAmount', this.translate.instant('BALANCE_CASES.ORIGINAL_AMOUNT'), true, 'originalAmount', 'currency'),
      new MoneyColumn('remainingAmount', this.translate.instant('BALANCE_CASES.REMAINING_AMOUNT'), true, 'remainingAmount', 'currency'),
      new EnumColumn('paymentStatus', this.translate.instant('BALANCE_CASES.PAYMENT_STATUS'), true, 'paymentStatus', PaymentStatus, 'PaymentStatus'),
    ]

    this.notesColumns = [
      new DateTimeColumn('createdAt', this.translate.instant('NOTES.CREATED_AT'), true, 'createdAt'),
      new StringColumn('createdBy', this.translate.instant('NOTES.CREATED_BY'), true, 'createdBy'),
      new StringColumn('content', this.translate.instant('NOTES.CONTENT'), true, 'content'),
      new ActionColumn('actions', this.translate.instant('COMMON.ACTIONS'), true, [
        new ActionItem('edit', this.translate.instant('COMMON.EDIT'), UserRight.OrdersWrite, () => true, (item: any) => this.onEditNote(item)),
        new ActionItem('delete', this.translate.instant('COMMON.DELETE'), UserRight.OrdersWrite, () => true, (item: any) => this.onDeleteNote(item)),
      ]),
    ]

    this.documentsColumns = [
      new DetailsLinkColumn('key', this.translate.instant('DOCUMENTS.KEY'), true, 'key', '/documents', 'key'),
      new StringColumn('number', this.translate.instant('DOCUMENTS.NUMBER'), true, 'number'),
      new EnumColumn('documentType', this.translate.instant('DOCUMENTS.TYPE'), true, 'documentType', DocumentType, 'DocumentType'),
      new DateColumn('documentDate', this.translate.instant('DOCUMENTS.DATE'), true, 'documentDate'),
      new MoneyColumn('totalGrossAmount', this.translate.instant('DOCUMENTS.TOTAL_AMOUNT'), true, 'totalGrossAmount', 'currency'),
      new EnumColumn('processingStatus', this.translate.instant('DOCUMENTS.STATUS'), true, 'processingStatus', ProcessingStatus, 'ProcessingStatus'),
      new ActionColumn('actions', this.translate.instant('COMMON.ACTIONS'), true, [
        new ActionItem('download', this.translate.instant('COMMON.DOWNLOAD'), UserRight.DocumentsRead, () => true, (item: any) => this.downloadDocument(item)),
      ]),
    ]

    this.propertiesColumns = [
      new StringColumn('key', this.translate.instant('MAIN.KEY'), true, 'key'),
      new StringColumn('value', this.translate.instant('MAIN.VALUE'), true, 'value'),
    ]
  }

  override ngAfterViewInit() {
    super.ngAfterViewInit()
    this.reloadSubItems()
  }

  override loadModel() {
    this.isLoading = true

    this.webPortalSubscriptionService
      .loadSubscription(this.sessionService.getTenantKey(), this.modelKey)
      .pipe(
        switchMap((data: WebPortalSubscriptionDetailsResponse) => {
          if (data == null) {
            console.log(`subscription ${this.modelKey} not found`)
            this.goBack()
            return of({ isCancelable: false, isFinalizable: false, isUpdatable: false })
          }

          console.log('loaded subscription', data)
          this.model = data
          this.updateFormWithModel()

          if (this.tableProperties) {
            this.tableProperties.reloadData()
          }

          return forkJoin({
            isCancelable: this.webPortalSubscriptionService.isSubscriptionCancelable(
              this.sessionService.getTenantKey(),
              this.modelKey
            ),
            isActivatable: this.webPortalSubscriptionService.isSubscriptionActivatable(
              this.sessionService.getTenantKey(),
              this.modelKey
            ),
            isPausable: this.webPortalSubscriptionService.isSubscriptionPausable(
              this.sessionService.getTenantKey(),
              this.modelKey
            ),
          })
        }),
        takeUntil(this.destroy$),
        finalize(() => (this.isLoading = false))
      )
      .subscribe({
        next: (result: any) => {
          this.isCancelable = result.isCancelable?.isCancelable || false
          this.isActivatable = result.isActivatable?.isActivatable || false
          this.isPausable = result.isPausable?.isPausable || false
          this.isEditable = true // TODO: Add proper subscription update check
        },
        error: (error: any) => {
          console.log(`subscription ${this.modelKey} loading error`, error)
        },
      })
  }

  reloadModels() {
    this.loadModel()
    this.reloadSubItems()
  }

  loadSubscriptionItems(event: any) {
    const observable = this.webPortalSubscriptionService.loadSubscriptionItems(
      this.sessionService.getTenantKey(),
      this.modelKey,
      event.pageIndex,
      event.pageSize,
      event.sort,
      event.direction,
      event.filterText
    )
    this.tableSubscriptionItems.loadedData(observable)
  }

  loadHistory(event: any) {
    const observable = this.webPortalSubscriptionService.loadSubscriptionHistoryEntries(
      this.sessionService.getTenantKey(),
      this.modelKey,
      event.pageIndex,
      event.pageSize,
      event.sortField,
      event.sortDirection,
      event.filterText
    )
    this.tableHistory.loadedData(observable)
  }

  loadPostingRecords(event: any) {
    const observable = this.webPortalPostingRecordService.loadPostingRecords(
      this.sessionService.getTenantKey(),
      event.pageIndex,
      event.pageSize,
      event.sortField,
      event.sortDirection,
      `subscriptionKey==${this.modelKey}` + (event.filterText ? `;${event.filterText}` : '')
    )
    this.tablePostingRecords.loadedData(observable)
  }

  loadBalanceInformation(event: any) {
    const observable = this.webPortalSubscriptionService.loadSubscriptionBalanceCases(
      this.sessionService.getTenantKey(),
      this.modelKey,
      event.pageIndex,
      event.pageSize,
      event.sortField,
      event.sortDirection,
      event.filterText
    )
    this.tableBalanceInformation.loadedData(observable)
  }

  loadNotes(event: any) {
    const observable = this.webPortalNotesService.loadNotes(
      this.sessionService.getTenantKey(),
      event.pageIndex,
      event.pageSize,
      event.sortField,
      event.sortDirection,
      `subscriptionKey==${this.modelKey}` + (event.filterText ? `;${event.filterText}` : '')
    )
    this.tableNotes.loadedData(observable)
  }

  loadDocuments(event: any) {
    const observable = this.webPortalDocumentService.loadDocuments(
      this.sessionService.getTenantKey(),
      event.pageIndex,
      event.pageSize,
      event.sort,
      event.direction,
      `subscriptionKey==${this.modelKey}`
    )
    this.tableDocuments.loadedData(observable)
  }

  loadProperties(event: any) {
    if (!this.model?.subscriptionData?.properties) {
      const observable = of({
        pageData: { totalCount: 0, pageIndex: 0 },
        items: []
      })
      this.tableProperties.loadedData(observable)
      return
    }

    const properties = Object.entries(this.model.subscriptionData.properties).map(([key, value]) => ({
      key,
      value: value || ''
    }))

    const observable = of({
      pageData: {
        totalCount: properties.length,
        pageIndex: 0,
      },
      items: properties,
    })

    this.tableProperties.loadedData(observable)
  }

  // Subscription-specific action methods
  onCancelSubscription() {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      data: {
        title: this.translate.instant('SUBSCRIPTIONS.CANCEL_SUBSCRIPTION'),
        message: this.translate.instant('SUBSCRIPTIONS.CONFIRM_CANCEL_SUBSCRIPTION', {
          value: this.model.subscriptionData.key,
        }),
      },
    })

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.cancelSubscription()
      }
    })
  }

  private cancelSubscription() {
    this.isLoading = true
    this.webPortalSubscriptionService
      .cancelSubscription(this.sessionService.getTenantKey(), this.model.subscriptionData.key, 'response')
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => (this.isLoading = false))
      )
      .subscribe({
        next: (response) => {
          this.snackBar.open(
            this.translate.instant('SUBSCRIPTIONS.SUBSCRIPTION_CANCELLED', {
              value: this.model.subscriptionData.key,
            }),
            this.translate.instant('MAIN.CLOSE'),
            {duration: 3000}
          )
          this.reloadModels()
        },
        error: (error: any) => {
          console.error(error)
          this.snackBar.open(
            this.translate.instant('SUBSCRIPTIONS.SUBSCRIPTION_CANCELLATION_FAILED', {
              value: this.model.subscriptionData.key,
            }),
            this.translate.instant('MAIN.CLOSE'),
            {duration: 3000}
          )
          this.reloadModels()
        },
      })
  }

  onActivateSubscription() {
    this.isLoading = true
    this.webPortalSubscriptionService
      .activateSubscription(this.sessionService.getTenantKey(), this.model.subscriptionData.key, 'response')
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => (this.isLoading = false))
      )
      .subscribe({
        next: (response) => {
          this.snackBar.open(
            this.translate.instant('SUBSCRIPTIONS.SUBSCRIPTION_ACTIVATED', {
              value: this.model.subscriptionData.key,
            }),
            this.translate.instant('MAIN.CLOSE'),
            {duration: 3000}
          )
          this.reloadModels()
        },
        error: (error: any) => {
          console.error(error)
          this.snackBar.open(
            this.translate.instant('SUBSCRIPTIONS.SUBSCRIPTION_ACTIVATION_FAILED', {
              value: this.model.subscriptionData.key,
            }),
            this.translate.instant('MAIN.CLOSE'),
            {duration: 3000}
          )
          this.reloadModels()
        },
      })
  }

  onPauseSubscription() {
    this.isLoading = true
    this.webPortalSubscriptionService
      .pauseSubscription(this.sessionService.getTenantKey(), this.model.subscriptionData.key, 'response')
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => (this.isLoading = false))
      )
      .subscribe({
        next: (response) => {
          this.snackBar.open(
            this.translate.instant('SUBSCRIPTIONS.SUBSCRIPTION_PAUSED', {
              value: this.model.subscriptionData.key,
            }),
            this.translate.instant('MAIN.CLOSE'),
            {duration: 3000}
          )
          this.reloadModels()
        },
        error: (error: any) => {
          console.error(error)
          this.snackBar.open(
            this.translate.instant('SUBSCRIPTIONS.SUBSCRIPTION_PAUSE_FAILED', {
              value: this.model.subscriptionData.key,
            }),
            this.translate.instant('MAIN.CLOSE'),
            {duration: 3000}
          )
          this.reloadModels()
        },
      })
  }

  // Note management methods
  onAddNote() {
    const data = {
      noteContent: '',
    }

    const dialogRef = this.dialog.open(NoteEditDialogComponent, {
      width: '400px',
      data,
    })

    dialogRef.componentInstance.submitNote.subscribe((note: any) => {
      const request: CreateNoteRequest = {
        createdBy: this.sessionService.getCurrentUser().userData.key,
        content: note.noteContent,
      }

      this.webPortalSubscriptionService
        .addSubscriptionNote(this.sessionService.getTenantKey(), this.modelKey, request)
        .subscribe({
          next: (result: any) => {
            console.log('submitted addSubscriptionNote', result)
            this.snackBar.open(
              this.translate.instant('NOTES.NOTE_CREATED', {
                value: result.key,
              }),
              this.translate.instant('MAIN.CLOSE'),
              {duration: 5000}
            )

            this.reloadModels()

            dialogRef.close()
          },
          error: (error: any) => {
            console.error('Error creating note', error)
            dialogRef.componentInstance.errorMessage = this.translate.instant('NOTES.NOTE_CREATION_FAILED')
          },
        })
    })
  }

  onEditNote(row: any) {
    const data = {
      noteContent: row.content,
    }

    const dialogRef = this.dialog.open(NoteEditDialogComponent, {
      width: '400px',
      data,
    })

    dialogRef.componentInstance.submitNote.subscribe((data: any) => {
      const request: UpdateNoteRequest = {
        key: row.key,
        version: row.version,
        content: data.noteContent,
      }

      this.webPortalSubscriptionService
        .updateSubscriptionNote(this.sessionService.getTenantKey(), this.modelKey, row.key, request, 'response')
        .subscribe({
          next: (result: any) => {
            console.log('submitted updateSubscriptionNote', result)
            this.snackBar.open(
              this.translate.instant('NOTES.NOTE_UPDATED', {
                value: result.key,
              }),
              this.translate.instant('MAIN.CLOSE'),
              {duration: 5000}
            )

            this.reloadModels()

            dialogRef.close()
          },
          error: (error: any) => {
            console.error('Error updating note', error)
            dialogRef.componentInstance.errorMessage = this.translate.instant('NOTES.NOTE_UPDATE_FAILED')
          },
        })
    })
  }

  onDeleteNote(row: any) {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      data: {
        title: this.translate.instant('MAIN.DELETE'),
        message: this.translate.instant('NOTES.NOTE_DELETION_CONFIRMATION'),
      },
    })

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.webPortalSubscriptionService
          .deleteSubscriptionNote(this.sessionService.getTenantKey(), this.modelKey, row.key, 'response')
          .pipe(
            takeUntil(this.destroy$),
            finalize(() => (this.isLoading = false))
          )
          .subscribe({
            next: (response: any) => {
              console.log('submitted deleteSubscriptionNote', response)
              this.snackBar.open(
                this.translate.instant('NOTES.NOTE_DELETED', {
                  value: row.key,
                }),
                this.translate.instant('MAIN.CLOSE'),
                {duration: 5000}
              )

              this.reloadModels()
            },
            error: (error: any) => {
              console.error('Error deleting note', error)
              this.snackBar.open(
                this.translate.instant('NOTES.NOTE_DELETION_FAILED', {
                  value: row.key,
                }),
                this.translate.instant('MAIN.CLOSE'),
                {duration: 5000}
              )
            },
          })
      }
    })
  }

  downloadDocument(document: any) {
    this.fileDownloadService.openDocumentPdf(
      null,
      this.modelKey,
      document.key
    )
  }

  editSubscription() {
    this.router.navigate(['/subscriptions', this.modelKey, 'edit'])
  }

  override goBack(): void {
    this.router.navigate(['/subscriptions'])
  }

  // Helper methods for subscription status checks
  private canCancelSubscription(): boolean {
    return this.model?.subscriptionData?.status === SubscriptionStatus.Active
  }

  private canFinalizeSubscription(): boolean {
    // Subscription finalization logic - placeholder
    return false
  }

  private canUpdateSubscription(): boolean {
    return this.model?.subscriptionData?.status !== SubscriptionStatus.Cancelled
  }

  private reloadSubItems() {
    this.tableSubscriptionItems.reloadData()
    if (this.sessionService.hasRight(UserRight.OpenItemsRead)) {
      this.tableBalanceInformation.reloadData()
    }
    this.tableNotes.reloadData()
    this.tableHistory.reloadData()
    if (this.sessionService.hasRight(UserRight.DocumentsRead)) {
      this.tableDocuments.reloadData()
    }
    if (this.sessionService.hasRight(UserRight.PostingRecordsRead)) {
      this.tablePostingRecords.reloadData()
    }
  }

  getFirstElementToFocus(): ElementRef {
    return null
  }

  // Helper methods for UI
  canActivateSubscription(): boolean {
    return this.model?.subscriptionData?.status === SubscriptionStatus.Paused
  }

  canPauseSubscription(): boolean {
    return this.model?.subscriptionData?.status === SubscriptionStatus.Active
  }
}
