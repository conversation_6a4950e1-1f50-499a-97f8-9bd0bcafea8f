#workflow-canvas {
    flex: 1;
    height: 100%;
    padding: 0;
    margin: 0;
    background-color: white;
    min-width: 0;
    transition: margin-right 0.3s ease;
}

#workflow-canvas.with-properties {
    margin-right: 0;
}

.workflow-container {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 200px);
    min-height: 700px;
    margin-top: 16px;
}

.canvas-area {
    display: flex;
    flex: 1;
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    background: white;
}

/* Properties Panel */
.properties-panel-wrapper {
    width: 320px; /* Startbreite vergrößert von 260px auf 320px */
    min-width: 250px; /* Mindestbreite */
    max-width: 600px; /* Maximale Breite */
    border-left: 1px solid #ccc;
    background: white;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
}

.properties-panel-content {
    flex: 1;
    overflow: hidden;
}

/* Resizer für Properties Panel */
.properties-panel-resizer {
    position: absolute !important;
    left: -5px !important;
    top: 0 !important;
    bottom: 0 !important;
    width: 10px !important;
    cursor: col-resize !important;
    background: transparent !important;
    z-index: 1000 !important;
    border-left: 2px solid transparent !important;
    transition: border-color 0.2s ease !important;
}

.properties-panel-resizer:hover {
    border-left: 2px solid #1976d2 !important;
}

.properties-panel-resizer:active {
    border-left: 2px solid #0d47a1 !important;
}

.properties-panel-resizer::before {
    content: '' !important;
    position: absolute !important;
    left: 50% !important;
    top: 50% !important;
    transform: translate(-50%, -50%) !important;
    width: 3px !important;
    height: 30px !important;
    background: #ccc !important;
    border-radius: 2px !important;
    opacity: 0 !important;
    transition: opacity 0.2s ease !important;
}

.properties-panel-resizer:hover::before {
    opacity: 1 !important;
}

/* Verbesserung für Workflow Canvas */
#workflow-canvas.with-properties {
    margin-right: 0;
    transition: margin-right 0.3s ease;
}

/* Button Styling */
.workflow-controls button.active {
    background-color: #e3f2fd;
    color: #1976d2;
}

.workflow-controls button {
    transition: all 0.2s ease;
}

.workflow-controls button:hover {
    background-color: #f5f5f5;
}

.hidden {
    display: none;
}

/* Workflow Controls Layout - Spezifischere Selektoren ohne !important */
.with-icon .workflow-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    gap: 16px;
    margin-top: 5px;
}

.workflow-controls .workflow-actions {
    display: flex;
    align-items: center;
    gap: 4px;
}

/* Rechte Seite: Action Icons mit vertikaler Trennlinie */
.workflow-controls .workflow-actions {
    display: flex;
    align-items: center;
    gap: 4px;
}

/* Vertikale Trennlinie vor Info-Button */
.workflow-controls .action-separator {
    width: 1px;
    height: 24px;
    background-color: #ddd;
    margin: 0 8px;
}

/* Workflow Type Selector - Spezifischere Selektoren */
.workflow-controls .workflow-type-selector {
    min-width: 240px;
    width: 240px;
    flex-shrink: 0;
}

/* Entferne den unteren Teil der Material Form Field */
.workflow-controls .workflow-type-selector ::ng-deep .mat-form-field-wrapper {
    margin-bottom: -1.25em;
}

.workflow-controls .workflow-type-selector .mat-mdc-form-field-wrapper {
    padding-bottom: 0;
}

.workflow-controls .workflow-type-selector .mat-mdc-form-field-flex {
    align-items: center;
    min-height: 36px;
}

.workflow-controls .workflow-type-selector .mat-mdc-form-field-infix {
    min-height: 36px;
    padding: 8px 0;
}

.workflow-controls .workflow-type-selector .mat-mdc-select {
    font-size: 16px;
    line-height: 1.3;
}

.workflow-controls .workflow-type-selector .mat-mdc-form-field-label {
    font-size: 14px;
    font-weight: 500;
}

.workflow.controls .workflow-type-selector .mat-mdc-select-value {
    font-size: 16px;
    font-weight: 500;
}

.workflow-controls .workflow-type-selector .mat-mdc-select-arrow {
    transform: scale(1.1);
}

/* Entferne unnötige Subscript/Hint Wrapper - Spezifischere Selektoren */
.workflow-controls .workflow-type-selector .mat-mdc-form-field-subscript-wrapper,
.workflow-controls .workflow-type-selector .mat-mdc-form-field-hint-wrapper,
.workflow-controls .workflow-type-selector .mat-mdc-form-field-hint-spacer,
.workflow-controls .workflow-type-selector .mat-mdc-form-field-bottom-align {
    display: none;
    height: 0;
    min-height: 0;
    margin: 0;
    padding: 0;
}

/* Zusätzliche spezifische Regel für hartnäckige Material Elements */
.workflow-controls .workflow-type-selector .mat-mdc-form-field-wrapper .mat-mdc-form-field-subscript-wrapper {
    display: none;
    visibility: hidden;
    height: 0;
    line-height: 0;
    overflow: hidden;
}
