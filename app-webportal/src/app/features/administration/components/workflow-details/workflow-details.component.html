<div class="with-icon">
    <div class="workflow-controls">
        <div class="workflow-left-controls">
            <mat-form-field appearance="outline" class="workflow-type-selector">
                <mat-label>{{ 'WORKFLOW.TYPE' | translate }}</mat-label>
                <mat-select [(value)]="selectedWorkflowType" (selectionChange)="onWorkflowTypeChange()">
                    @for (type of workflowTypes; track type.value) {
                        <mat-option [value]="type.value">{{ type.label | translate }}</mat-option>
                    }
                </mat-select>
            </mat-form-field>

            @if (!isLoading) {
                <button style="margin-left: 15px" mat-icon-button (click)="refreshWorkflow()"
                        title="{{ 'MAIN.RELOAD' | translate }}">
                    <mat-icon>refresh</mat-icon>
                </button>

                <button mat-icon-button (click)="toggleEditMode()" [color]="isEditMode ? 'primary' : undefined"
                        [disabled]="!sessionService.hasRight(UserRight.WorkflowWrite)"
                        title="{{ isEditMode ? 'MAIN.READ' : 'MAIN.EDIT' | translate }}">
                    <mat-icon>{{ isEditMode ? 'visibility' : 'edit' }}</mat-icon>
                </button>

                @if (isEditMode) {
                    <button mat-icon-button (click)="saveWorkflow()" color="primary"
                            [disabled]="!sessionService.hasRight(UserRight.WorkflowWrite)"
                            title="{{ 'MAIN.SAVE' | translate }}">
                        <mat-icon>check</mat-icon>
                    </button>
                    <button mat-icon-button (click)="cancelEdit()" title="{{ 'MAIN.CANCEL' | translate }}">
                        <mat-icon>close</mat-icon>
                    </button>
                }
            }
        </div>

        <div class="workflow-actions">
            @if (!isLoading) {
                <button mat-icon-button (click)="zoomOut()">
                    <mat-icon>zoom_out</mat-icon>
                </button>
                <button mat-icon-button (click)="zoomIn()">
                    <mat-icon>zoom_in</mat-icon>
                </button>
                <button mat-icon-button (click)="home()" title="Fit to Size">
                    <mat-icon>fit_screen</mat-icon>
                </button>

                <div class="action-separator"></div>

                <button mat-icon-button (click)="togglePropertiesPanel()"
                        [class.active]="showPropertiesPanel"
                        title="Properties Panel">
                    <mat-icon>info</mat-icon>
                </button>
            }
        </div>
    </div>
</div>

@if (isLoading) {
    <mat-spinner></mat-spinner>
}

@if (hasDiagramError) {
    <div class="diagram-error-message">
        <mat-icon>info</mat-icon>
        <div class="error-content">
            <h4>{{ 'WORKFLOW.NO_DIAGRAM_TITLE' | translate }}</h4>
            <p>{{ 'WORKFLOW.NO_DIAGRAM_MESSAGE' | translate }}</p>
        </div>
    </div>
}

<div class="workflow-container" [class.hidden]="hasDiagramError">
    <div class="canvas-area">
        <div id="workflow-canvas" [class.with-properties]="showPropertiesPanel"></div>

        <div class="properties-panel-wrapper" [class.hidden]="!showPropertiesPanel">
            <div class="properties-panel-resizer"
                 (mousedown)="startResize($event)"></div>
            <div id="properties-panel" class="properties-panel-content"></div>
        </div>
    </div>
</div>
