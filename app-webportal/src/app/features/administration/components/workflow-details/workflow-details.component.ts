import {AfterViewInit, Component, OnInit} from '@angular/core'
import {UserRight, WebPortalWorkflowService, WorkflowType} from 'src/app/backend-api'
import {SessionService} from '../../../../services/session.service'
import {Subject, takeUntil} from 'rxjs'
import Modeler from 'bpmn-js/lib/Modeler'
import Viewer from 'bpmn-js/lib/Viewer'
import ZoomScroll from 'diagram-js/lib/navigation/zoomscroll/ZoomScroll'
import Canvas from 'diagram-js/lib/core/Canvas'
import EventBus from 'diagram-js/lib/core/EventBus'
import {RootLike} from 'diagram-js/lib/model/Types'

// Navigation Module für Zoom-Funktionalität
import ZoomScrollModule from 'diagram-js/lib/navigation/zoomscroll'
// Move-Navigation für Canvas-Verschiebung per Drag
import MoveCanvasModule from 'diagram-js/lib/navigation/movecanvas'

// Import für das offizielle Properties Panel
import {BpmnPropertiesPanelModule, BpmnPropertiesProviderModule,} from 'bpmn-js-properties-panel'
import CustomPropertiesProvider from './custom-properties-provider'

@Component({
    standalone: false,
    selector: 'app-workflow-details',
    templateUrl: './workflow-details.component.html',
    styleUrls: ['./workflow-details.component.css'],
})
export class WorkflowDetailsComponent implements OnInit, AfterViewInit {
    protected readonly UserRight = UserRight
    private viewer: Modeler | Viewer
    private rootElement: RootLike
    private destroy$ = new Subject<void>()
    private currentXml: string // XML-Inhalt zwischenspeichern
    private isResizing = false
    private startX = 0
    private startWidth = 0
    isLoading: boolean
    hasDiagramError: boolean = false
    selectedElement: any = null
    workflowInfo: any = null
    showPropertiesPanel: boolean = false // Properties Panel initial zugeklappt
    selectedWorkflowType: WorkflowType = WorkflowType.Order
    workflowTypes = [
        {value: WorkflowType.Order, label: 'WORKFLOW.ORDER'},
        {value: WorkflowType.Subscription, label: 'WORKFLOW.SUBSCRIPTION'}
    ]
    isEditMode: boolean = false // Edit-Modus für die Komponente
    private editModeForType: WorkflowType | null = null // Welcher Type ist im Edit-Modus

    constructor(
        private webPortalWorkflowService: WebPortalWorkflowService,
        protected sessionService: SessionService
    ) {
    }

    ngOnInit(): void {
        this.loadWorkflowDefinition()
        // Entferne Material Form Field Subscript Wrapper nach dem Laden
        setTimeout(() => this.removeFormFieldSubscriptWrapper(), 100)
    }

    ngAfterViewInit(): void {
        // Zusätzlicher Aufruf nach View-Initialisierung
        setTimeout(() => this.removeFormFieldSubscriptWrapper(), 200)
    }

    private removeFormFieldSubscriptWrapper(): void {
        const subscriptWrappers = document.querySelectorAll('.workflow-type-selector .mat-mdc-form-field-subscript-wrapper')
        subscriptWrappers.forEach(wrapper => {
            if (wrapper) {
                wrapper.remove()
            }
        })
    }

    private loadWorkflowDefinition(): void {
        this.createViewerInstance()
        this.isLoading = true
        this.hasDiagramError = false
        this.webPortalWorkflowService
            .loadWorkflowDefinition(this.sessionService.getTenantKey(), this.selectedWorkflowType, 'body', false, {
                httpHeaderAccept: 'application/xml',
            })
            .pipe(takeUntil(this.destroy$))
            .subscribe({
                next: (data: any) => {
                    try {
                        this.blobToString(data as Blob).then((xml: string) => {
                            this.currentXml = xml // XML zwischenspeichern
                            this.importXML(xml)
                        })
                    } catch (err) {
                        console.log('Workflow definition could not be loaded: ' + err)
                    }
                    this.isLoading = false
                },
                error: (error: any) => {
                    console.error('Error loading workflow definition', error)
                    this.isLoading = false
                },
            })
    }

    private createViewerInstance(): void {
        if (this.viewer) {
            this.viewer.destroy()
        }

        const config = {
            container: '#workflow-canvas',
            propertiesPanel: {
                parent: '#properties-panel'
            }
        }

        if (this.isEditMode) {
            // Modeler für Bearbeitungsmodus mit AI Assistant
            this.viewer = new Modeler({
                ...config,
                additionalModules: [
                    BpmnPropertiesPanelModule,
                    BpmnPropertiesProviderModule,
                    // Custom Properties Provider nur im Edit-Modus
                    {
                        __init__: ['customPropertiesProvider'],
                        customPropertiesProvider: ['type', CustomPropertiesProvider]
                    }
                ]
            })
        } else {
            // Viewer für Lesemodus - nur Canvas-Navigation, kein AI Assistant
            this.viewer = new Viewer({
                ...config,
                additionalModules: [
                    ZoomScrollModule,
                    MoveCanvasModule
                ]
            })
        }
    }

    private importXML(xml: string): void {
        this.viewer
            .importXML(xml)
            .then(() => {
                const canvas = this.viewer.get('canvas') as Canvas
                canvas.zoom('fit-viewport')
                this.rootElement = canvas.getRootElement()
                this.extractWorkflowInfo()
                this.setupElementSelection()
                console.log('Imported XML')
            })
            .catch((e) => {
                console.error('Error importing XML: ' + e)
                if (e.toString().includes('no diagram to display')) {
                    this.hasDiagramError = true
                    console.warn(`The ${this.selectedWorkflowType} workflow definition exists but lacks visual diagram information. The workflow is functional but cannot be displayed.`)
                }
            })
    }

    private blobToString(blob: Blob): Promise<string> {
        return new Promise((resolve, reject) => {
            const reader = new FileReader()
            reader.onloadend = () => resolve(reader.result as string)
            reader.onerror = reject
            reader.readAsText(blob)
        })
    }

    private setupElementSelection(): void {
        const eventBus = this.viewer.get('eventBus') as EventBus

        eventBus.on('element.click', (event: any) => {
            this.selectedElement = event.element
            console.log('Element clicked:', event.element.id, event.element.type)
        })

        eventBus.on('selection.changed', (event: any) => {
            if (event.newSelection && event.newSelection.length > 0) {
                this.selectedElement = event.newSelection[0]
                console.log('Selection changed:', this.selectedElement.id, this.selectedElement.type)
            } else {
                this.selectedElement = null
                console.log('Selection cleared')
            }
        })

        eventBus.on('canvas.viewbox.changed', () => {
            // Reset selection when canvas view changes
            // this.selectedElement = null
        })
    }

    private extractWorkflowInfo(): void {
        try {
            const elementRegistry = this.viewer.get('elementRegistry') as any
            const rootElements = elementRegistry.filter((element: any) => {
                return element.businessObject && element.businessObject.$type === 'bpmn:Process'
            })

            if (rootElements.length > 0) {
                const process = rootElements[0].businessObject
                this.workflowInfo = {
                    id: process.id || 'Unknown ID',
                    name: process.name || process.id || 'Unnamed Workflow'
                }
            } else {
                this.workflowInfo = {
                    id: 'Unknown ID',
                    name: 'Unknown Workflow'
                }
            }
        } catch (error) {
            console.error('Error extracting workflow info:', error)
            this.workflowInfo = {
                id: 'Unknown ID',
                name: 'Unknown Workflow'
            }
        }
    }

    zoomIn() {
        try {
            const zoomScroll = this.viewer.get('zoomScroll') as ZoomScroll
            zoomScroll.stepZoom(1)
        } catch (error) {
            console.warn('ZoomScroll service not available:', error)
        }
    }

    zoomOut() {
        try {
            const zoomScroll = this.viewer.get('zoomScroll') as ZoomScroll
            zoomScroll.stepZoom(-1)
        } catch (error) {
            console.warn('ZoomScroll service not available:', error)
        }
    }

    home() {
        try {
            const canvas = this.viewer.get('canvas') as Canvas
            canvas.setRootElement(this.rootElement)
            canvas.zoom('fit-viewport')
        } catch (error) {
            console.warn('Canvas service not available:', error)
        }
    }

    onWorkflowTypeChange(): void {
        // Edit-Modus zurücksetzen wenn Workflow-Type geändert wird
        if (this.isEditMode) {
            this.isEditMode = false
            this.editModeForType = null
        }
        this.loadWorkflowDefinition()
    }

    togglePropertiesPanel(): void {
        this.showPropertiesPanel = !this.showPropertiesPanel
    }

    toggleEditMode(): void {
        this.isEditMode = !this.isEditMode

        if (this.isEditMode) {
            // Edit-Modus für aktuellen Type aktivieren
            this.editModeForType = this.selectedWorkflowType
        } else {
            // Edit-Modus deaktivieren
            this.editModeForType = null
        }

        // Viewer-Instanz neu erstellen basierend auf dem Modus
        if (this.currentXml) {
            this.createViewerInstance()
            this.importXML(this.currentXml)
        }
    }

    refreshWorkflow(): void {
        this.loadWorkflowDefinition()
    }

    saveWorkflow(): void {
        if (this.isEditMode && this.viewer instanceof Modeler && this.editModeForType === this.selectedWorkflowType) {
            this.viewer.saveXML().then((result: any) => {
                console.log('Saved XML for', this.selectedWorkflowType, ':', result.xml)

                // Send the XML to the server
                this.webPortalWorkflowService
                    .updateWorkflowDefinition(
                        this.sessionService.getTenantKey(),
                        this.selectedWorkflowType,
                        result.xml,
                        'body',
                        false,
                        {httpHeaderAccept: 'application/xml'}
                    )
                    .pipe(takeUntil(this.destroy$))
                    .subscribe({
                        next: (response: any) => {
                            console.log('Workflow definition successfully updated', response)
                            this.currentXml = result.xml

                            // Edit-Modus nach erfolgreichem Speichern beenden
                            this.isEditMode = false
                            this.editModeForType = null
                            this.createViewerInstance()
                            this.importXML(this.currentXml)
                        },
                        error: (error: any) => {
                            console.error('Error updating workflow definition:', error)
                            // TODO: Show user-friendly error message
                        }
                    })
            }).catch((err: any) => {
                console.error('Error saving XML:', err)
            })
        }
    }

    cancelEdit(): void {
        if (this.isEditMode && this.editModeForType === this.selectedWorkflowType) {
            // Zurück zum ursprünglichen XML und Edit-Modus beenden
            this.isEditMode = false
            this.editModeForType = null
            this.createViewerInstance()
            this.importXML(this.currentXml)
        }
    }

    // Resizing-Funktionalität für Properties Panel
    startResize(event: MouseEvent): void {
        event.preventDefault()
        this.isResizing = true
        this.startX = event.clientX

        const panelElement = document.querySelector('.properties-panel-wrapper') as HTMLElement
        if (panelElement) {
            this.startWidth = panelElement.offsetWidth
        }

        // Event Listeners für Mouse Move und Mouse Up
        document.addEventListener('mousemove', this.onMouseMove.bind(this))
        document.addEventListener('mouseup', this.onMouseUp.bind(this))

        // Cursor für gesamte Seite während Resize
        document.body.style.cursor = 'col-resize'
        document.body.style.userSelect = 'none'
    }

    private onMouseMove(event: MouseEvent): void {
        if (!this.isResizing) return

        const deltaX = this.startX - event.clientX // Umgekehrte Richtung (linke Seite)
        const newWidth = this.startWidth + deltaX

        // Grenzen einhalten
        const minWidth = 250
        const maxWidth = 600
        const constrainedWidth = Math.min(Math.max(newWidth, minWidth), maxWidth)

        const panelElement = document.querySelector('.properties-panel-wrapper') as HTMLElement
        if (panelElement) {
            panelElement.style.width = `${constrainedWidth}px`
        }
    }

    private onMouseUp(): void {
        if (!this.isResizing) return

        this.isResizing = false

        // Event Listeners entfernen
        document.removeEventListener('mousemove', this.onMouseMove.bind(this))
        document.removeEventListener('mouseup', this.onMouseUp.bind(this))

        // Cursor zurücksetzen
        document.body.style.cursor = ''
        document.body.style.userSelect = ''
    }
}
