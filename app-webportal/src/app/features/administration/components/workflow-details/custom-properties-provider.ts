/**
 * AI-Chat Properties Provider für BPMN-Modell Diskussion und Anpassung
 */
export default class CustomPropertiesProvider {

  constructor(propertiesPanel: any) {
    propertiesPanel.registerProvider(this);
  }

  getGroups(element: any) {
    return (groups: any[]) => {
      // Nur AI Chat Gruppe hinzufügen
      groups.push(this.createAIChatGroup(element));

      return groups;
    };
  }

  private createAIChatGroup(element: any) {
    return {
      id: 'ai-chat',
      label: '🤖 AI Assistant',
      open: true, // Standardmäßig ausgeklappt - das wichtigste Feature
      entries: [
        {
          id: 'ai-chat-interface',
          html: `
            <div class="ai-chat-container">
              <!-- Chat Messages Area -->
              <div class="ai-chat-messages" id="ai-chat-messages-${element.id}">
                <div class="ai-message system">
                  <div class="message-avatar">🤖</div>
                  <div class="message-content">
                    Hallo! Ich bin dein BPMN AI-Assistent. Ich kann dir bei der Optimierung deines Workflows helfen. 
                    Frag mich etwas über dieses Element oder bitte mich, Änderungen vorzunehmen.
                  </div>
                </div>
              </div>
              
              <!-- Quick Action Buttons -->
              <div class="ai-quick-actions">
                <button type="button" class="ai-quick-btn" onclick="window.sendAIQuickMessage('${element.id}', 'Optimiere dieses Element')">
                  ⚡ Optimieren
                </button>
                <button type="button" class="ai-quick-btn" onclick="window.sendAIQuickMessage('${element.id}', 'Erkläre mir dieses Element')">
                  ❓ Erklären
                </button>
                <button type="button" class="ai-quick-btn" onclick="window.sendAIQuickMessage('${element.id}', 'Schlage Verbesserungen vor')">
                  💡 Verbessern
                </button>
              </div>
              
              <!-- Chat Input Area -->
              <div class="ai-chat-input-area">
                <div class="ai-input-container">
                  <textarea 
                    id="ai-chat-input-${element.id}"
                    class="ai-chat-input" 
                    placeholder="Frage den AI-Assistenten über dieses ${this.getElementDisplayName(element.type)}..."
                    rows="2"
                    onkeydown="if(event.key==='Enter'&&!event.shiftKey){event.preventDefault();window.sendAIMessage('${element.id}');}"
                  ></textarea>
                  <button 
                    type="button" 
                    class="ai-send-btn"
                    onclick="window.sendAIMessage('${element.id}')"
                  >
                    📤
                  </button>
                </div>
                <div class="ai-context-info">
                  <small>💡 Kontext: ${this.getElementDisplayName(element.type)} "${element.businessObject?.name || element.id}"</small>
                </div>
              </div>
              
              <!-- AI Status -->
              <div class="ai-status" id="ai-status-${element.id}" style="display: none;">
                <div class="ai-thinking">
                  <span class="ai-spinner">🔄</span>
                  AI denkt nach...
                </div>
              </div>
            </div>
          `,
          get: (element: any) => {
            // Initialisiere AI Chat für dieses Element
            setTimeout(() => this.initializeAIChat(element), 100);
            return {};
          },
          set: (element: any, values: any) => {
            // AI Chat State wird nicht über normale Properties gespeichert
          }
        }
      ]
    };
  }

  private getElementDisplayName(elementType: string): string {
    const displayMap: { [key: string]: string } = {
      'bpmn:StartEvent': 'Start Event',
      'bpmn:EndEvent': 'End Event',
      'bpmn:Task': 'Task',
      'bpmn:ServiceTask': 'Service Task',
      'bpmn:UserTask': 'User Task',
      'bpmn:Gateway': 'Gateway',
      'bpmn:ExclusiveGateway': 'Exclusive Gateway',
      'bpmn:ParallelGateway': 'Parallel Gateway'
    };
    return displayMap[elementType] || 'Element';
  }

  private initializeAIChat(element: any) {
    // Globale Funktionen für AI Chat registrieren
    (window as any).sendAIMessage = (elementId: string) => {
      this.sendAIMessage(elementId);
    };

    (window as any).sendAIQuickMessage = (elementId: string, message: string) => {
      this.sendAIQuickMessage(elementId, message);
    };
  }

  private async sendAIMessage(elementId: string) {
    const inputElement = document.getElementById(`ai-chat-input-${elementId}`) as HTMLTextAreaElement;
    const messagesContainer = document.getElementById(`ai-chat-messages-${elementId}`);
    const statusElement = document.getElementById(`ai-status-${elementId}`);

    if (!inputElement || !messagesContainer || !inputElement.value.trim()) return;

    const userMessage = inputElement.value.trim();
    inputElement.value = '';

    // User Message hinzufügen
    this.addChatMessage(messagesContainer, 'user', userMessage);

    // AI Status anzeigen
    if (statusElement) {
      statusElement.style.display = 'block';
    }

    try {
      // Hier würde der echte API-Call zum Backend/Vertex AI erfolgen
      const aiResponse = await this.callAIService(elementId, userMessage);

      // AI Response hinzufügen
      this.addChatMessage(messagesContainer, 'ai', aiResponse.message);

      // Wenn AI Änderungen vorschlägt, diese anwenden
      if (aiResponse.changes) {
        this.applyAIChanges(elementId, aiResponse.changes);
      }

    } catch (error) {
      this.addChatMessage(messagesContainer, 'ai', 'Entschuldigung, ich konnte deine Anfrage nicht verarbeiten. Bitte versuche es später erneut.');
    } finally {
      // AI Status verstecken
      if (statusElement) {
        statusElement.style.display = 'none';
      }
    }
  }

  private async sendAIQuickMessage(elementId: string, message: string) {
    const messagesContainer = document.getElementById(`ai-chat-messages-${elementId}`);
    if (!messagesContainer) return;

    // Quick Message als User Message hinzufügen
    this.addChatMessage(messagesContainer, 'user', message);

    // AI Response simulieren (in Realität API-Call)
    setTimeout(() => {
      let response = '';
      switch (message) {
        case 'Optimiere dieses Element':
          response = 'Ich habe das Element analysiert. Hier sind meine Optimierungsvorschläge:\n\n• Timeout auf 60 Sekunden erhöhen\n• Priority auf "high" setzen\n• Retry-Mechanismus aktivieren\n\nSoll ich diese Änderungen anwenden?';
          break;
        case 'Erkläre mir dieses Element':
          response = 'Dieses Element ist ein wichtiger Bestandteil deines Workflows. Es führt spezifische Geschäftslogik aus und kann durch entsprechende Konfiguration optimiert werden.';
          break;
        case 'Schlage Verbesserungen vor':
          response = 'Basierend auf Best Practices empfehle ich:\n\n1. Klarere Benennung des Elements\n2. Hinzufügung von Fehlerbehandlung\n3. Performance-Monitoring aktivieren\n\nMöchtest du mehr Details zu einem dieser Punkte?';
          break;
        default:
          response = 'Interessante Frage! Lass mich das für dich analysieren...';
      }
      this.addChatMessage(messagesContainer, 'ai', response);
    }, 1500);
  }

  private addChatMessage(container: Element, type: 'user' | 'ai', message: string) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `ai-message ${type}`;

    const avatar = type === 'ai' ? '🤖' : '👤';
    messageDiv.innerHTML = `
      <div class="message-avatar">${avatar}</div>
      <div class="message-content">${message.replace(/\n/g, '<br>')}</div>
    `;

    container.appendChild(messageDiv);
    container.scrollTop = container.scrollHeight;
  }

  private async callAIService(elementId: string, message: string): Promise<{message: string, changes?: any}> {
    // TODO: Hier echter API-Call zum Backend/Vertex AI
    // const response = await fetch('/api/ai/workflow-chat', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify({ elementId, message, workflowXml: this.getCurrentWorkflowXml() })
    // });

    // Simulation für jetzt
    await new Promise(resolve => setTimeout(resolve, 2000));

    return {
      message: `AI-Antwort zu: "${message}"\n\nIch habe deine Anfrage analysiert und kann folgende Optimierungen vorschlagen...`,
      changes: {
        properties: {
          'businessPriority': 'high',
          'timeoutDuration': '60'
        }
      }
    };
  }

  private applyAIChanges(elementId: string, changes: any) {
    // TODO: Hier würden die AI-vorgeschlagenen Änderungen am BPMN-Modell angewendet
    console.log('Applying AI changes for element', elementId, changes);
  }
}

// Dependency Injection für bpmn-js
(CustomPropertiesProvider as any).$inject = ['propertiesPanel'];
