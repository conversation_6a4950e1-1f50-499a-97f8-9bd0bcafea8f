<div class="overlay" [class.disabled]="isLoading">
  <app-details-header
    labelNew="ROLES.NEW_ROLE"
    labelExist="ROLES.ROLE"
    [modelKey]="this.modelKey"
    [writeRight]="UserRight.RolesWrite"
    [isNew]="isNew"
    [isEditMode]="isEditMode"
    (onGoBack)="goBack()"
    (onEditClick)="onEdit()"
    (onSaveClick)="onSave()"
    (onUndoClick)="onUndo()"
  ></app-details-header>

  <mat-card>
    <mat-card-header>
      <mat-card-title>Details</mat-card-title>
    </mat-card-header>
    <mat-card-content>
      @if (!isEditMode) {
        <div class="form-row">
          <label>Key:</label>
          <span>{{ model?.key }}</span>
        </div>
        <div class="form-row">
          <label>{{ 'ROLES.NAME' | translate }}:</label>
          <span>{{ model?.name }}</span>
        </div>
      } @else {
        <form [formGroup]="form" (ngSubmit)="onSave()" class="vertical-form">
          <mat-form-field>
            <mat-label>Key</mat-label>
            <input matInput formControlName="key" type="text" #keyInput />
            <mat-error>{{ this.formValidationService.checkValuesForErrorsIn(form.get('key')) }}</mat-error>
          </mat-form-field>
          <mat-form-field>
            <mat-label>{{ 'ROLES.NAME' | translate }}</mat-label>
            <input matInput formControlName="name" type="text" />
            <mat-error>{{ this.formValidationService.checkValuesForErrorsIn(form.get('name')) }}</mat-error>
          </mat-form-field>
        </form>
      }
    </mat-card-content>
  </mat-card>

  <mat-spinner *ngIf="isLoading" class="spinner"></mat-spinner>

  <span style="display: block; height: 5px"></span>

  <mat-expansion-panel expanded="true">
    <mat-expansion-panel-header>
      <mat-panel-title>
        <mat-card-title>{{ 'ROLES.RIGHTS' | translate }}</mat-card-title>
      </mat-panel-title>
    </mat-expansion-panel-header>

    <table mat-table [dataSource]="rightsDataSource">
      <ng-container matColumnDef="select">
        <th mat-header-cell *matHeaderCellDef style="width: 10px">
          <mat-checkbox
            [disabled]="!isEditMode"
            (change)="$event ? toggleAllRows() : null"
            [checked]="selection.hasValue() && isAllSelected()"
            [indeterminate]="selection.hasValue() && !isAllSelected()"
          >
          </mat-checkbox>
        </th>
        <td mat-cell *matCellDef="let row" style="width: 10px">
          <mat-checkbox
            [disabled]="!isEditMode"
            (click)="$event.stopPropagation()"
            (change)="$event ? selection.toggle(row) : null"
            [checked]="selection.isSelected(row)"
          >
          </mat-checkbox>
        </td>
      </ng-container>

      <ng-container matColumnDef="displayName">
        <th mat-header-cell *matHeaderCellDef>{{ 'ROLES.NAME' | translate }}</th>
        <td mat-cell *matCellDef="let item">{{ item.displayName }}</td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedRightsColumns; sticky: true"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedRightsColumns"></tr>
    </table>
  </mat-expansion-panel>
</div>
