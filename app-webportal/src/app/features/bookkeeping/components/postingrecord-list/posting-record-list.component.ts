import { Component, ViewChild } from '@angular/core'
import { PostingRecordTargetType, WebPortalPostingRecordService } from '../../../../backend-api'
import { SessionService } from '../../../../services/session.service'
import { OverviewTableComponent } from '../../../../shared/components/overview-table/overview-table.component'
import {
  Column,
  DateColumn,
  DetailsLinkColumn,
  EnumColumn,
  StringColumn,
  TargetLinkColumn,
} from '../../../../shared/models/column'
import { TranslateService } from '@ngx-translate/core'

@Component({
  standalone: false,
  selector: 'app-posting-record-list',
  templateUrl: './posting-record-list.component.html',
  styleUrl: './posting-record-list.component.css',
})
export class PostingRecordListComponent {
  @ViewChild(OverviewTableComponent)
  overviewTableComponent: OverviewTableComponent

  allColumns: Column[] = [
    new DetailsLinkColumn('key', 'Key', true, 'key', '/posting-records', 'key'),
    new StringColumn('number', this.translate.instant('POSTING_RECORDS.NUMBER'), true, 'number'),
    new EnumColumn(
      'targetType',
      this.translate.instant('POSTING_RECORDS.TARGET_TYPE'),
      true,
      'targetType',
      PostingRecordTargetType,
      'PostingRecordTargetType'
    ),
    new TargetLinkColumn(
      'targetKey',
      this.translate.instant('POSTING_RECORDS.TARGET_KEY'),
      true,
      'targetKey',
      'targetType'
    ),
    new EnumColumn(
      'documentType',
      this.translate.instant('POSTING_RECORDS.DOCUMENT_TYPE'),
      true,
      'documentType',
      DocumentType,
      'DocumentType'
    ),
    new DetailsLinkColumn(
      'orderKey',
      this.translate.instant('POSTING_RECORDS.ORDER'),
      true,
      'orderKey',
      '/orders',
      'orderKey'
    ),
    new DateColumn('bookingDate', this.translate.instant('POSTING_RECORDS.BOOKING_DATE'), true, 'bookingDate'),
  ]

  constructor(
    private webPortalPostingRecordService: WebPortalPostingRecordService,
    private sessionService: SessionService,
    private translate: TranslateService
  ) {}

  loadPostingRecords(event: any) {
    const observable = this.webPortalPostingRecordService.loadPostingRecords(
      this.sessionService.getTenantKey(),
      event.pageIndex,
      event.pageSize,
      event.sort,
      event.direction,
      event.filterText
    )
    this.overviewTableComponent.loadedData(observable)
  }
}
