<app-details-header
  labelNew="MANUAL_BOOKING.NEW_MANUAL_BOOKING"
  [immutableEntity]="true"
  [modelKey]="this.modelKey"
  [writeRight]="UserRight.BusinessSegmentsWrite"
  [isNew]="isNew"
  [isEditMode]="isEditMode"
  [withBack]="false"
  (onSaveClick)="onSave()"
  (onUndoClick)="onUndo()"
></app-details-header>

<mat-card>
  <mat-card-header>
    <mat-card-title>{{ 'MANUAL_BOOKING.NEW_ENTRY' | translate }}</mat-card-title>
  </mat-card-header>
  <mat-card-content>
    <form [formGroup]="form" (ngSubmit)="onSave()" class="vertical-form">
      <mat-form-field>
        <mat-label>{{ 'MANUAL_BOOKING.BOOKING_TEXT' | translate }}</mat-label>
        <input matInput formControlName="bookingText" type="text" #bookingTextInput />
        <mat-error>{{ this.formValidationService.checkValuesForErrorsIn(form.get('bookingText')) }}</mat-error>
      </mat-form-field>
      <app-money-input
        [form]="form"
        [amountControlName]="'amount'"
        [currencyControlName]="'currency'"
      ></app-money-input>
      <mat-form-field>
        <mat-label>{{ 'MANUAL_BOOKING.DEBIT_ACCOUNT' | translate }}</mat-label>
        <input matInput formControlName="debitAccount" type="text" />
        <mat-error>{{ this.formValidationService.checkValuesForErrorsIn(form.get('debitAccount')) }}</mat-error>
      </mat-form-field>
      <mat-form-field>
        <mat-label>{{ 'MANUAL_BOOKING.DEBIT_POSTING_KEY' | translate }}</mat-label>
        <input matInput formControlName="debitPostingKey" type="text" />
        <mat-error>{{ this.formValidationService.checkValuesForErrorsIn(form.get('debitPostingKey')) }}</mat-error>
      </mat-form-field>
      <mat-form-field>
        <mat-label>{{ 'MANUAL_BOOKING.CREDIT_ACCOUNT' | translate }}</mat-label>
        <input matInput formControlName="creditAccount" type="text" />
        <mat-error>{{ this.formValidationService.checkValuesForErrorsIn(form.get('creditAccount')) }}</mat-error>
      </mat-form-field>
      <mat-form-field>
        <mat-label>{{ 'MANUAL_BOOKING.CREDIT_POSTING_KEY' | translate }}</mat-label>
        <input matInput formControlName="creditPostingKey" type="text" />
        <mat-error>{{ this.formValidationService.checkValuesForErrorsIn(form.get('creditPostingKey')) }}</mat-error>
      </mat-form-field>
    </form>
  </mat-card-content>
</mat-card>
