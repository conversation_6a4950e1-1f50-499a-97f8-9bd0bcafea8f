.currency-amount-align {
  text-align: right;
}

.icon-middle {
  vertical-align: middle;
}

.mat-mdc-list-item-icon {
  color: rgba(0, 0, 0, 0.54);
}

.card-container {
  display: flex;
  justify-content: flex-end;
}

.mat-list .mat-list-item {
  padding: 0;
  margin: 0;
}

.mdc-list-item.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines {
  height: 60px;
  /* Adjust as needed */
  padding: 0;
  /* Adjust as needed */
  margin: 0;
  /* Adjust as needed */
}

.icon-middle {
  vertical-align: middle;
}

.mat-mdc-form-field {
  font-size: 14px;
  max-width: 400px;
}

.table-header-filter-label {
  font-size: 14px;
}

.filter-panel {
  display: flex;
  justify-content: space-between;
}

.inner-div {
  flex-grow: 1;
  margin-left: 10px;
}
