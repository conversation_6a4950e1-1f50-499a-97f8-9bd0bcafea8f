import { Component, ViewChild } from '@angular/core'
import {
  BookingRuleType,
  DebitCreditIndicator,
  UserRight,
  WebPortalBookingRuleListEntry,
  WebPortalBookingRuleService,
} from 'src/app/backend-api'
import { SessionService } from '../../../../services/session.service'
import { Column, CustomColumn, DetailsLinkColumn, EnumColumn } from '../../../../shared/models/column'
import { OverviewTableComponent } from '../../../../shared/components/overview-table/overview-table.component'
import { TranslateService } from '@ngx-translate/core'

@Component({
  standalone: false,
  selector: 'app-booking-rule-list',
  templateUrl: './booking-rule-list.component.html',
  styleUrls: ['./booking-rule-list.component.css'],
})
export class BookingRuleListComponent {
  @ViewChild(OverviewTableComponent)
  overviewTableComponent: OverviewTableComponent

  allColumns: Column[] = [
    new DetailsLinkColumn(
      'name',
      this.translate.instant('BOOKING_RULES.NAME'),
      true,
      'name',
      '/booking-rules',
      'key'
    ),
    new EnumColumn(
      'bookingRuleType',
      this.translate.instant('BOOKING_RULES.BOOKING_RULE_TYPE'),
      true,
      'bookingRuleType',
      BookingRuleType,
      'BookingRuleType'
    ),
    new EnumColumn(
      'debitCreditIndicator',
      this.translate.instant('BOOKING_RULES.DEBIT_CREDIT'),
      true,
      'debitCreditIndicator',
      DebitCreditIndicator,
      'DebitCreditIndicator'
    ),
    new CustomColumn(
      'debitAccountNumber',
      this.translate.instant('BOOKING_RULES.DEBIT_ACCOUNT_NUMBER'),
      true,
      'debitAccountNumber',
      false,
      null,
      (row) => this.formatDebitAccountNumber(row)
    ),
    new CustomColumn(
      'creditAccountNumber',
      this.translate.instant('BOOKING_RULES.CREDIT_ACCOUNT_NUMBER'),
      true,
      'creditAccountNumber',
      false,
      null,
      (row) => this.formatCreditAccountNumber(row)
    ),
  ]

  constructor(
    private webPortalBookingRuleService: WebPortalBookingRuleService,
    private sessionService: SessionService,
    private translate: TranslateService
  ) {}

  formatDebitAccountNumber(data: WebPortalBookingRuleListEntry): string {
    return data.debitAccountNumber != 0
      ? data.debitAccountNumber.toString()
      : this.translate.instant('BOOKING_RULES.DEBTOR_ACCOUNT')
  }

  formatCreditAccountNumber(data: WebPortalBookingRuleListEntry): string {
    return data.creditAccountNumber != 0
      ? data.creditAccountNumber.toString()
      : this.translate.instant('BOOKING_RULES.DEBTOR_ACCOUNT')
  }

  loadBookingRules(event: any) {
    const observable = this.webPortalBookingRuleService.loadBookingRules(
      this.sessionService.getTenantKey(),
      event.pageIndex,
      event.pageSize,
      event.sort,
      event.direction,
      event.filterText
    )
    this.overviewTableComponent.loadedData(observable)
  }

  protected readonly UserRight = UserRight
}
