import { Injectable } from '@angular/core'
import {
  WebPortalOrderService,
  WebPortalOrdersPagedResponse,
  WebPortalSubscriptionService,
  WebPortalSubscriptionsPagedResponse,
  SubscriptionStatus
} from '../../../backend-api'
import { SessionService } from '../../../services/session.service'
import { map, Observable, Subject, takeUntil, forkJoin, of, switchMap } from 'rxjs'
import { format, subDays, startOfMonth, endOfMonth } from 'date-fns'

export interface SubscriptionMetrics {
  active: number
  paused: number
  cancelled: number
  total: number
}

export interface RevenueMetrics {
  currentMRR: number
  previousMRR: number
  growth: number
  averageRevenuePerSubscription: number
  previousARPU: number
  arpuGrowth: number
  annualRunRate: number
  previousARR: number
  arrGrowth: number
}

export interface CustomerMetrics {
  newCustomers: number
  churnedCustomers: number
  totalCustomers: number
  growthRate: number
}

@Injectable({
  providedIn: 'root',
})
export class DashboardService {
  destroy$ = new Subject<void>()

  constructor(
    private webPortalOrderService: WebPortalOrderService,
    private sessionService: SessionService
  ) {}

  getOrdersCountFor7Days(): Observable<Map<string, number>> {
    const result: Map<string, number> = new Map<string, number>()

    for (let i = 6; i >= 0; i--) {
      const date = subDays(new Date(), i)
      const formattedDate = format(date, 'yyyy-MM-dd')
      result.set(formattedDate, 0)
    }

    const date7DaysAgo = subDays(new Date(), 7)
    const formattedDate7DaysAgo = format(date7DaysAgo, 'yyyy-MM-dd')
    const rsqlFilter = `order_date=ge=${formattedDate7DaysAgo}`

    return this.webPortalOrderService
      .loadOrders(this.sessionService.getTenantKey(), 0, 100, 'order_date', 'asc', rsqlFilter)
      .pipe(
        takeUntil(this.destroy$),
        map((data: WebPortalOrdersPagedResponse) => {
          data.items.forEach((order) => {
            const orderDate = format(new Date(order.orderData.orderDate), 'yyyy-MM-dd')
            const count = result.get(orderDate) ?? 0
            result.set(orderDate, count + 1)
          })
          return result
        })
      )
  }

  getRevenueFor7Days(): Observable<Map<string, number>> {
    const result: Map<string, number> = new Map<string, number>()

    for (let i = 6; i >= 0; i--) {
      const date = subDays(new Date(), i)
      const formattedDate = format(date, 'yyyy-MM-dd')
      result.set(formattedDate, 0)
    }

    const date7DaysAgo = subDays(new Date(), 7)
    const formattedDate = format(date7DaysAgo, 'yyyy-MM-dd')
    const rsqlFilter = `order_date=ge=${formattedDate}`

    return this.webPortalOrderService
      .loadOrders(this.sessionService.getTenantKey(), 0, 100, 'order_date', 'asc', rsqlFilter)
      .pipe(
        takeUntil(this.destroy$),
        map((data: WebPortalOrdersPagedResponse) => {
          data.items.forEach((order) => {
            const orderDate = format(new Date(order.orderData.orderDate), 'yyyy-MM-dd')
            const existingRevenue = result.get(orderDate) ?? 0
            result.set(orderDate, existingRevenue + order.orderData.totalAmount)
          })
          return result
        })
      )
  }

  getSubscriptionMetrics(webPortalSubscriptionService: WebPortalSubscriptionService): Observable<SubscriptionMetrics> {
    const tenantKey = this.sessionService.getTenantKey()

    return webPortalSubscriptionService
      .loadSubscriptions(tenantKey, 0, 1000, 'createdAt', 'desc', '')
      .pipe(
        takeUntil(this.destroy$),
        map((data: WebPortalSubscriptionsPagedResponse) => {
          const active = data.items.filter(sub => sub.status === SubscriptionStatus.Active).length
          const paused = data.items.filter(sub => sub.status === SubscriptionStatus.Paused).length
          const cancelled = data.items.filter(sub => sub.status === SubscriptionStatus.Cancelled).length
          const total = data.items.length

          return {
            active,
            paused,
            cancelled,
            total
          }
        })
      )
  }

  getMonthlyRecurringRevenue(webPortalSubscriptionService: WebPortalSubscriptionService): Observable<RevenueMetrics> {
    const tenantKey = this.sessionService.getTenantKey()

    return webPortalSubscriptionService
      .loadSubscriptions(tenantKey, 0, 1000, 'createdAt', 'desc', 'status==ACTIVE')
      .pipe(
        takeUntil(this.destroy$),
        map((data: WebPortalSubscriptionsPagedResponse) => {
          const activeSubscriptions = data.items.filter(sub => sub.status === SubscriptionStatus.Active)

          const currentMRR = activeSubscriptions.reduce((sum, sub) => {
              // Convert to monthly amount based on frequency
              let monthlyAmount = sub.amount
              switch (sub.frequency) {
                case 'DAILY':
                  monthlyAmount = sub.amount * 30
                  break
                case 'WEEKLY':
                  monthlyAmount = sub.amount * 4.33
                  break
                case 'QUARTERLY':
                  monthlyAmount = sub.amount / 3
                  break
                case 'YEARLY':
                  monthlyAmount = sub.amount / 12
                  break
                // MONTHLY is already correct
              }
              return sum + monthlyAmount
            }, 0)

          // Calculate additional metrics
          const totalActiveSubscriptions = activeSubscriptions.length
          const averageRevenuePerSubscription = totalActiveSubscriptions > 0 ? currentMRR / totalActiveSubscriptions : 0
          const annualRunRate = currentMRR * 12

          // For demo purposes, simulate previous month data (in real app, this would be historical data)
          const previousMRR = currentMRR * 0.95 // Simulate 5% growth
          const growth = previousMRR > 0 ? ((currentMRR - previousMRR) / previousMRR) * 100 : 0

          // Calculate previous ARPU and growth
          const previousActiveSubscriptions = Math.max(1, totalActiveSubscriptions * 0.92) // Simulate subscription growth
          const previousARPU = previousActiveSubscriptions > 0 ? previousMRR / previousActiveSubscriptions : 0
          const arpuGrowth = previousARPU > 0 ? ((averageRevenuePerSubscription - previousARPU) / previousARPU) * 100 : 0

          // Calculate previous ARR and growth
          const previousARR = previousMRR * 12
          const arrGrowth = previousARR > 0 ? ((annualRunRate - previousARR) / previousARR) * 100 : 0

          return {
            currentMRR,
            previousMRR,
            growth,
            averageRevenuePerSubscription,
            previousARPU,
            arpuGrowth,
            annualRunRate,
            previousARR,
            arrGrowth
          }
        })
      )
  }

  getUpcomingBillingEvents(webPortalSubscriptionService: WebPortalSubscriptionService): Observable<any[]> {
    const tenantKey = this.sessionService.getTenantKey()
    const nextMonth = format(new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), 'yyyy-MM-dd')

    return webPortalSubscriptionService
      .loadSubscriptions(tenantKey, 0, 10, 'nextBillingDate', 'asc', `status==ACTIVE;nextBillingDate=le=${nextMonth}`)
      .pipe(
        takeUntil(this.destroy$),
        map((data: WebPortalSubscriptionsPagedResponse) => {
          // If no events found in next 30 days, get the next 5 upcoming events regardless of date
          if (data.items.length === 0) {
            return webPortalSubscriptionService
              .loadSubscriptions(tenantKey, 0, 5, 'nextBillingDate', 'asc', 'status==ACTIVE')
              .pipe(
                map((fallbackData: WebPortalSubscriptionsPagedResponse) => {
                  return fallbackData.items.map(sub => ({
                    subscriptionName: sub.name,
                    customerName: sub.customerName || 'Unknown Customer',
                    amount: sub.amount,
                    currency: sub.currency,
                    nextBillingDate: sub.nextBillingDate
                  }))
                })
              )
          }

          return of(data.items.map(sub => ({
            subscriptionName: sub.name,
            customerName: sub.customerName || 'Unknown Customer',
            amount: sub.amount,
            currency: sub.currency,
            nextBillingDate: sub.nextBillingDate
          })))
        }),
        // Flatten the nested observable if we had to make a fallback call
        switchMap(result => result instanceof Observable ? result : of(result))
      )
  }
}
