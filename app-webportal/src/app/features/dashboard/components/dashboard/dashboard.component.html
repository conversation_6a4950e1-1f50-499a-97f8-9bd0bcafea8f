<div class="dashboard-container">
  <div #primary class="dashboard-color" style="display: none"></div>

  <div class="dashboard-grid">
    <!-- Orders Chart Widget -->
    @if (sessionService.hasRight(UserRight.OrdersRead)) {
      <div class="dashboard-card">
        <div class="card-header">
          <h4 class="card-title">{{ 'DASHBOARD.ORDERS' | translate }}</h4>
          <p class="card-subtitle">{{ 'DASHBOARD.LAST_7_DAYS' | translate }}</p>
          <mat-icon class="card-icon">shopping_cart</mat-icon>
        </div>
        <div class="card-content">
          <echarts
            #orderChart
            class="echart-diagram"
            [options]="orderChartOption"
            (chartInit)="onOrderChartInit($event)"
          ></echarts>
        </div>
      </div>
    }

    <!-- Revenue Chart Widget -->
    @if (sessionService.hasRight(UserRight.OrdersRead)) {
      <div class="dashboard-card">
        <div class="card-header">
          <h4 class="card-title">{{ 'DASHBOARD.REVENUE' | translate }}</h4>
          <p class="card-subtitle">{{ 'DASHBOARD.LAST_7_DAYS' | translate }}</p>
          <mat-icon class="card-icon">trending_up</mat-icon>
        </div>
        <div class="card-content">
          <echarts
            #revenueChart
            class="echart-diagram"
            [options]="revenueChartOption"
            (chartInit)="onRevenueChartInit($event)"
          ></echarts>
        </div>
      </div>
    }

    <!-- Subscription Overview Widget -->
    @if (sessionService.hasRight(UserRight.CustomersRead)) {
      <div class="dashboard-card">
        <div class="card-header">
          <h4 class="card-title">{{ 'DASHBOARD.SUBSCRIPTION_OVERVIEW' | translate }}</h4>
          <p class="card-subtitle">{{ 'DASHBOARD.THIS_MONTH' | translate }}</p>
          <mat-icon class="card-icon">subscriptions</mat-icon>
        </div>
        <div class="card-content">
          @if (isSubscriptionMetricsLoading) {
            <div class="loading-spinner">
              <mat-spinner diameter="40"></mat-spinner>
            </div>
          } @else {
            <div class="metrics-grid">
              <div class="metric-item clickable-metric" (click)="onForwardToActiveSubscriptions()">
                <div class="metric-number">{{ subscriptionMetrics.active }}</div>
                <div class="metric-text">{{ 'DASHBOARD.ACTIVE_SUBSCRIPTIONS' | translate }}</div>
              </div>
              <div class="metric-item clickable-metric" (click)="onForwardToPausedSubscriptions()">
                <div class="metric-number">{{ subscriptionMetrics.paused }}</div>
                <div class="metric-text">{{ 'DASHBOARD.PAUSED_SUBSCRIPTIONS' | translate }}</div>
              </div>
              <div class="metric-item clickable-metric" (click)="onForwardToCancelledSubscriptions()">
                <div class="metric-number">{{ subscriptionMetrics.cancelled }}</div>
                <div class="metric-text">{{ 'DASHBOARD.CANCELLED_SUBSCRIPTIONS' | translate }}</div>
              </div>
              <div class="metric-item clickable-metric" (click)="onForwardToAllSubscriptions()">
                <div class="metric-number">{{ subscriptionMetrics.total }}</div>
                <div class="metric-text">{{ 'DASHBOARD.TOTAL_SUBSCRIPTIONS' | translate }}</div>
              </div>
            </div>
          }
        </div>
      </div>
    }

    <!-- Monthly Recurring Revenue Widget -->
    @if (sessionService.hasRight(UserRight.CustomersRead)) {
      <div class="dashboard-card">
        <div class="card-header">
          <h4 class="card-title">{{ 'DASHBOARD.MONTHLY_RECURRING_REVENUE' | translate }}</h4>
          <p class="card-subtitle">{{ 'DASHBOARD.THIS_MONTH' | translate }}</p>
          <mat-icon class="card-icon">{{ getCurrencyIcon() }}</mat-icon>
        </div>
        <div class="card-content">
          @if (isMrrLoading) {
            <div class="loading-spinner">
              <mat-spinner diameter="40"></mat-spinner>
            </div>
          } @else {
            <div class="metrics-grid">
              <div class="metric-item clickable-metric" (click)="onForwardToActiveSubscriptions()">
                <div class="metric-number">{{ formatCurrency(revenueMetrics.currentMRR) }}</div>
                <div class="metric-text">{{ 'DASHBOARD.MRR' | translate }}</div>
                <div class="metric-change" [class]="getGrowthClass(revenueMetrics.growth)">
                  <mat-icon>{{ getGrowthIcon(revenueMetrics.growth) }}</mat-icon>
                  {{ formatPercentage(revenueMetrics.growth) }}
                </div>
              </div>
              <div class="metric-item clickable-metric" (click)="onForwardToActiveSubscriptions()">
                <div class="metric-number">{{ formatCurrency(revenueMetrics.averageRevenuePerSubscription) }}</div>
                <div class="metric-text">{{ 'DASHBOARD.ARPU' | translate }}</div>
                <div class="metric-change" [class]="getGrowthClass(revenueMetrics.arpuGrowth)">
                  <mat-icon>{{ getGrowthIcon(revenueMetrics.arpuGrowth) }}</mat-icon>
                  {{ formatPercentage(revenueMetrics.arpuGrowth) }}
                </div>
              </div>
              <div class="metric-item clickable-metric" (click)="onForwardToActiveSubscriptions()">
                <div class="metric-number">{{ formatCurrency(revenueMetrics.annualRunRate) }}</div>
                <div class="metric-text">{{ 'DASHBOARD.ARR' | translate }}</div>
                <div class="metric-change" [class]="getGrowthClass(revenueMetrics.arrGrowth)">
                  <mat-icon>{{ getGrowthIcon(revenueMetrics.arrGrowth) }}</mat-icon>
                  {{ formatPercentage(revenueMetrics.arrGrowth) }}
                </div>
              </div>
            </div>
          }
        </div>
      </div>
    }

    <!-- Open Tasks Widget -->
    <div class="dashboard-card">
      <div class="card-header">
        <h4 class="card-title">{{ 'DASHBOARD.OPEN_TASKS' | translate }}</h4>
        <p class="card-subtitle">{{ 'DASHBOARD.THIS_MONTH' | translate }}</p>
        <mat-icon class="card-icon">task</mat-icon>
      </div>
      <div class="card-content">
        @if (isOpenTasksLoading) {
          <div class="loading-spinner">
            <mat-spinner diameter="40"></mat-spinner>
          </div>
        } @else {
          @if (openPaymentTransactionCount === 0 && openItemCount === 0 && openApprovalCount === 0) {
            <div class="empty-state">
              <mat-icon>check_circle</mat-icon>
              <p>{{ 'DASHBOARD.NO_OPEN_TASKS' | translate }}</p>
            </div>
          } @else {
            <div class="metrics-grid">
              @if (openPaymentTransactionCount > 0) {
                <div class="metric-item" (click)="onForwardToPaymentTransactions()" style="cursor: pointer;">
                  <div class="metric-number">{{ openPaymentTransactionCount }}</div>
                  <div class="metric-text">{{ 'DASHBOARD.OPEN_PAYMENT_TRANSACTIONS' | translate }}</div>
                </div>
              }
              @if (openItemCount > 0) {
                <div class="metric-item" (click)="onForwardToOpenItems()" style="cursor: pointer;">
                  <div class="metric-number">{{ openItemCount }}</div>
                  <div class="metric-text">{{ 'DASHBOARD.OPEN_ITEMS' | translate }}</div>
                </div>
              }
              @if (openApprovalCount > 0) {
                <div class="metric-item" (click)="onForwardToOpenApproval()" style="cursor: pointer;">
                  <div class="metric-number">{{ openApprovalCount }}</div>
                  <div class="metric-text">{{ 'DASHBOARD.OPEN_APPROVALS' | translate }}</div>
                </div>
              }
            </div>
          }
        }
      </div>
    </div>

    <!-- Reports Widget -->
    @if (sessionService.hasRight(UserRight.ReportsRead)) {
      <div class="dashboard-card">
        <div class="card-header">
          <h4 class="card-title">{{ 'DASHBOARD.REPORTS' | translate }}</h4>
          <p class="card-subtitle">{{ 'DASHBOARD.LAST_7_DAYS' | translate }}</p>
          <mat-icon class="card-icon">assessment</mat-icon>
        </div>
        <div class="card-content">
          @if (isReportsLoading) {
            <div class="loading-spinner">
              <mat-spinner diameter="40"></mat-spinner>
            </div>
          } @else {
            @if (reports.length === 0) {
              <div class="empty-state">
                <mat-icon>description</mat-icon>
                <p>{{ 'DASHBOARD.NO_REPORTS' | translate }}</p>
              </div>
            } @else {
              <ul class="task-list">
                @for (report of reports; track report.key) {
                  <li class="task-item">
                    <div class="task-info">
                      <div class="task-name">{{ report.key }}</div>
                      <div class="task-date">{{ report.reportDate | date:'shortDate' }}</div>
                    </div>
                  </li>
                }
              </ul>
            }
          }
        </div>
      </div>
    }

    <!-- Upcoming Billing Events Widget -->
    @if (sessionService.hasRight(UserRight.CustomersRead)) {
      <div class="dashboard-card">
        <div class="card-header">
          <h4 class="card-title">{{ 'DASHBOARD.UPCOMING_BILLING' | translate }}</h4>
          <p class="card-subtitle">{{ 'DASHBOARD.NEXT_BILLING_DATES' | translate }}</p>
          <mat-icon class="card-icon">schedule</mat-icon>
        </div>
        <div class="card-content">
          @if (isUpcomingBillingLoading) {
            <div class="loading-spinner">
              <mat-spinner diameter="40"></mat-spinner>
            </div>
          } @else {
            @if (upcomingBillingEvents.length === 0) {
              <div class="empty-state">
                <mat-icon>event_available</mat-icon>
                <p>No upcoming billing events</p>
              </div>
            } @else {
              <ul class="task-list">
                @for (event of upcomingBillingEvents; track event.subscriptionName) {
                  <li class="task-item">
                    <div class="task-info">
                      <div class="task-name">{{ event.subscriptionName }}</div>
                      <div class="task-date">{{ event.customerName }} • {{ event.nextBillingDate | date:'shortDate' }}</div>
                    </div>
                    <div class="task-amount">{{ formatCurrency(event.amount, event.currency) }}</div>
                  </li>
                }
              </ul>
            }
          }
        </div>
      </div>
    }
  </div>
</div>
