import { Component, ElementRef, OnInit, ViewChild, <PERSON><PERSON><PERSON>roy, HostListener } from '@angular/core'
import { DashboardService, SubscriptionMetrics, RevenueMetrics } from '../../services/dashboard.service'
import type { EChartsOption } from 'echarts'
import type { EChartsType } from 'echarts/core'
import moment from 'moment'
import { Router } from '@angular/router'
import { CurrencyPipe } from '@angular/common'
import {
  ApprovalStatus,
  PaymentTransactionAssignmentStatus,
  UserRight,
  WebPortalApprovalService,
  WebPortalBalanceCaseService,
  WebPortalPaymentTransactionService,
  WebPortalReportService,
  WebPortalReportsListEntry,
  WebPortalReportsPagedResponse,
  WebPortalSubscriptionService,
} from '../../../../backend-api'
import { SessionService } from '../../../../services/session.service'
import { LocaleService } from '../../../../services/locale.service'
import { forkJoin, of, Subject, takeUntil } from 'rxjs'
import { format, subDays } from 'date-fns'

@Component({
  standalone: false,
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss'],
})
export class DashboardComponent implements OnInit, OnDestroy {
  protected readonly UserRight = UserRight

  @ViewChild('primary') primary: ElementRef

  orderChartOption: EChartsOption = this.getEmptyChartOption()
  revenueChartOption: EChartsOption = this.getEmptyChartOption()
  mrrChartOption: EChartsOption = this.getEmptyChartOption()

  orderChartInstance: EChartsType = null
  revenueChartInstance: EChartsType = null
  mrrChartInstance: EChartsType = null

  isOpenTasksLoading = false
  isSubscriptionMetricsLoading = false
  isMrrLoading = false
  isUpcomingBillingLoading = false

  openPaymentTransactionCount = 0
  openItemCount = 0
  openApprovalCount = 0

  subscriptionMetrics: SubscriptionMetrics = { active: 0, paused: 0, cancelled: 0, total: 0 }
  revenueMetrics: RevenueMetrics = {
    currentMRR: 0,
    previousMRR: 0,
    growth: 0,
    averageRevenuePerSubscription: 0,
    previousARPU: 0,
    arpuGrowth: 0,
    annualRunRate: 0,
    previousARR: 0,
    arrGrowth: 0
  }
  upcomingBillingEvents: any[] = []

  isReportsLoading = false

  reports: WebPortalReportsListEntry[] = []

  destroy$ = new Subject<void>()

  constructor(
    private dashboardService: DashboardService,
    private router: Router,
    protected sessionService: SessionService,
    private webPortalPaymentTransactionService: WebPortalPaymentTransactionService,
    private webPortalBalanceCaseService: WebPortalBalanceCaseService,
    private webPortalApprovalService: WebPortalApprovalService,
    private webPortalReportService: WebPortalReportService,
    private webPortalSubscriptionService: WebPortalSubscriptionService,
    private localeService: LocaleService,
    private currencyPipe: CurrencyPipe
  ) {}

  ngOnInit(): void {
    this.loadReports()
    this.loadOpenTasks()
    this.loadSubscriptionMetrics()
    this.loadRevenueMetrics()
    this.loadUpcomingBillingEvents()
  }

  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    // Resize charts when window is resized
    setTimeout(() => {
      if (this.orderChartInstance) {
        this.orderChartInstance.resize()
      }
      if (this.revenueChartInstance) {
        this.revenueChartInstance.resize()
      }
      if (this.mrrChartInstance) {
        this.mrrChartInstance.resize()
      }
    }, 100)
  }

  ngOnDestroy(): void {
    this.destroy$.next()
    this.destroy$.complete()
  }

  private loadReports(): void {
    const tenantKey = this.sessionService.getTenantKey()
    if (this.sessionService.hasRight(UserRight.ReportsRead)) {
      this.isReportsLoading = true

      const date7DaysAgo = subDays(new Date(), 7)
      const formattedDate7DaysAgo = format(date7DaysAgo, 'yyyy-MM-dd')
      const rsqlFilter = `report_date=ge=${formattedDate7DaysAgo}`

      this.webPortalReportService
        .loadReports(tenantKey, 0, 6, 'report_date', 'desc', rsqlFilter)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (data: WebPortalReportsPagedResponse) => {
            this.reports = data.items
            this.isReportsLoading = false
          },
          error: (error: any) => {
            console.error(error)
            this.isReportsLoading = false
          },
        })
    }
  }

  private loadOpenTasks(): void {
    const tenantKey = this.sessionService.getTenantKey()
    this.isOpenTasksLoading = true

    const paymentTransactionsCount$ = this.sessionService.hasRight(UserRight.PaymentTransactionsRead)
      ? this.webPortalPaymentTransactionService.countOpenPaymentTransactions(tenantKey)
      : of(0)

    const openItemsCount$ = this.sessionService.hasRight(UserRight.OpenItemsRead)
      ? this.webPortalBalanceCaseService.countOpenItems(tenantKey)
      : of(0)

    const openApprovalCount$ = this.sessionService.hasRight(UserRight.ApprovalsRead)
      ? this.webPortalApprovalService.countOpenApprovals(tenantKey)
      : of(0)

    forkJoin([paymentTransactionsCount$, openItemsCount$, openApprovalCount$])
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: ([paymentTransactionsCount, openItemsCount, openApprovalCount]) => {
          this.openPaymentTransactionCount = paymentTransactionsCount
          this.openItemCount = openItemsCount
          this.openApprovalCount = openApprovalCount
          this.isOpenTasksLoading = false
        },
        error: (error: any) => {
          console.error(error)
          this.isOpenTasksLoading = false
        },
      })
  }

  private loadSubscriptionMetrics(): void {
    if (!this.sessionService.hasRight(UserRight.CustomersRead)) {
      return
    }

    this.isSubscriptionMetricsLoading = true
    this.dashboardService.getSubscriptionMetrics(this.webPortalSubscriptionService)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (metrics) => {
          this.subscriptionMetrics = metrics
          this.isSubscriptionMetricsLoading = false
        },
        error: (error) => {
          console.error('Error loading subscription metrics:', error)
          this.isSubscriptionMetricsLoading = false
        }
      })
  }

  private loadRevenueMetrics(): void {
    if (!this.sessionService.hasRight(UserRight.CustomersRead)) {
      return
    }

    this.isMrrLoading = true
    this.dashboardService.getMonthlyRecurringRevenue(this.webPortalSubscriptionService)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (metrics) => {
          this.revenueMetrics = metrics
          this.isMrrLoading = false
        },
        error: (error) => {
          console.error('Error loading revenue metrics:', error)
          this.isMrrLoading = false
        }
      })
  }

  private loadUpcomingBillingEvents(): void {
    if (!this.sessionService.hasRight(UserRight.CustomersRead)) {
      return
    }

    this.isUpcomingBillingLoading = true
    this.dashboardService.getUpcomingBillingEvents(this.webPortalSubscriptionService)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (events) => {
          this.upcomingBillingEvents = events
          this.isUpcomingBillingLoading = false
        },
        error: (error) => {
          console.error('Error loading upcoming billing events:', error)
          this.isUpcomingBillingLoading = false
        }
      })
  }

  getGrowthClass(growth: number): string {
    if (growth > 0) return 'positive'
    if (growth < 0) return 'negative'
    return 'neutral'
  }

  getGrowthIcon(growth: number): string {
    if (growth > 0) return 'trending_up'
    if (growth < 0) return 'trending_down'
    return 'trending_flat'
  }

  formatCurrency(amount: number, currency?: string): string {
    // Use provided currency, or tenant's currency from session, fallback to EUR if not available
    const currencyToUse = currency || this.sessionService.getCurrentUser()?.userData?.currency || 'EUR'

    // Use CurrencyPipe with proper locale support
    const formatted = this.currencyPipe.transform(
      amount,
      currencyToUse,
      'symbol',
      '1.0-0', // No decimal places for dashboard metrics
      this.localeService.getLocale()
    )

    return formatted || amount.toString()
  }

  formatPercentage(value: number): string {
    return `${value > 0 ? '+' : ''}${value.toFixed(1)}%`
  }

  getCurrencyIcon(): string {
    const currency = this.sessionService.getCurrentUser()?.userData?.currency || 'EUR'

    // Map currencies to Material Icons
    switch (currency) {
      case 'USD':
        return 'attach_money'
      case 'GBP':
        return 'currency_pound'
      case 'JPY':
        return 'currency_yen'
      case 'EUR':
      default:
        return 'euro_symbol'
    }
  }

  onForwardToPaymentTransactions() {
    this.router
      .navigate(['/payment-transactions'], {
        queryParams: { filter: 'assignmentStatus!=' + PaymentTransactionAssignmentStatus.CompletelyAssigned },
      })
      .catch(console.error)
  }

  onForwardToOpenItems() {
    this.router.navigate(['/open-items']).catch(console.error)
  }

  onForwardToSubscriptions(status?: string) {
    const queryParams = status ? { filter: `status==${status}` } : {}
    this.router.navigate(['/subscriptions'], { queryParams }).catch(console.error)
  }

  onForwardToActiveSubscriptions() {
    this.onForwardToSubscriptions('ACTIVE')
  }

  onForwardToPausedSubscriptions() {
    this.onForwardToSubscriptions('PAUSED')
  }

  onForwardToCancelledSubscriptions() {
    this.onForwardToSubscriptions('CANCELLED')
  }

  onForwardToAllSubscriptions() {
    this.onForwardToSubscriptions()
  }

  onForwardToOpenApproval() {
    this.router
      .navigate(['/approvals'], {
        queryParams: { filter: 'approvalStatus==' + ApprovalStatus.Pending },
      })
      .catch(console.error)
  }

  onOrderChartInit(ec: EChartsType) {
    this.orderChartInstance = ec

    this.orderChartInstance.showLoading()

    // Make chart responsive
    this.orderChartInstance.resize()

    this.dashboardService.getOrdersCountFor7Days().subscribe((countFor7Days) => {
      this.orderChartOption = this.getOrderChartData(countFor7Days)
      this.orderChartInstance.hideLoading()
      this.orderChartInstance.on('click', (params) => {
        this.router.navigate(['/orders'], { queryParams: { filter: 'orderDate==' + params.name } }).catch(console.error)
      })
    })
  }

  onRevenueChartInit(ec: EChartsType) {
    this.revenueChartInstance = ec

    this.revenueChartInstance.showLoading()

    // Make chart responsive
    this.revenueChartInstance.resize()

    this.dashboardService.getRevenueFor7Days().subscribe((countFor7Days) => {
      this.revenueChartOption = this.getRevenueChartData(countFor7Days)

      this.revenueChartInstance.hideLoading()

      this.revenueChartInstance.on('click', (params) => {
        this.router
          .navigate(['/orders'], {
            queryParams: { filter: 'orderDate==' + params.name },
          })
          .catch(console.error)
      })
    })
  }

  private getOrderChartData(countFor7Days: Map<string, number>): EChartsOption {
    const data: { name: string; value: number }[] = Array.from(countFor7Days, ([name, value]) => ({ name, value }))

    const primaryColor = getComputedStyle(this.primary.nativeElement).color

    return {
      tooltip: {
        trigger: 'axis',
        formatter: (params) => `${moment(params[0].name).format('DD/MM/YYYY')}: ${params[0].value}`,
      },
      color: [primaryColor],
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        data: data.map((item) => item.name),
        axisLabel: {
          formatter: (value) => moment(value).format('DD/MM'),
        },
      },
      yAxis: {
        type: 'value',
        minInterval: 1,
      },
      series: [
        {
          data,
          type: 'bar',
          barWidth: '60%',
        },
      ],
    }
  }

  private getRevenueChartData(revenueFor7Days: Map<string, number>): EChartsOption {
    const data: { name: string; value: number }[] = Array.from(revenueFor7Days, ([name, value]) => ({ name, value }))

    const primaryColor = getComputedStyle(this.primary.nativeElement).color

    return {
      tooltip: {
        trigger: 'axis',
        formatter: (params) => `${moment(params[0].name).format('DD/MM/YYYY')}: ${this.formatCurrency(params[0].value)}`,
      },
      color: [primaryColor],
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        data: data.map((item) => item.name),
        axisLabel: {
          formatter: (value) => moment(value).format('DD/MM'),
        },
      },
      yAxis: {
        type: 'value',
        minInterval: 1,
        axisLabel: {
          formatter: (value) => this.formatCurrency(value),
        },
      },
      series: [
        {
          data,
          type: 'bar',
          barWidth: '60%',
        },
      ],
    }
  }

  private getEmptyChartOption(): EChartsOption {
    return {
      tooltip: {
        trigger: 'axis',
      },
      xAxis: {
        data: [],
      },
      yAxis: {
        type: 'value',
        minInterval: 1,
      },
      series: [
        {
          data: [],
          type: 'bar',
        },
      ],
    }
  }
}
