@use '@angular/material' as mat;

@include mat.core();

.dashboard-container {
  padding: 24px;
  min-height: calc(100vh - 64px);
}

.dashboard-header {
  margin-bottom: 32px;

  h3 {
    font-size: 2.5rem;
    font-weight: 300;
    color: #333333;
    margin: 0;
  }
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  grid-auto-rows: minmax(320px, auto);
  gap: 24px;
  margin-bottom: 24px;
  width: 100%;
  box-sizing: border-box;

  .dashboard-card {
    animation: fadeInUp 0.6s ease-out;
    animation-fill-mode: both;
    min-height: 320px;
    display: flex;
    flex-direction: column;
    width: 100%;
    box-sizing: border-box;
    overflow: hidden;

    &:nth-child(1) { animation-delay: 0.1s; }
    &:nth-child(2) { animation-delay: 0.2s; }
    &:nth-child(3) { animation-delay: 0.3s; }
    &:nth-child(4) { animation-delay: 0.4s; }
    &:nth-child(5) { animation-delay: 0.5s; }
    &:nth-child(6) { animation-delay: 0.6s; }
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dashboard-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.2);

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
  }
}

.card-header {
  padding: 16px 20px 12px;
  background: var(--dashboard-card-header-background, #3f51b5);
  color: var(--dashboard-card-header-text, white);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
  }

  .card-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin: 0 0 4px 0;
    position: relative;
    z-index: 1;
    line-height: 1.3;
  }

  .card-subtitle {
    font-size: 0.8rem;
    opacity: 0.9;
    margin: 0;
    position: relative;
    z-index: 1;
    line-height: 1.2;
  }

  .card-icon {
    position: absolute;
    top: 16px;
    right: 20px;
    font-size: 1.75rem;
    width: 1.75rem;
    height: 1.75rem;
    opacity: 0.8;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.card-content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  width: 100%;
  min-width: 0;
  flex: 1;
  min-height: 200px;
  box-sizing: border-box;
  overflow: hidden;
}

.metric-value {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--dashboard-metric-value-color, #1e40af);
  margin: 0 0 8px 0;
  line-height: 1;
}

.metric-label {
  font-size: 0.875rem;
  color: var(--dashboard-metric-label-color, #6b7280);
  margin: 0 0 16px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.metric-change {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;

  &.positive {
    background: rgba(16, 185, 129, 0.1);
    color: #10b981;
  }

  &.negative {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
  }

  &.neutral {
    background: rgba(107, 114, 128, 0.1);
    color: #6b7280;
  }
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 16px;
  margin-top: 8px;
  width: 100%;
}

.metric-item {
  text-align: center;
  padding: 20px 16px;
  background: rgba(30, 64, 175, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(30, 64, 175, 0.1);
  transition: all 0.2s ease;
  cursor: pointer;
  min-height: 80px;
  display: flex;
  flex-direction: column;
  justify-content: center;

  &:hover {
    background: rgba(30, 64, 175, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(30, 64, 175, 0.15);
  }

  .metric-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e40af;
    margin: 0 0 4px 0;
  }

  .metric-text {
    font-size: 0.75rem;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
  }

  &:hover .metric-number {
    color: #1d4ed8;
  }

  .metric-change {
    display: inline-flex;
    align-items: center;
    gap: 2px;
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 0.65rem;
    font-weight: 600;
    margin-top: 4px;

    &.positive {
      background: rgba(16, 185, 129, 0.1);
      color: #10b981;
    }

    &.negative {
      background: rgba(239, 68, 68, 0.1);
      color: #ef4444;
    }

    &.neutral {
      background: rgba(107, 114, 128, 0.1);
      color: #6b7280;
    }

    mat-icon {
      font-size: 0.75rem;
      width: 0.75rem;
      height: 0.75rem;
    }
  }
}

.echart-diagram {
  width: 100% !important;
  height: 250px !important;
  border-radius: 8px;
  min-width: 0;
  flex: 1;
  min-height: 200px;
  max-width: 100%;
  box-sizing: border-box;
}

.task-list {
  list-style: none;
  padding: 0;
  margin: 0;

  .task-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);

    &:last-child {
      border-bottom: none;
    }

    .task-info {
      flex: 1;

      .task-name {
        font-weight: 500;
        color: #374151;
        margin: 0 0 4px 0;
      }

      .task-date {
        font-size: 0.75rem;
        color: #6b7280;
      }
    }

    .task-amount {
      font-weight: 600;
      color: #1e40af;
    }
  }
}

.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #6b7280;

  mat-icon {
    font-size: 3rem;
    width: 3rem;
    height: 3rem;
    margin-bottom: 16px;
    opacity: 0.5;
  }

  p {
    margin: 0;
    font-size: 0.875rem;
  }
}

// Responsive design
@media (max-width: 768px) {
  .dashboard-container {
    padding: 16px;
  }

  .dashboard-grid {
    grid-template-columns: 1fr;
    grid-auto-rows: minmax(280px, auto);
    gap: 16px;

    .dashboard-card {
      min-height: 280px;
      width: 100%;
      max-width: 100%;
    }
  }

  .card-header {
    padding: 14px 16px 10px;

    .card-title {
      font-size: 1rem;
    }

    .card-icon {
      right: 16px;
      font-size: 1.5rem;
      width: 1.5rem;
      height: 1.5rem;
      top: 14px;
    }
  }

  .card-content {
    padding: 16px;
    min-height: 180px;
  }

  .metric-value {
    font-size: 2rem;
  }

  .echart-diagram {
    height: 200px !important;
    min-height: 150px;
  }

  .metrics-grid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 12px;
  }
}

.clickable-metric {
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(30, 64, 175, 0.1);
  }
}
