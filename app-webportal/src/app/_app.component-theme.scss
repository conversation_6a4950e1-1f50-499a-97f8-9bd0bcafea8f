@use 'sass:map';
@use '@angular/material' as mat;

@mixin color($theme) {
  .menu-toggler {
    color: mat.get-theme-color($theme, accent, 200);
    font-weight: 500;
    font-size: 24px;
  }

  .user-icon {
    display: inline-block;
    width: 24px;
    height: 24px;
    background-color: #fff;
    color: #000;
    text-align: center;
    line-height: 24px;
    border-radius: 50%;
    font-size: 14px;
  }

  .outer-div {
    flex-grow: 1;
    background-color: #dadada;
  }

  .mat-sidenav {
    background-color: mat.get-theme-color($theme, primary, default) !important;
    padding: 0 !important;
    margin-top: 0 !important;
  }

  .mat-sidenav .mdc-list {
    padding: 0 !important;
  }

  .mat-sidenav .dashboard-entry {
    background-color: mat.get-theme-color($theme, primary, 400);
    padding: 0 !important;
    border-radius: 0 !important;
    height: 48px !important;
    border-top: 0;
    border-bottom: 2px;
    border-style: solid;
    border-left: 0;
    border-right: 0;
    border-color: mat.get-theme-color($theme, primary, default);
  }

  .mat-sidenav .dashboard-entry .mat-mdc-list-item-unscoped-content {
    font-size: 18px;
    font-weight: 400;
    display: flex;
    align-items: center;
    height: 100%;
  }

  .mat-sidenav mat-icon {
    color: mat.get-theme-color($theme, accent, 200) !important;
  }

  .colored-mat-icon {
    color: rgb(128, 128, 128) !important;
  }

  .mat-sidenav .mat-expansion-panel-header {
    padding-left: 8px !important;
    background-color: mat.get-theme-color($theme, primary, 400) !important;
    color: mat.get-theme-color($theme, primary, 500-contrast);
    border-top: 2px;
    border-bottom: 2px;
    border-style: solid;
    border-left: 0;
    border-right: 0;
    border-color: mat.get-theme-color($theme, primary, default);
    font-size: 18px;
    font-weight: 400;
    letter-spacing: var(--mdc-list-list-item-label-text-tracking);
  }

  .mat-expansion-indicator::after {
    color: mat.get-theme-color($theme, primary, 500-contrast) !important;
  }

  .mat-sidenav .mat-expansion-panel-body {
    background-color: mat.get-theme-color($theme, primary, 300) !important;
    padding: 0 !important;
  }

  .mat-sidenav .mat-mdc-list-item-unscoped-content {
    padding-left: 8px;
    color: mat.get-theme-color($theme, primary, 500-contrast) !important;
  }

  .mat-sidenav .mat-expansion-panel .mat-mdc-list-item-unscoped-content {
    padding-left: 42px;
    padding-right: 16px;
    color: mat.get-theme-color($theme, primary, 500-contrast) !important;
  }

  .mat-sidenav .mat-expansion-panel .mat-mdc-list-item {
    background-color: mat.get-theme-color($theme, primary, 300);
    padding: 0 !important;
    border-radius: 0 !important;
    height: 48px !important;
  }

  .nav-item-active {
    background-color: darkgrey !important;
    color: mat.get-theme-color($theme, primary, 500-contrast) !important;
  }

  .mat-sidenav .mat-mdc-list-item:hover {
    color: mat.get-theme-color($theme, primary, 500-contrast) !important;
  }

  .nav-item-active:hover {
    color: mat.get-theme-color($theme, primary, 500-contrast) !important;
  }

  .nav-item-active:focus {
    background-color: darkgrey !important;
    color: mat.get-theme-color($theme, primary, 500-contrast) !important;
  }

  .mat-sidenav .mdc-list-item:focus::before {
    background-color: transparent !important;
    opacity: unset !important;
  }

  //.mat-mdc-row {
  //  transition: background-color 0.3s;
  //}

  .mat-mdc-row:hover {
    background-color: mat.get-theme-color($theme, primary, 50);
  }
  .mat-mdc-row:active {
    background-color: mat.get-theme-color($theme, primary, 100);
  }

  .mat-mdc-row:focus,
  .mat-mdc-row.active {
    background-color: mat.get-theme-color($theme, primary, 100);
  }

  a {
    color: mat.get-theme-color($theme, accent, 300);
    text-decoration: none;
  }
  a:visited {
    color: mat.get-theme-color($theme, accent, 300);
  }

  .dashboard-color {
    color: mat.get-theme-color($theme, primary, 300);
  }

  // Dashboard theme variables
  :root {
    --dashboard-card-header-background: #{mat.get-theme-color($theme, primary, 400)};
    --dashboard-card-header-text: #{mat.get-theme-color($theme, primary, default-contrast)};
    --dashboard-metric-value-color: #{mat.get-theme-color($theme, primary, 700)};
    --dashboard-metric-label-color: #{mat.get-theme-color($theme, primary, 400)};
    --dashboard-metric-item-background: #{mat.get-theme-color($theme, primary, 50)};
    --dashboard-metric-item-border: #{mat.get-theme-color($theme, primary, 200)};
    --dashboard-metric-item-hover-background: #{mat.get-theme-color($theme, primary, 100)};
    --dashboard-metric-item-hover-shadow: 0 4px 12px #{mat.get-theme-color($theme, primary, 200)};
    --dashboard-metric-number-color: #{mat.get-theme-color($theme, primary, 700)};
    --dashboard-metric-text-color: #{mat.get-theme-color($theme, primary, 400)};
    --dashboard-metric-number-hover-color: #{mat.get-theme-color($theme, primary, 800)};
    --dashboard-metric-text-hover-color: #{mat.get-theme-color($theme, primary, 500)};
  }

  .badge {
    background-color: mat.get-theme-color($theme, accent, 200) !important;
    color: mat.get-theme-color($theme, primary, 500-contrast) !important;
    padding: 2px 8px;
    font-size: 0.8rem;
    border-radius: 10px;
    margin-left: 10px;
  }
}

@mixin theme($theme) {
  @if mat.theme-has($theme, color) {
    @include color($theme);
  }
}
