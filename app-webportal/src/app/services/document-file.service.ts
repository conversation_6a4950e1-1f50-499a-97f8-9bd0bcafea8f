import {Injectable} from '@angular/core'
import {SessionService} from "./session.service";
import {DocumentFormat, WebPortalDocumentService} from "../backend-api";
import {TranslateService} from "@ngx-translate/core";
import {MatSnackBar} from "@angular/material/snack-bar";
import {map, Subject, takeUntil} from "rxjs";
import {PdfViewerDialogComponent} from "../shared/components/pdf-viewer-dialog/pdf-viewer-dialog.component";
import {MatDialog} from "@angular/material/dialog";

@Injectable({providedIn: 'root'})
export class DocumentFileService {

  destroy$ = new Subject<void>()

  constructor(
    private sessionService: SessionService,
    private webPortalDocumentService: WebPortalDocumentService,
    private translate: TranslateService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog,
  ) {
  }

  openDocumentPdf(orderKey: string | null, subscriptionKey: string | null, documentKey: string) {
    this.openDocumentPdfInternal(orderKey, subscriptionKey, documentKey, false)
  }

  openDocumentPdfCopy(orderKey: string | null, subscriptionKey: string | null, documentKey: string) {
    this.openDocumentPdfInternal(orderKey, subscriptionKey, documentKey, true)
  }

  private openDocumentPdfInternal(
    orderKey: string | null,
    subscriptionKey: string | null,
    documentKey: string,
    openCopy: boolean,
  ) {
    return this.webPortalDocumentService
      .loadDocumentFile(
        this.sessionService.getTenantKey(),
        documentKey,
        orderKey || undefined,
        subscriptionKey || undefined,
        DocumentFormat.EPdf,
        openCopy,
        'response'
      )
      .pipe(
        map((response) => new Blob([response.body], {type: 'application/pdf'})),
        takeUntil(this.destroy$)
      )
      .subscribe({
        next: (blob: Blob) => {
          const fileURL = URL.createObjectURL(blob)
          this.dialog.open(PdfViewerDialogComponent, {data: {url: fileURL},})
        },
        error: (error: any) => {
          console.error(error)
          this.snackBar.open(
            this.translate.instant('DOCUMENTS.DOWNLOAD_FAILED', {value: documentKey,}),
            this.translate.instant('MAIN.CLOSE'),
            {duration: 3000}
          )
        },
      })
  }

  downloadFileXml(orderKey: string | null, subscriptionKey: string | null, documentKey: string) {
    this.downloadFile(orderKey, subscriptionKey, documentKey, DocumentFormat.Xml, 'xml')
  }

  downloadFileXRechnung(orderKey: string | null, subscriptionKey: string | null, documentKey: string) {
    this.downloadFile(orderKey, subscriptionKey, documentKey, DocumentFormat.XRechnung, 'xml')
  }

  downloadFileJson(orderKey: string | null, subscriptionKey: string | null, documentKey: string) {
    this.downloadFile(orderKey, subscriptionKey, documentKey, DocumentFormat.Json, 'json')
  }

  private downloadFile(orderKey: string | null, subscriptionKey: string | null, documentKey: string, format: DocumentFormat, fileExtension: string) {
    this.webPortalDocumentService
      .loadDocumentFile(
        this.sessionService.getTenantKey(),
        documentKey,
        orderKey || undefined,
        subscriptionKey || undefined,
        format,
        false,
        'response'
      )
      .pipe(
        map((response) => new Blob([response.body], {type: 'application/json'})),
        takeUntil(this.destroy$)
      )
      .subscribe({
        next: (blob: Blob) => {
          const fileURL = URL.createObjectURL(blob)
          const link = document.createElement('a')
          link.setAttribute('href', fileURL)
          link.setAttribute('download', documentKey + '.' + fileExtension)
          document.body.appendChild(link)
          link.click()
          URL.revokeObjectURL(fileURL)
        },
        error: (error: any) => {
          console.error(error)
          this.snackBar.open(
            this.translate.instant('DOCUMENTS.DOWNLOAD_FAILED', {
              value: documentKey,
            }),
            this.translate.instant('MAIN.CLOSE'),
            {duration: 3000}
          )
        },
      })
  }

}
