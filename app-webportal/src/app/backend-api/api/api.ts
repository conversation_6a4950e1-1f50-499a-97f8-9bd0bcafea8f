export * from './webPortalApproval.service';
import { WebPortalApprovalService } from './webPortalApproval.service';
export * from './webPortalBalanceCase.service';
import { WebPortalBalanceCaseService } from './webPortalBalanceCase.service';
export * from './webPortalBookingAccount.service';
import { WebPortalBookingAccountService } from './webPortalBookingAccount.service';
export * from './webPortalBookingRule.service';
import { WebPortalBookingRuleService } from './webPortalBookingRule.service';
export * from './webPortalBusinessSegment.service';
import { WebPortalBusinessSegmentService } from './webPortalBusinessSegment.service';
export * from './webPortalCurrencyExchangeRates.service';
import { WebPortalCurrencyExchangeRatesService } from './webPortalCurrencyExchangeRates.service';
export * from './webPortalCustomer.service';
import { WebPortalCustomerService } from './webPortalCustomer.service';
export * from './webPortalDocument.service';
import { WebPortalDocumentService } from './webPortalDocument.service';
export * from './webPortalInternal.service';
import { WebPortalInternalService } from './webPortalInternal.service';
export * from './webPortalNotes.service';
import { WebPortalNotesService } from './webPortalNotes.service';
export * from './webPortalNotification.service';
import { WebPortalNotificationService } from './webPortalNotification.service';
export * from './webPortalOrder.service';
import { WebPortalOrderService } from './webPortalOrder.service';
export * from './webPortalPaymentAccount.service';
import { WebPortalPaymentAccountService } from './webPortalPaymentAccount.service';
export * from './webPortalPaymentAssignment.service';
import { WebPortalPaymentAssignmentService } from './webPortalPaymentAssignment.service';
export * from './webPortalPaymentCategorizationRule.service';
import { WebPortalPaymentCategorizationRuleService } from './webPortalPaymentCategorizationRule.service';
export * from './webPortalPaymentTransaction.service';
import { WebPortalPaymentTransactionService } from './webPortalPaymentTransaction.service';
export * from './webPortalPostingRecord.service';
import { WebPortalPostingRecordService } from './webPortalPostingRecord.service';
export * from './webPortalPredefinedItem.service';
import { WebPortalPredefinedItemService } from './webPortalPredefinedItem.service';
export * from './webPortalReport.service';
import { WebPortalReportService } from './webPortalReport.service';
export * from './webPortalRole.service';
import { WebPortalRoleService } from './webPortalRole.service';
export * from './webPortalSentArchive.service';
import { WebPortalSentArchiveService } from './webPortalSentArchive.service';
export * from './webPortalSettlementReport.service';
import { WebPortalSettlementReportService } from './webPortalSettlementReport.service';
export * from './webPortalSubscription.service';
import { WebPortalSubscriptionService } from './webPortalSubscription.service';
export * from './webPortalSupplier.service';
import { WebPortalSupplierService } from './webPortalSupplier.service';
export * from './webPortalTranslation.service';
import { WebPortalTranslationService } from './webPortalTranslation.service';
export * from './webPortalUser.service';
import { WebPortalUserService } from './webPortalUser.service';
export * from './webPortalWorkflow.service';
import { WebPortalWorkflowService } from './webPortalWorkflow.service';
export const APIS = [WebPortalApprovalService, WebPortalBalanceCaseService, WebPortalBookingAccountService, WebPortalBookingRuleService, WebPortalBusinessSegmentService, WebPortalCurrencyExchangeRatesService, WebPortalCustomerService, WebPortalDocumentService, WebPortalInternalService, WebPortalNotesService, WebPortalNotificationService, WebPortalOrderService, WebPortalPaymentAccountService, WebPortalPaymentAssignmentService, WebPortalPaymentCategorizationRuleService, WebPortalPaymentTransactionService, WebPortalPostingRecordService, WebPortalPredefinedItemService, WebPortalReportService, WebPortalRoleService, WebPortalSentArchiveService, WebPortalSettlementReportService, WebPortalSubscriptionService, WebPortalSupplierService, WebPortalTranslationService, WebPortalUserService, WebPortalWorkflowService];
