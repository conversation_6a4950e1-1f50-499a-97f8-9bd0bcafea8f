/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * The type of the right
 */
export type UserRight = 'CUSTOMERS_READ' | 'CUSTOMERS_WRITE' | 'SUPPLIERS_READ' | 'SUPPLIERS_WRITE' | 'ORDER_CAPTURE' | 'ORDERS_READ' | 'ORDERS_WRITE' | 'PREDEFINED_ITEMS_READ' | 'PREDEFINED_ITEMS_WRITE' | 'OPEN_ITEMS_READ' | 'DOCUMENTS_READ' | 'TRANSLATIONS_READ' | 'TRANSLATIONS_WRITE' | 'REPORTS_READ' | 'BUSINESS_SEGMENTS_READ' | 'BUSINESS_SEGMENTS_WRITE' | 'APPROVALS_READ' | 'APPROVALS_WRITE' | 'PAYMENT_ACCOUNTS_READ' | 'PAYMENT_ACCOUNTS_WRITE' | 'SETTLEMENT_REPORTS_READ' | 'SETTLEMENT_REPORTS_WRITE' | 'PAYMENT_CATEGORIZATION_RULES_READ' | 'PAYMENT_CATEGORIZATION_RULES_WRITE' | 'PAYMENT_TRANSACTIONS_READ' | 'PAYMENT_ASSIGNMENTS_READ' | 'PAYMENT_ASSIGNMENTS_WRITE' | 'BOOKING_ACCOUNTS_READ' | 'BOOKING_ACCOUNTS_WRITE' | 'BOOKING_RULES_READ' | 'BOOKING_RULES_WRITE' | 'POSTING_RECORDS_READ' | 'POSTING_RECORDS_WRITE' | 'CURRENCY_EXCHANGE_RATE_READ' | 'MANUAL_BOOKING' | 'SENT_ARCHIVE_READ' | 'SENT_ARCHIVE_WRITE' | 'NOTIFICATIONS_READ' | 'NOTIFICATIONS_WRITE' | 'NOTIFICATION_RECEIVERS_READ' | 'NOTIFICATION_RECEIVERS_WRITE' | 'REPORT_CONFIGURATIONS_READ' | 'REPORT_CONFIGURATIONS_WRITE' | 'USERS_READ' | 'USERS_WRITE' | 'ROLES_READ' | 'ROLES_WRITE' | 'WORKFLOW_READ' | 'WORKFLOW_WRITE' | 'INTERNAL_JOBS_READ' | 'INTERNAL_JOBS_WRITE';

export const UserRight = {
    CustomersRead: 'CUSTOMERS_READ' as UserRight,
    CustomersWrite: 'CUSTOMERS_WRITE' as UserRight,
    SuppliersRead: 'SUPPLIERS_READ' as UserRight,
    SuppliersWrite: 'SUPPLIERS_WRITE' as UserRight,
    OrderCapture: 'ORDER_CAPTURE' as UserRight,
    OrdersRead: 'ORDERS_READ' as UserRight,
    OrdersWrite: 'ORDERS_WRITE' as UserRight,
    PredefinedItemsRead: 'PREDEFINED_ITEMS_READ' as UserRight,
    PredefinedItemsWrite: 'PREDEFINED_ITEMS_WRITE' as UserRight,
    OpenItemsRead: 'OPEN_ITEMS_READ' as UserRight,
    DocumentsRead: 'DOCUMENTS_READ' as UserRight,
    TranslationsRead: 'TRANSLATIONS_READ' as UserRight,
    TranslationsWrite: 'TRANSLATIONS_WRITE' as UserRight,
    ReportsRead: 'REPORTS_READ' as UserRight,
    BusinessSegmentsRead: 'BUSINESS_SEGMENTS_READ' as UserRight,
    BusinessSegmentsWrite: 'BUSINESS_SEGMENTS_WRITE' as UserRight,
    ApprovalsRead: 'APPROVALS_READ' as UserRight,
    ApprovalsWrite: 'APPROVALS_WRITE' as UserRight,
    PaymentAccountsRead: 'PAYMENT_ACCOUNTS_READ' as UserRight,
    PaymentAccountsWrite: 'PAYMENT_ACCOUNTS_WRITE' as UserRight,
    SettlementReportsRead: 'SETTLEMENT_REPORTS_READ' as UserRight,
    SettlementReportsWrite: 'SETTLEMENT_REPORTS_WRITE' as UserRight,
    PaymentCategorizationRulesRead: 'PAYMENT_CATEGORIZATION_RULES_READ' as UserRight,
    PaymentCategorizationRulesWrite: 'PAYMENT_CATEGORIZATION_RULES_WRITE' as UserRight,
    PaymentTransactionsRead: 'PAYMENT_TRANSACTIONS_READ' as UserRight,
    PaymentAssignmentsRead: 'PAYMENT_ASSIGNMENTS_READ' as UserRight,
    PaymentAssignmentsWrite: 'PAYMENT_ASSIGNMENTS_WRITE' as UserRight,
    BookingAccountsRead: 'BOOKING_ACCOUNTS_READ' as UserRight,
    BookingAccountsWrite: 'BOOKING_ACCOUNTS_WRITE' as UserRight,
    BookingRulesRead: 'BOOKING_RULES_READ' as UserRight,
    BookingRulesWrite: 'BOOKING_RULES_WRITE' as UserRight,
    PostingRecordsRead: 'POSTING_RECORDS_READ' as UserRight,
    PostingRecordsWrite: 'POSTING_RECORDS_WRITE' as UserRight,
    CurrencyExchangeRateRead: 'CURRENCY_EXCHANGE_RATE_READ' as UserRight,
    ManualBooking: 'MANUAL_BOOKING' as UserRight,
    SentArchiveRead: 'SENT_ARCHIVE_READ' as UserRight,
    SentArchiveWrite: 'SENT_ARCHIVE_WRITE' as UserRight,
    NotificationsRead: 'NOTIFICATIONS_READ' as UserRight,
    NotificationsWrite: 'NOTIFICATIONS_WRITE' as UserRight,
    NotificationReceiversRead: 'NOTIFICATION_RECEIVERS_READ' as UserRight,
    NotificationReceiversWrite: 'NOTIFICATION_RECEIVERS_WRITE' as UserRight,
    ReportConfigurationsRead: 'REPORT_CONFIGURATIONS_READ' as UserRight,
    ReportConfigurationsWrite: 'REPORT_CONFIGURATIONS_WRITE' as UserRight,
    UsersRead: 'USERS_READ' as UserRight,
    UsersWrite: 'USERS_WRITE' as UserRight,
    RolesRead: 'ROLES_READ' as UserRight,
    RolesWrite: 'ROLES_WRITE' as UserRight,
    WorkflowRead: 'WORKFLOW_READ' as UserRight,
    WorkflowWrite: 'WORKFLOW_WRITE' as UserRight,
    InternalJobsRead: 'INTERNAL_JOBS_READ' as UserRight,
    InternalJobsWrite: 'INTERNAL_JOBS_WRITE' as UserRight
};

