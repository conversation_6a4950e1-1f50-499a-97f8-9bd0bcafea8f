/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { SumAggregation } from './sumAggregation';
import { DocumentType } from './documentType';
import { PaymentStatus } from './paymentStatus';


export interface WebPortalDocumentDetails { 
    key: string;
    orderKey?: string;
    subscriptionKey?: string;
    createdAt: string;
    documentDate: string;
    documentType: DocumentType;
    paymentStatus: PaymentStatus;
    sumAggregations: Array<SumAggregation>;
}
export namespace WebPortalDocumentDetails {
}


