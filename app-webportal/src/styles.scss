/* You can add global styles to this file, and also import other style files */

html, body {
  height: 100%;
}

body {
  margin: 0;
  font-family: Roboto, "Helvetica Neue", sans-serif;
}

.loading-spinner {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.form-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  max-width: 400px;
}

.form-row label {
  width: 200px;
}

.form-row span {
  text-align: right;
}

.form-row a {
  text-align: right;
}

.mat-mdc-card-header {
  margin-bottom: 12px;
}

.with-icon {
  display: flex;
  align-items: center;
}

.with-icon > * {
  margin-right: 5px;
}

/* Scrollbar */

::-webkit-scrollbar {
  width: 10px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: lightgrey;
}

::-webkit-scrollbar-thumb:hover {
  background: lightgrey;
}

.back-button {
  margin-left: -10px;
}

.mat-mdc-row {
  height: 38px !important;
}

.vertical-form {
  display: flex;
  flex-direction: column;
}

.mat-mdc-form-field {
  margin-bottom: 14px;
}

.mdc-tab__text-label {
  font-size: 18px;
}

.mat-mdc-tab {
  margin-top: 8px !important;
  margin-bottom: 12px !important;
  font-weight: bold;
}

.overlay {
  width: 100%;
  height: 100%;
  z-index: 9900;
}

.overlay.disabled {
  pointer-events: none;
  z-index: 9998;
  opacity: 0.4;
}

.spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 9999;
}

/* BPMN Properties Panel Global Styles */
.bio-properties-panel {
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif !important;
  font-size: 14px !important; /* Vergrößert von 12px auf 14px */
  background: white !important;
  border: none !important;
  padding-bottom: 70px !important;
  min-height: 100% !important;
}

/* AI Chat Styles für Properties Panel */
.ai-chat-container {
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif !important;
  background: #f8f9fa !important;
  border-radius: 8px !important;
  padding: 12px !important;
  margin: 8px 0 !important;
}

.ai-chat-messages {
  max-height: 300px !important;
  overflow-y: auto !important;
  margin-bottom: 12px !important;
  background: white !important;
  border-radius: 6px !important;
  padding: 8px !important;
  border: 1px solid #e0e0e0 !important;
}

.ai-message {
  display: flex !important;
  margin-bottom: 12px !important;
  align-items: flex-start !important;
}

.ai-message.user {
  flex-direction: row-reverse !important;
}

.ai-message.user .message-content {
  background: #1976d2 !important;
  color: white !important;
  margin-right: 8px !important;
  margin-left: 0 !important;
}

.ai-message.ai .message-content,
.ai-message.system .message-content {
  background: #f5f5f5 !important;
  color: #333 !important;
  margin-left: 8px !important;
  margin-right: 0 !important;
}

.message-avatar {
  width: 24px !important;
  height: 24px !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 14px !important;
  flex-shrink: 0 !important;
}

.message-content {
  padding: 8px 12px !important;
  border-radius: 12px !important;
  font-size: 13px !important; /* Vergrößert von 12px auf 13px */
  line-height: 1.4 !important;
  max-width: 200px !important;
  word-wrap: break-word !important;
}

.ai-quick-actions {
  display: flex !important;
  gap: 6px !important;
  margin-bottom: 12px !important;
  flex-wrap: wrap !important;
}

.ai-quick-btn {
  background: #e3f2fd !important;
  border: 1px solid #1976d2 !important;
  color: #1976d2 !important;
  padding: 6px 10px !important; /* Vergrößert padding */
  border-radius: 16px !important;
  font-size: 11px !important; /* Vergrößert von 10px auf 11px */
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  white-space: nowrap !important;
}

.ai-quick-btn:hover {
  background: #1976d2 !important;
  color: white !important;
}

.ai-chat-input-area {
  border-top: 1px solid #e0e0e0 !important;
  padding-top: 8px !important;
}

.ai-input-container {
  display: flex !important;
  gap: 6px !important;
  align-items: flex-end !important;
}

.ai-chat-input {
  flex: 1 !important;
  padding: 8px 10px !important; /* Vergrößert padding */
  border: 1px solid #ccc !important;
  border-radius: 6px !important;
  font-size: 13px !important; /* Vergrößert von 12px auf 13px */
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif !important;
  resize: vertical !important;
  min-height: 24px !important; /* Vergrößert von 20px auf 24px */
}

.ai-chat-input:focus {
  outline: none !important;
  border-color: #1976d2 !important;
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2) !important;
}

.ai-send-btn {
  background: #1976d2 !important;
  color: white !important;
  border: none !important;
  border-radius: 6px !important;
  padding: 6px 8px !important;
  cursor: pointer !important;
  font-size: 12px !important;
  transition: background 0.2s ease !important;
}

.ai-send-btn:hover {
  background: #1565c0 !important;
}

.ai-context-info {
  margin-top: 4px !important;
  color: #666 !important;
  font-size: 10px !important;
}

.ai-status {
  background: #fff3e0 !important;
  border: 1px solid #ffb74d !important;
  border-radius: 6px !important;
  padding: 8px !important;
  margin-top: 8px !important;
}

.ai-thinking {
  display: flex !important;
  align-items: center !important;
  gap: 6px !important;
  font-size: 12px !important; /* Vergrößert von 11px auf 12px */
  color: #f57c00 !important;
}

.ai-spinner {
  animation: spin 1s linear infinite !important;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Scrollbar für AI Chat */
.ai-chat-messages::-webkit-scrollbar {
  width: 4px !important;
}

.ai-chat-messages::-webkit-scrollbar-track {
  background: #f1f1f1 !important;
}

.ai-chat-messages::-webkit-scrollbar-thumb {
  background: #ccc !important;
  border-radius: 2px !important;
}

.ai-chat-messages::-webkit-scrollbar-thumb:hover {
  background: #999 !important;
}
