@use '@angular/material' as mat;
@use '../app/_app.component-theme.scss' as app;

@include mat.core();

$velocity-wheels-brand: (
  50: #eff6ff,
  100: #dbeafe,
  200: #bfdbfe,
  300: #93c5fd,
  400: #60a5fa,
  500: #1e40af,
  600: #1d4ed8,
  700: #1e3a8a,
  800: #1e3a8a,
  900: #172554,
  A100: #bfdbfe,
  A200: #60a5fa,
  A400: #3b82f6,
  A700: #1d4ed8,
  contrast: (
    50: rgba(black, 0.87),
    100: rgba(black, 0.87),
    200: rgba(black, 0.87),
    300: rgba(black, 0.87),
    400: white,
    500: white,
    600: white,
    700: white,
    800: white,
    900: white,
    A100: rgba(black, 0.87),
    A200: white,
    A400: white,
    A700: white,
  )
);

$primary: mat.m2-define-palette($velocity-wheels-brand, 500);
$accent: mat.m2-define-palette(mat.$m2-amber-palette, A200, A100, A400);

$theme: mat.m2-define-light-theme((
  color: (
    primary: $primary,
    accent: $accent,
  ),
  typography: mat.m2-define-typography-config(),
  density: -3,
));

$primary-default: mat.get-theme-color($theme, primary, default);

@include mat.all-component-themes($theme);

@include mat.button-density(0);
@include app.theme($theme);
