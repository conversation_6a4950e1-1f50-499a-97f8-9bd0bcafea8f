package com.klosesoft.billingsolution.generated.api.webportal.server.model

import java.util.Objects
import com.fasterxml.jackson.annotation.JsonCreator
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.annotation.JsonValue
import com.klosesoft.billingsolution.generated.api.webportal.server.model.CustomerTypeDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.LanguageDto
import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid

/**
 * 
 * @param key 
 * @param customerType 
 * @param language 
 * @param firstName The first name of the customer
 * @param lastName The last name of the customer
 * @param companyName The name of the company (if the customer is a company)
 * @param vatId The VAT ID for the customer.
 * @param properties Additional properties for the customer
 */
data class CustomerDto(

    @get:JsonProperty("key", required = true) val key: kotlin.String,

    @field:Valid
    @get:JsonProperty("customerType", required = true) val customerType: CustomerTypeDto,

    @field:Valid
    @get:JsonProperty("language", required = true) val language: LanguageDto,

    @get:JsonProperty("firstName") val firstName: kotlin.String? = null,

    @get:JsonProperty("lastName") val lastName: kotlin.String? = null,

    @get:JsonProperty("companyName") val companyName: kotlin.String? = null,

    @get:JsonProperty("vatId") val vatId: kotlin.String? = null,

    @get:JsonProperty("properties") val properties: kotlin.collections.Map<kotlin.String, kotlin.String>? = null
    ) {

}

