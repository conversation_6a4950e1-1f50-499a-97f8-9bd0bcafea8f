/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (7.8.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
*/
package com.klosesoft.billingsolution.generated.api.webportal.server

import com.klosesoft.billingsolution.generated.api.webportal.server.model.WorkflowTypeDto
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity

import org.springframework.web.bind.annotation.*
import org.springframework.validation.annotation.Validated
import org.springframework.web.context.request.NativeWebRequest
import org.springframework.beans.factory.annotation.Autowired

import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid

import kotlinx.coroutines.flow.Flow
import kotlin.collections.List
import kotlin.collections.Map

@RestController
@Validated
interface WebPortalWorkflowApi {


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/webportal/v1/workflow"],
            produces = ["application/xml"]
    )
    suspend fun loadWorkflowDefinition( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @Valid @RequestParam(value = "workflowType", required = false, defaultValue = "ORDER") workflowType: WorkflowTypeDto): ResponseEntity<kotlin.String>


    @RequestMapping(
            method = [RequestMethod.PUT],
            value = ["/webportal/v1/workflow"],
            produces = ["application/xml"],
            consumes = ["application/xml"]
    )
    suspend fun updateWorkflowDefinition( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String,@NotNull  @Valid @RequestParam(value = "workflowType", required = true, defaultValue = "ORDER") workflowType: WorkflowTypeDto, @Valid @RequestBody body: kotlin.String): ResponseEntity<kotlin.String>
}
