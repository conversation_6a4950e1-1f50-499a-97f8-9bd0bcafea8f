package com.klosesoft.billingsolution.generated.api.webportal.server.model

import java.util.Objects
import com.fasterxml.jackson.annotation.JsonValue
import com.fasterxml.jackson.annotation.JsonCreator
import com.fasterxml.jackson.annotation.JsonProperty
import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid

/**
* The type of the right
* Values: CUSTOMERS_READ,CUSTOMERS_WRITE,SUPPLIERS_READ,SUPPLIERS_WRITE,ORDER_CAPTURE,ORDERS_READ,ORDERS_WRITE,PREDEFINED_ITEMS_READ,PREDEFINED_ITEMS_WRITE,OPEN_ITEMS_READ,DOCUMENTS_READ,TRANSLATIONS_READ,TRA<PERSON>LATIONS_WRITE,REPORTS_READ,BUSINESS_SEGMENTS_READ,BUSINESS_SEGMENTS_WRITE,APPROVALS_READ,APPROVALS_WRITE,PAYMENT_ACCOUNTS_READ,PAYMENT_ACCOUNTS_WRITE,SETTLEMENT_REPORTS_READ,SETTLEMENT_REPORTS_WRITE,PAYMENT_CATEGORIZATION_RULES_READ,PAYMENT_CATEGORIZATION_RULES_WRITE,PAYMENT_TRANSACTIONS_READ,PAYMENT_ASSIGNMENTS_READ,PAYMENT_ASSIGNMENTS_WRITE,BOOKING_ACCOUNTS_READ,BOOKING_ACCOUNTS_WRITE,BOOKING_RULES_READ,BOOKING_RULES_WRITE,POSTING_RECORDS_READ,POSTING_RECORDS_WRITE,CURRENCY_EXCHANGE_RATE_READ,MANUAL_BOOKING,SENT_ARCHIVE_READ,SENT_ARCHIVE_WRITE,NOTIFICATIONS_READ,NOTIFICATIONS_WRITE,NOTIFICATION_RECEIVERS_READ,NOTIFICATION_RECEIVERS_WRITE,REPORT_CONFIGURATIONS_READ,REPORT_CONFIGURATIONS_WRITE,USERS_READ,USERS_WRITE,ROLES_READ,ROLES_WRITE,WORKFLOW_READ,WORKFLOW_WRITE,INTERNAL_JOBS_READ,INTERNAL_JOBS_WRITE
*/
enum class UserRightDto(@get:JsonValue val value: kotlin.String) {

    CUSTOMERS_READ("CUSTOMERS_READ"),
    CUSTOMERS_WRITE("CUSTOMERS_WRITE"),
    SUPPLIERS_READ("SUPPLIERS_READ"),
    SUPPLIERS_WRITE("SUPPLIERS_WRITE"),
    ORDER_CAPTURE("ORDER_CAPTURE"),
    ORDERS_READ("ORDERS_READ"),
    ORDERS_WRITE("ORDERS_WRITE"),
    PREDEFINED_ITEMS_READ("PREDEFINED_ITEMS_READ"),
    PREDEFINED_ITEMS_WRITE("PREDEFINED_ITEMS_WRITE"),
    OPEN_ITEMS_READ("OPEN_ITEMS_READ"),
    DOCUMENTS_READ("DOCUMENTS_READ"),
    TRANSLATIONS_READ("TRANSLATIONS_READ"),
    TRANSLATIONS_WRITE("TRANSLATIONS_WRITE"),
    REPORTS_READ("REPORTS_READ"),
    BUSINESS_SEGMENTS_READ("BUSINESS_SEGMENTS_READ"),
    BUSINESS_SEGMENTS_WRITE("BUSINESS_SEGMENTS_WRITE"),
    APPROVALS_READ("APPROVALS_READ"),
    APPROVALS_WRITE("APPROVALS_WRITE"),
    PAYMENT_ACCOUNTS_READ("PAYMENT_ACCOUNTS_READ"),
    PAYMENT_ACCOUNTS_WRITE("PAYMENT_ACCOUNTS_WRITE"),
    SETTLEMENT_REPORTS_READ("SETTLEMENT_REPORTS_READ"),
    SETTLEMENT_REPORTS_WRITE("SETTLEMENT_REPORTS_WRITE"),
    PAYMENT_CATEGORIZATION_RULES_READ("PAYMENT_CATEGORIZATION_RULES_READ"),
    PAYMENT_CATEGORIZATION_RULES_WRITE("PAYMENT_CATEGORIZATION_RULES_WRITE"),
    PAYMENT_TRANSACTIONS_READ("PAYMENT_TRANSACTIONS_READ"),
    PAYMENT_ASSIGNMENTS_READ("PAYMENT_ASSIGNMENTS_READ"),
    PAYMENT_ASSIGNMENTS_WRITE("PAYMENT_ASSIGNMENTS_WRITE"),
    BOOKING_ACCOUNTS_READ("BOOKING_ACCOUNTS_READ"),
    BOOKING_ACCOUNTS_WRITE("BOOKING_ACCOUNTS_WRITE"),
    BOOKING_RULES_READ("BOOKING_RULES_READ"),
    BOOKING_RULES_WRITE("BOOKING_RULES_WRITE"),
    POSTING_RECORDS_READ("POSTING_RECORDS_READ"),
    POSTING_RECORDS_WRITE("POSTING_RECORDS_WRITE"),
    CURRENCY_EXCHANGE_RATE_READ("CURRENCY_EXCHANGE_RATE_READ"),
    MANUAL_BOOKING("MANUAL_BOOKING"),
    SENT_ARCHIVE_READ("SENT_ARCHIVE_READ"),
    SENT_ARCHIVE_WRITE("SENT_ARCHIVE_WRITE"),
    NOTIFICATIONS_READ("NOTIFICATIONS_READ"),
    NOTIFICATIONS_WRITE("NOTIFICATIONS_WRITE"),
    NOTIFICATION_RECEIVERS_READ("NOTIFICATION_RECEIVERS_READ"),
    NOTIFICATION_RECEIVERS_WRITE("NOTIFICATION_RECEIVERS_WRITE"),
    REPORT_CONFIGURATIONS_READ("REPORT_CONFIGURATIONS_READ"),
    REPORT_CONFIGURATIONS_WRITE("REPORT_CONFIGURATIONS_WRITE"),
    USERS_READ("USERS_READ"),
    USERS_WRITE("USERS_WRITE"),
    ROLES_READ("ROLES_READ"),
    ROLES_WRITE("ROLES_WRITE"),
    WORKFLOW_READ("WORKFLOW_READ"),
    WORKFLOW_WRITE("WORKFLOW_WRITE"),
    INTERNAL_JOBS_READ("INTERNAL_JOBS_READ"),
    INTERNAL_JOBS_WRITE("INTERNAL_JOBS_WRITE");

    companion object {
        @JvmStatic
        @JsonCreator
        fun forValue(value: kotlin.String): UserRightDto {
                return values().first{it -> it.value == value}
        }
    }
}

