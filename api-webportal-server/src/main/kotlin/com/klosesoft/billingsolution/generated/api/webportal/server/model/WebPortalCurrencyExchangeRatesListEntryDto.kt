package com.klosesoft.billingsolution.generated.api.webportal.server.model

import java.util.Objects
import com.fasterxml.jackson.annotation.JsonCreator
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.annotation.JsonValue
import com.klosesoft.billingsolution.generated.api.webportal.server.model.CurrencyDto
import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid

/**
 * 
 * @param validFrom 
 * @param sourceCurrency 
 * @param targetCurrency 
 * @param rate 
 * @param key 
 */
data class WebPortalCurrencyExchangeRatesListEntryDto(

    @get:JsonProperty("validFrom", required = true) val validFrom: java.time.OffsetDateTime,

    @field:Valid
    @get:JsonProperty("sourceCurrency", required = true) val sourceCurrency: CurrencyDto,

    @field:Valid
    @get:JsonProperty("targetCurrency", required = true) val targetCurrency: CurrencyDto,

    @get:JsonProperty("rate", required = true) val rate: java.math.BigDecimal,

    @get:JsonProperty("key", required = true) val key: kotlin.String
    ) {

}

