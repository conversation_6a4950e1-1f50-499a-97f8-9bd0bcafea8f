package com.klosesoft.billingsolution.domain.logic.api.service.mapper

import com.klosesoft.billingsolution.domain.model.dto.PostingRecordDomainDto
import com.klosesoft.billingsolution.domain.model.dto.PostingRecordEntryDomainDto
import com.klosesoft.billingsolution.domain.model.valueobject.PostingRecordTargetType
import com.klosesoft.billingsolution.persistence.model.entity.bookkeeping.PostingRecord
import com.klosesoft.billingsolution.persistence.model.entity.bookkeeping.PostingRecordEntry
import com.klosesoft.billingsolution.persistence.service.DocumentLoadService
import com.klosesoft.billingsolution.persistence.service.OrderLoadService
import com.klosesoft.billingsolution.persistence.service.PaymentAssignmentLoadService
import com.klosesoft.billingsolution.persistence.service.PaymentTransactionLoadService
import org.springframework.stereotype.Service

@Service
class PostingRecordDomainApiMapper(
    private val orderLoadService: OrderLoadService,
    private val documentLoadService: DocumentLoadService,
    private val paymentAssignmentLoadService: PaymentAssignmentLoadService,
    private val paymentTransactionLoadService: PaymentTransactionLoadService,
) {
    internal suspend fun toDomainDto(
        postingRecord: PostingRecord,
    ): PostingRecordDomainDto {
        val targetKey =
            when (postingRecord.targetType) {
                PostingRecordTargetType.DOCUMENT -> {
                    documentLoadService.findById(
                        postingRecord.targetId!!,
                    ).key
                }

                PostingRecordTargetType.PAYMENT_ASSIGNMENT -> {
                    paymentAssignmentLoadService.findById(
                        postingRecord.targetId!!,
                    ).key
                }

                PostingRecordTargetType.PAYMENT_TRANSACTION -> {
                    paymentTransactionLoadService.findById(
                        postingRecord.targetId!!,
                    ).key
                }

                else -> null
            }

        return PostingRecordDomainDto(
            key = postingRecord.key,
            creationDateTime = postingRecord.creationDate,
            orderKey = postingRecord.orderId?.let { orderLoadService.findById(it).key },
            targetKey = targetKey,
            documentDate = postingRecord.documentDate,
            number = postingRecord.number,
            targetType = postingRecord.targetType,
            bookingDate = postingRecord.bookingDate,
            bookingText = postingRecord.bookingText,
            currency = postingRecord.currency,
            servicePeriodStart = postingRecord.servicePeriodStart,
            servicePeriodEnd = postingRecord.servicePeriodEnd,
            documentType = postingRecord.documentType,
        )
    }

    internal suspend fun toDomainDto(
        postingRecordEntry: PostingRecordEntry,
    ): PostingRecordEntryDomainDto = PostingRecordEntryDomainDto(
        key = postingRecordEntry.key,
        debitAccountNumber = postingRecordEntry.debitAccountNumber,
        debitPostingKey = postingRecordEntry.debitPostingKey,
        creditAccountNumber = postingRecordEntry.creditAccountNumber,
        creditPostingKey = postingRecordEntry.creditPostingKey,
        amount = postingRecordEntry.amount,
        taxCode = postingRecordEntry.taxCode,
        documentItemKey = null,
        bookingRuleKey = null,
        usedCurrencyExchangeRateId = null,
    )
}
