package com.klosesoft.billingsolution.domain.logic.api.service.load

import com.klosesoft.billingsolution.domain.model.dto.TenantConfigDomainDto
import com.klosesoft.billingsolution.persistence.service.TenantLoadService
import org.springframework.stereotype.Service
import java.util.Locale
import java.util.UUID

@Service
class TenantConfigLoadDomainApiService(private val tenantLoadService: TenantLoadService) {
    suspend fun findTenantConfigByTenantId(
        tenantId: UUID,
    ): TenantConfigDomainDto = tenantLoadService.findTenantConfigByTenantId(tenantId).let {
        TenantConfigDomainDto(
            appClientId = it.appClientId,
            storageType = it.storageType,
            orderWorkflow = it.orderWorkflow,
            orderWorkflowXml = it.orderWorkflowXml,
            subscriptionWorkflow = it.subscriptionWorkflow,
            subscriptionWorkflowXml = it.subscriptionWorkflowXml,
            locale = Locale.forLanguageTag(it.locale),
            theme = it.theme,
            timeZone = it.timeZone,
            defaultCurrency = it.ledgerCurrency,
            defaultTaxRate = it.defaultTaxRate,
            defaultLanguage = it.defaultLanguage,
        )
    }

    suspend fun findTenantConfigByTenantKey(
        tenantKey: String,
    ): TenantConfigDomainDto {
        val tenantId = tenantLoadService.fetchTenantId(tenantKey)
        return tenantLoadService.findTenantConfigByTenantId(tenantId).let {
            TenantConfigDomainDto(
                appClientId = it.appClientId,
                storageType = it.storageType,
                orderWorkflow = it.orderWorkflow,
                orderWorkflowXml = it.orderWorkflowXml,
                subscriptionWorkflow = it.subscriptionWorkflow,
                subscriptionWorkflowXml = it.subscriptionWorkflowXml,
                locale = Locale.forLanguageTag(it.locale),
                theme = it.theme,
                timeZone = it.timeZone,
                defaultCurrency = it.ledgerCurrency,
                defaultTaxRate = it.defaultTaxRate,
                defaultLanguage = it.defaultLanguage,
            )
        }
    }
}
