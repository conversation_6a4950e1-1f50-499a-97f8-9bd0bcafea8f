package com.klosesoft.billingsolution.domain.logic.api.service.mapper

import com.klosesoft.billingsolution.domain.model.dto.SentArchiveEntryDomainDto
import com.klosesoft.billingsolution.persistence.model.entity.SentArchiveEntry
import org.springframework.stereotype.Service

@Service
class SentArchiveEntryDomainApiMapper {
    fun toDomainDto(
        it: SentArchiveEntry,
    ): SentArchiveEntryDomainDto = SentArchiveEntryDomainDto(
        key = it.key,
        targetType = it.targetType,
        targetKey = it.targetKey,
        sentAt = it.sentAt,
        sendingStatus = it.sendingStatus,
        type = it.type,
        emailHeader = it.emailHeader,
        emailTo = it.emailTo,
        emailBody = it.emailBody,
        emailAttachmentFilenames = it.emailAttachmentFilenames,
        documentType = it.documentType,
    )
}
