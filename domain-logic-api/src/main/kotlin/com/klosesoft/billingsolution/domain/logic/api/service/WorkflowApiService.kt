package com.klosesoft.billingsolution.domain.logic.api.service

import com.fasterxml.jackson.databind.ObjectMapper
import com.klosesoft.billingsolution.common.utils.BillingSolutionConstants
import com.klosesoft.billingsolution.common.utils.exception.OrderNotInActionableStateException
import com.klosesoft.billingsolution.domain.logic.api.service.load.DocumentLoadDomainApiService
import com.klosesoft.billingsolution.domain.logic.api.service.load.TenantConfigLoadDomainApiService
import com.klosesoft.billingsolution.domain.logic.service.common.TenantService
import com.klosesoft.billingsolution.domain.logic.service.common.WorkflowDefinitionService
import com.klosesoft.billingsolution.persistence.service.WorkflowDefinitionLoadService
import com.klosesoft.billingsolution.domain.model.ReactivePageImpl
import com.klosesoft.billingsolution.domain.model.dto.FailedJobDomainDto
import com.klosesoft.billingsolution.domain.model.dto.HistoricActivityDomainDto
import com.klosesoft.billingsolution.domain.model.dto.OrderDomainDto
import com.klosesoft.billingsolution.domain.model.dto.OrderItemRequestDomainDto
import com.klosesoft.billingsolution.domain.model.dto.PaymentAssignmentDomainDto
import com.klosesoft.billingsolution.domain.model.dto.SubscriptionDomainDto
import com.klosesoft.billingsolution.domain.model.dto.SubscriptionItemRequestDomainDto
import com.klosesoft.billingsolution.domain.model.dto.UserTaskDomainDto
import com.klosesoft.billingsolution.domain.model.valueobject.PaymentAssignmentOperation
import com.klosesoft.billingsolution.domain.model.valueobject.PaymentAssignmentTargetType
import com.klosesoft.billingsolution.domain.model.valueobject.UpdateOrderAction
import com.klosesoft.billingsolution.domain.model.valueobject.UpdateSubscriptionAction
import com.klosesoft.billingsolution.domain.model.valueobject.WorkflowType
import com.klosesoft.billingsolution.persistence.model.entity.PaymentAssignment
import com.klosesoft.billingsolution.persistence.model.entity.WorkflowDefinition
import com.klosesoft.billingsolution.persistence.service.OrderLoadService
import com.klosesoft.billingsolution.persistence.service.PaymentAssignmentLoadService
import com.klosesoft.billingsolution.persistence.service.SubscriptionLoadService
import com.klosesoft.billingsolution.persistence.service.TenantLoadService
import com.klosesoft.billingsolution.persistence.service.UserLoadService
import com.klosesoft.billingsolution.workflow.api.WorkflowMessages
import com.klosesoft.billingsolution.workflow.api.parameters.ParameterKeys
import com.klosesoft.billingsolution.workflow.api.parameters.StartSubscriptionWorkflowParameters
import com.klosesoft.billingsolution.workflow.api.parameters.StartWorkflowParameters
import io.github.oshai.kotlinlogging.KotlinLogging
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.asFlow
import kotlinx.coroutines.withContext
import org.flowable.engine.ProcessEngine
import org.flowable.engine.impl.HistoricActivityInstanceQueryProperty
import org.flowable.engine.runtime.Execution
import org.flowable.job.service.impl.JobQueryProperty
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service
import java.io.BufferedReader
import java.io.InputStreamReader
import java.time.ZoneId
import java.util.Locale
import java.util.UUID

@Service
@Suppress("LargeClass")
class WorkflowApiService(
    private val processEngine: ProcessEngine,
    private val objectMapper: ObjectMapper,
    private val tenantConfigLoadDomainApiService: TenantConfigLoadDomainApiService,
    private val tenantLoadService: TenantLoadService,
    private val tenantService: TenantService,
    private val workflowDefinitionService: WorkflowDefinitionService,
    private val workflowDefinitionLoadService: WorkflowDefinitionLoadService,
    private val orderLoadService: OrderLoadService,
    private val subscriptionLoadService: SubscriptionLoadService,
    private val paymentAssignmentLoadService: PaymentAssignmentLoadService,
    private val documentLoadDomainApiService: DocumentLoadDomainApiService,
    private val userLoadService: UserLoadService,
) {
    private val logger = KotlinLogging.logger {}

    suspend fun startOrderWorkflow(
        tenantKey: String,
        userKey: String,
        orderDomainDto: OrderDomainDto,
    ) {
        withContext(Dispatchers.IO) {
            val tenant = tenantLoadService.findTenantByKey(tenantKey)

            // Try to get custom workflow first, fallback to default
            val activeWorkflow = workflowDefinitionLoadService.findActiveByTenantIdAndWorkflowType(tenant.id, WorkflowType.ORDER)
            val processDefinitionKey = if (activeWorkflow != null) {
                activeWorkflow.processDefinitionKey
            } else {
                val tenantConfig = tenantConfigLoadDomainApiService.findTenantConfigByTenantId(tenantId = tenant.id)
                tenantConfig.orderWorkflow
            }

            processEngine.runtimeService.startProcessInstanceByKeyAndTenantId(
                processDefinitionKey,
                orderDomainDto.key,
                StartWorkflowParameters(
                    tenantKey = tenantKey,
                    userKey = userKey,
                    orderDomainDto = orderDomainDto,
                ).toMap(objectMapper),
                tenantKey,
            )
        }
    }

    suspend fun startSubscriptionWorkflow(
        tenantKey: String,
        userKey: String,
        subscriptionDomainDto: SubscriptionDomainDto,
    ) {
        withContext(Dispatchers.IO) {
            val tenant = tenantLoadService.findTenantByKey(tenantKey)

            // Try to get custom workflow first, fallback to default
            val activeWorkflow = workflowDefinitionLoadService.findActiveByTenantIdAndWorkflowType(tenant.id, WorkflowType.SUBSCRIPTION)
            val processDefinitionKey = if (activeWorkflow != null) {
                activeWorkflow.processDefinitionKey
            } else {
                val tenantConfig = tenantConfigLoadDomainApiService.findTenantConfigByTenantId(tenantId = tenant.id)
                tenantConfig.subscriptionWorkflow
            }

            processEngine.runtimeService.startProcessInstanceByKeyAndTenantId(
                processDefinitionKey,
                subscriptionDomainDto.key,
                StartSubscriptionWorkflowParameters(
                    tenantKey = tenantKey,
                    userKey = userKey,
                    subscriptionDomainDto = subscriptionDomainDto,
                ).toMap(objectMapper),
                tenantKey,
            )
        }
    }

    suspend fun sendUpdateSubscriptionEvent(
        tenantKey: String,
        subscriptionKey: String,
        userKey: String? = null,
        action: UpdateSubscriptionAction,
        subscriptionItemRequestDomainDto: SubscriptionItemRequestDomainDto? = null,
        subscriptionItemKey: String? = null,
        subscriptionDomainDto: SubscriptionDomainDto? = null,
    ) {
        withContext(Dispatchers.IO) {
            val subscribedProcess = loadSubscribedSubscriptionProcess(tenantKey, subscriptionKey, WorkflowMessages.UPDATE_SUBSCRIPTION)

            logger.info { "Sending update event to subscription process ${subscribedProcess.id}" }

            processEngine.runtimeService.setVariables(
                subscribedProcess.id,
                determineSubscriptionUpdateParameters(
                    action = action,
                    userId = userKey?.let { userLoadService.fetchUserId(userKey) },
                    subscriptionItemRequestDomainDto = subscriptionItemRequestDomainDto,
                    subscriptionItemKey = subscriptionItemKey,
                    subscriptionDomainDto = subscriptionDomainDto,
                ),
            )

            processEngine.runtimeService.messageEventReceived(WorkflowMessages.UPDATE_SUBSCRIPTION, subscribedProcess.id)
        }
    }

    private fun determineSubscriptionUpdateParameters(
        action: UpdateSubscriptionAction,
        userId: UUID? = null,
        subscriptionItemRequestDomainDto: SubscriptionItemRequestDomainDto?,
        subscriptionItemKey: String?,
        subscriptionDomainDto: SubscriptionDomainDto?,
    ): Map<String, String> {
        val result = when (action) {
            UpdateSubscriptionAction.ADD_SUBSCRIPTION_ITEM, UpdateSubscriptionAction.UPDATE_SUBSCRIPTION_ITEM ->
                mapOf(
                    ParameterKeys.UPDATE_SUBSCRIPTION_ACTION to action.name,
                    ParameterKeys.UPDATE_SUBSCRIPTION_ITEM_DOMAIN_DTO to objectMapper.writeValueAsString(subscriptionItemRequestDomainDto),
                )

            UpdateSubscriptionAction.DELETE_SUBSCRIPTION_ITEM ->
                mapOf(
                    ParameterKeys.UPDATE_SUBSCRIPTION_ACTION to action.name,
                    ParameterKeys.UPDATE_SUBSCRIPTION_ITEM_KEY to subscriptionItemKey,
                )

            UpdateSubscriptionAction.UPDATE_SUBSCRIPTION, UpdateSubscriptionAction.UPDATE_SUBSCRIPTION_PARTIAL ->
                mapOf(
                    ParameterKeys.UPDATE_SUBSCRIPTION_ACTION to action.name,
                    ParameterKeys.UPDATE_SUBSCRIPTION_DOMAIN_DTO to objectMapper.writeValueAsString(subscriptionDomainDto),
                )
        }

        return if (userId != null) {
            result + (ParameterKeys.UPDATE_SUBSCRIPTION_USER_ID to userId.toString())
        } else {
            result
        }
    }

    suspend fun sendUpdateOrderEvent(
        tenantKey: String,
        orderKey: String,
        userKey: String? = null,
        action: UpdateOrderAction,
        orderItemRequestDomainDto: OrderItemRequestDomainDto? = null,
        orderItemKey: String? = null,
        orderDomainDto: OrderDomainDto? = null,
    ) {
        withContext(Dispatchers.IO) {
            val subscribedProcess = loadSubscribedProcess(tenantKey, orderKey, WorkflowMessages.UPDATE_ORDER)

            logger.info { "Sending update event to process ${subscribedProcess.id}" }

            processEngine.runtimeService.setVariables(
                subscribedProcess.id,
                determineOrderUpdateParameters(
                    action = action,
                    userId = userKey?.let { userLoadService.fetchUserId(userKey) },
                    orderItemRequestDomainDto = orderItemRequestDomainDto,
                    orderItemKey = orderItemKey,
                    orderDomainDto = orderDomainDto,
                ),
            )

            processEngine.runtimeService.messageEventReceived(WorkflowMessages.UPDATE_ORDER, subscribedProcess.id)
        }
    }

    private fun determineOrderUpdateParameters(
        action: UpdateOrderAction,
        userId: UUID? = null,
        orderItemRequestDomainDto: OrderItemRequestDomainDto?,
        orderItemKey: String?,
        orderDomainDto: OrderDomainDto?,
    ): Map<String, String> {
        val result = when (action) {
            UpdateOrderAction.ADD_ORDER_ITEM, UpdateOrderAction.UPDATE_ORDER_ITEM ->
                mapOf(
                    ParameterKeys.UPDATE_ORDER_ACTION to action.name,
                    ParameterKeys.UPDATE_ORDER_ITEM_DOMAIN_DTO to objectMapper.writeValueAsString(orderItemRequestDomainDto),
                )

            UpdateOrderAction.DELETE_ORDER_ITEM ->
                mapOf(ParameterKeys.UPDATE_ORDER_ACTION to action.name, ParameterKeys.UPDATE_ORDER_ITEM_KEY to orderItemKey)

            UpdateOrderAction.UPDATE_ORDER, UpdateOrderAction.UPDATE_ORDER_PARTIAL ->
                mapOf(
                    ParameterKeys.UPDATE_ORDER_ACTION to action.name,
                    ParameterKeys.UPDATE_ORDER_DOMAIN_DTO to objectMapper.writeValueAsString(orderDomainDto),
                )
        }

        return userId?.let { result.plus(ParameterKeys.UPDATE_ORDER_USER_ID to userId.toString()) } ?: result
    }

    suspend fun sendCompletelyAssignPaymentEvent(
        tenantKey: String,
        orderKey: String,
        paymentTransactionId: UUID,
    ) {
        withContext(Dispatchers.IO) {
            val subscribedProcess = subscribePaymentAssignment(tenantKey, orderKey)
            logger.info { "Sending assign payment event to process ${subscribedProcess.id}" }

            processEngine.runtimeService.setVariables(
                subscribedProcess.id,
                mapOf(
                    ParameterKeys.PAYMENT_ASSIGNMENT_OPERATION to PaymentAssignmentOperation.COMPLETELY_ASSIGN.name,
                    ParameterKeys.PAYMENT_ASSIGNMENT_PAYMENT_TRANSACTION_ID to paymentTransactionId.toString(),
                ),
            )

            processEngine.runtimeService.messageEventReceived(WorkflowMessages.ASSIGN_PAYMENT, subscribedProcess.id)
        }
    }

    suspend fun sendAssignPaymentEvent(
        tenantKey: String,
        userId: UUID,
        paymentAssignmentDomainDto: PaymentAssignmentDomainDto,
    ) {
        withContext(Dispatchers.IO) {
            val orderKey = paymentAssignmentDomainDto.fetchOrderKey(tenantKey)

            val subscribedProcess = subscribePaymentAssignment(tenantKey, orderKey)
            logger.info { "Sending assign payment event to process ${subscribedProcess.id}" }

            processEngine.runtimeService.setVariables(
                subscribedProcess.id,
                mapOf(
                    ParameterKeys.PAYMENT_ASSIGNMENT_USER to userId.toString(),
                    ParameterKeys.PAYMENT_ASSIGNMENT_OPERATION to PaymentAssignmentOperation.ASSIGN.name,
                    ParameterKeys.PAYMENT_ASSIGNMENT_DTO to objectMapper.writeValueAsString(paymentAssignmentDomainDto).toString(),
                ),
            )

            processEngine.runtimeService.messageEventReceived(WorkflowMessages.ASSIGN_PAYMENT, subscribedProcess.id)
        }
    }

    private suspend fun PaymentAssignmentDomainDto.fetchOrderKey(
        tenantKey: String,
    ): String = if (this.targetType == PaymentAssignmentTargetType.ORDER) {
        this.assignedEntityKey
    } else {
        val document = documentLoadDomainApiService.findDocument(tenantKey, this.assignedEntityKey)
        document.orderKey ?: throw IllegalStateException(
            "Document ${document.key} does not reference an order but a subscription. " +
                "Payment assignment workflows for subscription documents are not yet supported.",
        )
    }

    private suspend fun PaymentAssignment.fetchOrderKey(
        tenantKey: String,
    ): String = if (this.targetType == PaymentAssignmentTargetType.ORDER) {
        this.assignedEntityKey
    } else {
        val document = documentLoadDomainApiService.findDocument(tenantKey, this.assignedEntityKey)
        document.orderKey ?: throw IllegalStateException(
            "Document ${document.key} does not reference an order but a subscription. " +
                "Payment assignment workflows for subscription documents are not yet supported.",
        )
    }

    suspend fun sendReverseAssignPaymentEvent(
        tenantKey: String,
        userId: UUID,
        paymentAssignmentKey: String,
    ) {
        withContext(Dispatchers.IO) {
            val paymentAssignment = paymentAssignmentLoadService.findPaymentAssignment(tenantKey, paymentAssignmentKey)

            val orderKey = paymentAssignment.fetchOrderKey(tenantKey)

            val subscribedProcess = subscribePaymentAssignment(tenantKey, orderKey)
            logger.info { "Sending assign payment event to process ${subscribedProcess.id}" }

            processEngine.runtimeService.setVariables(
                subscribedProcess.id,
                mapOf(
                    ParameterKeys.PAYMENT_ASSIGNMENT_USER to userId.toString(),
                    ParameterKeys.PAYMENT_ASSIGNMENT_OPERATION to PaymentAssignmentOperation.MANUAL_REVERSE.name,
                    ParameterKeys.PAYMENT_ASSIGNMENT_REVERSING_KEY to paymentAssignmentKey,
                ),
            )

            processEngine.runtimeService.messageEventReceived(WorkflowMessages.ASSIGN_PAYMENT, subscribedProcess.id)
        }
    }

    private suspend fun subscribePaymentAssignment(
        tenantKey: String,
        orderKey: String,
    ): Execution {
        val subscribedProcess =
            processEngine.runtimeService.createExecutionQuery()
                .executionTenantId(tenantKey)
                .processInstanceBusinessKey(orderKey, true)
                .messageEventSubscriptionName(WorkflowMessages.ASSIGN_PAYMENT)
                .singleResult()

        if (subscribedProcess == null) {
            val order = orderLoadService.findOrder(tenantKey, orderKey)

            logger.error {
                "Requested to assign payment to the order $orderKey but it's not in the right state: ${order.processingStatus}"
            }
            throw OrderNotInActionableStateException(
                "Requested to assign payment to the order $orderKey but it's not in the right state",
            )
        }

        return subscribedProcess
    }

    suspend fun checkInRightState(
        tenantKey: String,
        orderKey: String,
        messageName: String,
    ): Boolean = withContext(Dispatchers.IO) {
        return@withContext try {
            loadSubscribedProcess(tenantKey, orderKey, messageName, false)
            true
        } catch (e: OrderNotInActionableStateException) {
            false
        }
    }

    private suspend fun loadSubscribedProcess(
        tenantKey: String,
        orderKey: String,
        messageName: String,
        withLog: Boolean = true,
    ): Execution {
        val subscribedProcess =
            processEngine.runtimeService.createExecutionQuery()
                .executionTenantId(tenantKey)
                .processInstanceBusinessKey(orderKey, true)
                .messageEventSubscriptionName(messageName)
                .singleResult()

        if (subscribedProcess == null) {
            if (withLog) {
                val order = orderLoadService.findOrder(tenantKey, orderKey)
                logger.error {
                    "Requested to  $messageName the order $orderKey but it's not in the right state: ${order.processingStatus}"
                }
            }
            throw OrderNotInActionableStateException("Requested to $messageName the order $orderKey but it's not in the right state")
        }

        return subscribedProcess
    }

    private suspend fun loadSubscribedSubscriptionProcess(
        tenantKey: String,
        subscriptionKey: String,
        messageName: String,
        withLog: Boolean = true,
    ): Execution {
        val subscribedProcess =
            processEngine.runtimeService.createExecutionQuery()
                .executionTenantId(tenantKey)
                .processInstanceBusinessKey(subscriptionKey, true)
                .messageEventSubscriptionName(messageName)
                .singleResult()

        if (subscribedProcess == null) {
            if (withLog) {
                val subscription = subscriptionLoadService.findSubscription(tenantKey, subscriptionKey)
                logger.error {
                    "Requested to $messageName the subscription $subscriptionKey but it's not in the right state: " +
                        "${subscription.processingStatus}"
                }
            }
            throw OrderNotInActionableStateException(
                "Requested to $messageName the subscription $subscriptionKey but it's not in the right state",
            )
        }

        return subscribedProcess
    }

    suspend fun sendFinalizeOrderEvent(
        tenantKey: String,
        orderKey: String,
        userKey: String,
    ) {
        withContext(Dispatchers.IO) {
            val subscribedProcess = loadSubscribedProcess(tenantKey, orderKey, WorkflowMessages.FINALIZE_ORDER)

            logger.info { "Sending finalize event to process ${subscribedProcess.id}" }

            val userId = userLoadService.fetchUserId(userKey)

            processEngine.runtimeService.setVariables(
                subscribedProcess.id,
                mapOf(
                    ParameterKeys.FINALIZATION_USER_ID to userId.toString(),
                ),
            )

            processEngine.runtimeService.messageEventReceived(WorkflowMessages.FINALIZE_ORDER, subscribedProcess.id)
        }
    }

    suspend fun sendCancelOrderEvent(
        tenantKey: String,
        orderKey: String,
        userKey: String,
    ) {
        withContext(Dispatchers.IO) {
            val subscribedProcess = loadSubscribedProcess(tenantKey, orderKey, WorkflowMessages.CANCEL_ORDER)

            logger.info { "Sending cancellation event to process ${subscribedProcess.id}" }

            val userId = userLoadService.fetchUserId(userKey)

            processEngine.runtimeService.messageEventReceived(
                WorkflowMessages.CANCEL_ORDER,
                subscribedProcess.id,
                mapOf(
                    ParameterKeys.CANCELLATION_USER_ID to userId.toString(),
                    ParameterKeys.CANCELLATION_REASON to "Order was canceled by $userKey",
                ),
            )
        }
    }

    suspend fun sendProcessSubscriptionBillingEvent(
        tenantKey: String,
        subscriptionKey: String,
    ) {
        withContext(Dispatchers.IO) {
            val subscribedProcess = loadSubscribedSubscriptionProcess(
                tenantKey,
                subscriptionKey,
                WorkflowMessages.PROCESS_SUBSCRIPTION_BILLING,
            )

            logger.info { "Sending billing event to subscription process ${subscribedProcess.id}" }

            processEngine.runtimeService.messageEventReceived(
                WorkflowMessages.PROCESS_SUBSCRIPTION_BILLING,
                subscribedProcess.id,
            )
        }
    }

    suspend fun sendCancelSubscriptionEvent(
        tenantKey: String,
        subscriptionKey: String,
        userKey: String,
    ) {
        withContext(Dispatchers.IO) {
            val subscribedProcess = loadSubscribedSubscriptionProcess(
                tenantKey,
                subscriptionKey,
                WorkflowMessages.CANCEL_SUBSCRIPTION,
            )

            logger.info { "Sending cancel event to subscription process ${subscribedProcess.id}" }

            val userId = userLoadService.fetchUserId(userKey)

            processEngine.runtimeService.messageEventReceived(
                WorkflowMessages.CANCEL_SUBSCRIPTION,
                subscribedProcess.id,
                mapOf(
                    ParameterKeys.SUBSCRIPTION_USER_ID to userId.toString(),
                ),
            )
        }
    }

    suspend fun sendPauseSubscriptionEvent(
        tenantKey: String,
        subscriptionKey: String,
        userKey: String,
    ) {
        withContext(Dispatchers.IO) {
            val subscribedProcess = loadSubscribedSubscriptionProcess(
                tenantKey,
                subscriptionKey,
                WorkflowMessages.PAUSE_SUBSCRIPTION,
            )

            logger.info { "Sending pause event to subscription process ${subscribedProcess.id}" }

            val userId = userLoadService.fetchUserId(userKey)

            processEngine.runtimeService.messageEventReceived(
                WorkflowMessages.PAUSE_SUBSCRIPTION,
                subscribedProcess.id,
                mapOf(
                    ParameterKeys.SUBSCRIPTION_USER_ID to userId.toString(),
                ),
            )
        }
    }

    suspend fun sendResumeSubscriptionEvent(
        tenantKey: String,
        subscriptionKey: String,
        userKey: String,
    ) {
        withContext(Dispatchers.IO) {
            val subscribedProcess = loadSubscribedSubscriptionProcess(
                tenantKey,
                subscriptionKey,
                WorkflowMessages.RESUME_SUBSCRIPTION,
            )

            logger.info { "Sending resume event to subscription process ${subscribedProcess.id}" }

            val userId = userLoadService.fetchUserId(userKey)

            processEngine.runtimeService.messageEventReceived(
                WorkflowMessages.RESUME_SUBSCRIPTION,
                subscribedProcess.id,
                mapOf(
                    ParameterKeys.SUBSCRIPTION_USER_ID to userId.toString(),
                ),
            )
        }
    }

    suspend fun loadWorkflowDefinition(
        tenantKey: String,
        workflowType: WorkflowType = WorkflowType.ORDER,
    ): String {
        return withContext(Dispatchers.IO) {
            val tenantId = tenantLoadService.fetchTenantId(tenantKey)

            KotlinLogging.logger { }.info { "Loading $workflowType workflow definition for tenant $tenantKey" }

            // First try to load from workflow_definitions table (for custom workflows)
            val activeWorkflow = workflowDefinitionLoadService.findActiveByTenantIdAndWorkflowType(tenantId, workflowType)

            if (activeWorkflow != null) {
                KotlinLogging.logger { }.info {
                    "Found custom $workflowType workflow (version ${activeWorkflow.workflowVersion}) for tenant $tenantKey"
                }
                return@withContext activeWorkflow.bpmnXml
            }

            // Fallback: Load from static files (for default workflows)
            val tenantConfig = tenantConfigLoadDomainApiService.findTenantConfigByTenantId(tenantId)
            val workflowDefinitionKey = when (workflowType) {
                WorkflowType.ORDER -> tenantConfig.orderWorkflow
                WorkflowType.SUBSCRIPTION -> tenantConfig.subscriptionWorkflow
            }

            val workflowFileName = workflowDefinitionKey.replaceFirstChar {
                if (it.isLowerCase()) {
                    it.titlecase(Locale.getDefault())
                } else {
                    it.toString()
                }
            }

            KotlinLogging.logger { }.info { "Loading default workflow file: processes/$workflowFileName.bpmn for tenant $tenantKey" }
            val inputStream = javaClass.classLoader.getResourceAsStream("processes/$workflowFileName.bpmn")
                ?: error("Workflow definition $workflowFileName not found in static files")
            return@withContext BufferedReader(InputStreamReader(inputStream)).use { it.readText() }
        }
    }

    suspend fun updateWorkflowDefinition(
        tenantKey: String,
        workflowType: WorkflowType,
        bpmnXml: String,
    ): String {
        return withContext(Dispatchers.IO) {
            val tenantId = tenantLoadService.fetchTenantId(tenantKey)

            KotlinLogging.logger { }.info { "Updating $workflowType workflow definition for tenant $tenantKey" }

            // Check for running process instances with the current active workflow
            val currentActiveWorkflow = workflowDefinitionLoadService.findActiveByTenantIdAndWorkflowType(tenantId, workflowType)
            val runningInstances = if (currentActiveWorkflow != null) {
                processEngine.runtimeService.createProcessInstanceQuery()
                    .processDefinitionKey(currentActiveWorkflow.processDefinitionKey)
                    .processInstanceTenantId(tenantKey)
                    .list()
            } else {
                emptyList()
            }

            if (runningInstances.isNotEmpty()) {
                KotlinLogging.logger { }.warn {
                    "Found ${runningInstances.size} running process instances for current workflow in tenant $tenantKey. " +
                        "These will continue with the old workflow definition. New instances will use the updated workflow."
                }
            }

            // Deploy the new workflow definition to the process engine
            val processDefinitionKey = WorkflowDefinition.generateProcessDefinitionKey(tenantKey, workflowType)
            val deployment = processEngine.repositoryService.createDeployment()
                .addString("$processDefinitionKey.bpmn", bpmnXml)
                .name("$processDefinitionKey-${System.currentTimeMillis()}")
                .tenantId(tenantKey)
                .deploy()

            KotlinLogging.logger { }.info { "Deployed workflow with deployment ID: ${deployment.id}" }

            // Create new workflow definition in database
            val workflowDefinition = workflowDefinitionService.createWorkflowDefinition(
                tenantKey = tenantKey,
                workflowType = workflowType,
                name = "Custom $workflowType Workflow",
                description = "Custom workflow definition updated via web portal",
                bpmnXml = bpmnXml,
                deploymentId = deployment.id,
                createdBy = BillingSolutionConstants.SYSTEM_USER_ID
            )

            KotlinLogging.logger { }.info {
                "Created new workflow definition version ${workflowDefinition.workflowVersion} for $workflowType in tenant $tenantKey. " +
                    "Running instances: ${runningInstances.size}, will continue with old definition."
            }

            return@withContext bpmnXml
        }
    }

    suspend fun loadOpenUserTasks(): List<UserTaskDomainDto> {
        val openTasks = processEngine.taskService.createTaskQuery()
            .taskUnassigned()
            .orderByTaskCreateTime()
            .desc()
            .list()

        return openTasks.map { task ->
            val processInstance = processEngine.runtimeService.createProcessInstanceQuery()
                .processInstanceId(task.processInstanceId)
                .singleResult()

            processEngine.runtimeService.getVariable(task.processInstanceId, ParameterKeys.CANCELLATION_USER_ID) as String

            UserTaskDomainDto(
                id = task.id,
                createdAt = task.createTime.toInstant().atZone(ZoneId.of("UTC")).toLocalDateTime(),
                businessKey = processInstance.businessKey,
                variables = processEngine.runtimeService.getVariables(task.executionId),
            )
        }
    }

    suspend fun countFailedJobs(
        tenantKey: String,
    ) = processEngine.managementService.createDeadLetterJobQuery()
        .jobTenantId(tenantKey)
        .count()

    suspend fun loadFailedJobs(
        tenantKey: String,
        pageable: Pageable,
    ): ReactivePageImpl<FailedJobDomainDto> {
        return withContext(Dispatchers.IO) {
            val count = processEngine.managementService.createDeadLetterJobQuery()
                .jobTenantId(tenantKey)
                .count()

            val jobs = processEngine.managementService.createDeadLetterJobQuery()
                .jobTenantId(tenantKey)
                .orderBy(JobQueryProperty.CREATE_TIME).desc()
                .listPage(pageable.pageNumber * pageable.pageSize, pageable.pageSize)

            return@withContext ReactivePageImpl(
                jobs.map {
                    val processInstance = processEngine.runtimeService.createProcessInstanceQuery()
                        .processInstanceTenantId(tenantKey)
                        .processInstanceId(it.processInstanceId)
                        .singleResult()

                    FailedJobDomainDto(
                        id = it.id,
                        createdAt = it.createTime.toInstant().atZone(ZoneId.of("UTC")).toLocalDateTime(),
                        exceptionMessage = it.exceptionMessage ?: "No exception message",
                        businessKey = processInstance.businessKey,
                        remainingRetries = it.retries,
                    )
                }.asFlow(),
                pageable,
                count,
            )
        }
    }

    suspend fun loadHistoricActivities(
        tenantKey: String,
        pageable: Pageable,
    ): ReactivePageImpl<HistoricActivityDomainDto> {
        return withContext(Dispatchers.IO) {
            val count = processEngine.historyService.createHistoricActivityInstanceQuery()
                .activityTenantId(tenantKey)
                .activityType("serviceTask")
                .count()

            val activities = processEngine.historyService.createHistoricActivityInstanceQuery()
                .activityTenantId(tenantKey)
                .activityType("serviceTask")
                .orderBy(HistoricActivityInstanceQueryProperty.START).desc()
                .listPage(pageable.pageNumber * pageable.pageSize, pageable.pageSize)

            return@withContext ReactivePageImpl(
                activities.map {
                    val processInstance = processEngine.runtimeService.createProcessInstanceQuery()
                        .processInstanceTenantId(tenantKey)
                        .processInstanceId(it.processInstanceId)
                        .singleResult()

                    HistoricActivityDomainDto(
                        id = it.id,
                        startTime = it.startTime.toInstant().atZone(ZoneId.of("UTC")).toLocalDateTime(),
                        endTime = it.endTime?.toInstant()?.atZone(ZoneId.of("UTC"))?.toLocalDateTime(),
                        businessKey = processInstance.businessKey,
                        activityId = it.activityId,
                        activityName = it.activityName,
                        activityType = it.activityType,
                        deleteReason = it.deleteReason,
                    )
                }.asFlow(),
                pageable,
                count,
            )
        }
    }

    suspend fun retryJob(
        jobId: String,
        tenantKey: String,
    ) {
        processEngine.managementService.createDeadLetterJobQuery()
            .jobTenantId(tenantKey)
            .jobId(jobId)
            .singleResult()
            ?.let {
                processEngine.managementService.moveDeadLetterJobToExecutableJob(it.id, 1)
            }
    }

    suspend fun claimUserTask(
        task: UserTaskDomainDto,
    ) {
        val userId = "SystemUser"
        processEngine.taskService.claim(task.id, userId)
    }

    suspend fun approveUserTask(
        task: UserTaskDomainDto,
        tenantKey: String,
    ) {
        val processInstance = processEngine.runtimeService.createProcessInstanceQuery()
            .processInstanceTenantId(tenantKey)
            .processInstanceBusinessKey(task.businessKey)
            .singleResult()

        processEngine.runtimeService.setVariables(processInstance.id, mapOf(ParameterKeys.APPROVAL_APPROVED to true))
        processEngine.taskService.complete(task.id, "SystemUser")
    }

    suspend fun rejectUserTask(
        task: UserTaskDomainDto,
        tenantKey: String,
    ) {
        val processInstance = processEngine.runtimeService.createProcessInstanceQuery()
            .processInstanceTenantId(tenantKey)
            .processInstanceBusinessKey(task.businessKey)
            .singleResult()

        processEngine.runtimeService.setVariables(processInstance.id, mapOf(ParameterKeys.APPROVAL_APPROVED to false))
        processEngine.taskService.complete(task.id, "SystemUser")
    }
}
