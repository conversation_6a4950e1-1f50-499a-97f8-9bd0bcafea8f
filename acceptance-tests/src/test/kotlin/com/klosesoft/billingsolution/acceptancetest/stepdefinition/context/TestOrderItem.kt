package com.klosesoft.billingsolution.acceptancetest.stepdefinition.context

import com.klosesoft.billingsolution.generated.api.external.client.model.DebitCreditIndicatorDto
import com.klosesoft.billingsolution.generated.api.external.client.model.ItemCategoryDto
import com.klosesoft.billingsolution.generated.api.external.client.model.PropertyDto
import com.klosesoft.billingsolution.generated.api.external.client.model.TaxInformationDto
import java.math.BigDecimal

data class TestOrderItem(
    val key: String,
    val name: String,
    val articleNumber: String,
    val itemGroup: String? = null,
    val category: ItemCategoryDto,
    val description: String? = null,
    val unitNetAmount: BigDecimal,
    val quantity: BigDecimal,
    val taxInformationDto: TaxInformationDto? = null,
    val currency: String,
    val debitCreditIndicator: DebitCreditIndicatorDto,
    val properties: List<PropertyDto> = mutableListOf(),

)
