package com.klosesoft.billingsolution.acceptancetest.helper

import io.kotest.assertions.withClue
import io.kotest.matchers.shouldBe
import retrofit2.Response

object CustomAssertions {
    fun assertStatusCodeIs(
        expected: Int,
        actual: Int,
    ) {
        withClue("Response code differs - expected $expected but found $actual") {
            actual shouldBe expected
        }
    }

    fun <T> assertStatusCodeIs(
        expected: Int,
        response: Response<T>,
    ) {
        val errorBody =
            response.errorBody()?.let {
                String(it.bytes())
            } ?: "no error body set"

        withClue("Response code differs - expected $expected but found ${response.code()}, error: $errorBody") {
            response.code() shouldBe expected
        }
    }
}

fun <T> List<T>.assertAny(
    validation: (T) -> Unit,
) {
    var lastException: Throwable? = null
    forEach {
        try {
            return validation(it)
        } catch (e: AssertionError) {
            lastException = e
        }
    }

    throw lastException!!
}
