package com.klosesoft.billingsolution.acceptancetest.helper

import org.mustangproject.ZUGFeRD.TransactionCalculator

class OwnTransactionCalculator(invoice: org.mustangproject.Invoice) : TransactionCalculator(invoice) {

    fun getGrossAmount(): java.math.BigDecimal = grandTotal

    fun getNetAmount(): java.math.BigDecimal = taxBasis

    fun getTaxAmount(): java.math.BigDecimal = getVATPercentAmountMap().values.sumOf { it.calculated }
}
