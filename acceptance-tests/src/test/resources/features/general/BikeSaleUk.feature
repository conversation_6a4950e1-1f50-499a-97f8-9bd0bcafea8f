Feature: BikeSaleUK - scenarios for buying a car in UK

  @BikeSaleUK
    @Order
    @id:1
  Scenario Template: Cash sale MT940, private customer
    Given a private customer for tenant BikeSaleUK <businessSegment> from country "GB" with language "EN"
    And the customer has a shipping address
    When creating this customer
    And setting up a cash order
    And adding 1 bike item "Cube Stereo Hybrid 120 Pro 750 Allroad" for 4565.50 GBP
    And adding 1 option item "ABL" "Bike Lock - Abus Granit Super Extreme" for 114.99 GBP
    And adding 2 option items "CHX" "Child Seat - Thule Yepp 2 Maxi" for 35.19 GBP
    And creating this order
    And finalizing the order
    And receiving the final invoice
    Then the document has <final_net_amount> GBP as net amount
    And the document has <final_tax_amount> GBP as tax amount
    And the document has <final_gross_amount> GBP as total amount
    When the customer pays the final invoice with <final_gross_amount> GBP via MT940 bank transfer
    Then the final invoice is marked as paid
    When cancelling the order
    And receiving the credit note
    And the credit note document has <final_net_amount> GBP as net amount
    And the credit note document has <final_tax_amount> GBP as tax amount
    Then the credit note document has <final_gross_amount> GBP as total amount
    When the customer receives a payout of <final_gross_amount> GBP via MT940 bank transfer
    Then the order is reversed

    Examples:
      | businessSegment | final_net_amount | final_tax_amount | final_gross_amount |
      | "CUBE_CASH"     | 4750.87          | 950.17           | 5701.04            |
      | "CANYON_CASH"   | 4750.87          | 950.17           | 5701.04            |

  @BikeSaleUK
    @Order
    @id:2
  Scenario Template: Cash sale Camt053, private customer
    Given a private customer for tenant BikeSaleUK <businessSegment> from country "GB" with language "EN"
    And the customer has a shipping address
    When creating this customer
    And setting up a cash order
    And adding 1 bike item "Cube Stereo Hybrid 120 Pro 750 Allroad" for 4565.50 GBP
    And adding 1 option item "ABL" "Bike Lock - Abus Granit Super Extreme" for 114.99 GBP
    And adding 2 option items "CHX" "Child Seat - Thule Yepp 2 Maxi" for 35.19 GBP
    And creating this order
    And finalizing the order
    And receiving the final invoice
    Then the document has <final_net_amount> GBP as net amount
    And the document has <final_tax_amount> GBP as tax amount
    And the document has <final_gross_amount> GBP as total amount
    When the customer pays the final invoice with <final_gross_amount> GBP via CAMT.053 bank transfer
    Then the final invoice is marked as paid
    When cancelling the order
    And receiving the credit note
    And the credit note document has <final_net_amount> GBP as net amount
    And the credit note document has <final_tax_amount> GBP as tax amount
    Then the credit note document has <final_gross_amount> GBP as total amount
    When the customer receives a payout of <final_gross_amount> GBP via CAMT.053 bank transfer
    Then the order is reversed

    Examples:
      | businessSegment | final_net_amount | final_tax_amount | final_gross_amount |
      | "CUBE_CASH"     | 4750.87          | 950.17           | 5701.04            |
      | "CANYON_CASH"   | 4750.87          | 950.17           | 5701.04            |

  @BikeSaleUK
    @Order
    @id:3
  Scenario Template: Financed sale, private customer
    Given a private customer for tenant BikeSaleUK <businessSegment> from country "GB" with language "EN"
    And the customer has a shipping address
    When creating this customer
    And setting up a financed order
    And adding 1 bike item "Cube Stereo Hybrid 120 Pro 750 Allroad" for 4565.50 GBP
    And adding 4 option item "ABL" "Bike Lock - Abus Granit Super Extreme" for 114.99 GBP
    And adding 1 option items "CHX" "Child Seat - Thule Yepp 2 Maxi" for 35.19 GBP
    And creating this order
    And finalizing the order
    And receiving the final invoice
    Then the document has <final_net_amount> GBP as net amount
    And the document has <final_tax_amount> GBP as tax amount
    And the document has <final_gross_amount> GBP as total amount
    When cancelling the order
    And receiving the credit note
    Then the credit note document has <final_net_amount> GBP as net amount
    And the credit note document has <final_tax_amount> GBP as tax amount
    And the credit note document has <final_gross_amount> GBP as total amount

    Examples:
      | businessSegment   | final_net_amount | final_tax_amount | final_gross_amount |
      | "CUBE_FINANCED"   | 5060.65          | 1012.13          | 6072.78            |
      | "CANYON_FINANCED" | 5060.65          | 1012.13          | 6072.78            |


  @BikeSaleUK
    @Order
    @id:4
  Scenario Template: Cash sale, company customer
    Given a company customer for tenant BikeSaleUK <businessSegment> from country "GB" with language "EN"
    And the customer has a shipping address
    When creating this customer
    And setting up a cash order
    And adding 1 bike item "Cube Stereo Hybrid 120 Pro 750 Allroad" for 4565.50 GBP
    And adding 4 option item "ABL" "Bike Lock - Abus Granit Super Extreme" for 114.99 GBP
    And adding 1 option items "CHX" "Child Seat - Thule Yepp 2 Maxi" for 35.19 GBP
    And creating this order
    And finalizing the order
    And receiving the final invoice
    Then the document has <final_net_amount> GBP as net amount
    And the document has <final_tax_amount> GBP as tax amount
    And the document has <final_gross_amount> GBP as total amount
    When cancelling the order
    And receiving the credit note
    Then the credit note document has <final_net_amount> GBP as net amount
    And the credit note document has <final_tax_amount> GBP as tax amount
    And the credit note document has <final_gross_amount> GBP as total amount

    Examples:
      | businessSegment   | final_net_amount | final_tax_amount | final_gross_amount |
      | "CUBE_FINANCED"   | 5060.65          | 1012.13          | 6072.78            |
      | "CANYON_FINANCED" | 5060.65          | 1012.13          | 6072.78            |


  @BikeSaleUK
    @Order
    @id:5
  Scenario Template: Financed sale, company customer
    Given a company customer for tenant BikeSaleUK <businessSegment> from country "GB" with language "EN"
    And the customer has a shipping address
    When creating this customer
    And setting up a financed order
    And adding 1 bike item "Cube Stereo Hybrid 120 Pro 750 Allroad" for 4565.50 GBP
    And adding 4 option item "ABL" "Bike Lock - Abus Granit Super Extreme" for 114.99 GBP
    And adding 1 option items "CHX" "Child Seat - Thule Yepp 2 Maxi" for 35.19 GBP
    And creating this order
    And finalizing the order
    And receiving the final invoice
    Then the document has <final_net_amount> GBP as net amount
    And the document has <final_tax_amount> GBP as tax amount
    And the document has <final_gross_amount> GBP as total amount
    When cancelling the order
    And receiving the credit note
    Then the credit note document has <final_net_amount> GBP as net amount
    And the credit note document has <final_tax_amount> GBP as tax amount
    And the credit note document has <final_gross_amount> GBP as total amount

    Examples:
      | businessSegment   | final_net_amount | final_tax_amount | final_gross_amount |
      | "CUBE_FINANCED"   | 5060.65          | 1012.13          | 6072.78            |
      | "CANYON_FINANCED" | 5060.65          | 1012.13          | 6072.78            |

  @BikeSaleUK
    @Order
    @id:6
  Scenario Template: Draft order, cancel before finalizing
    Given a private customer for tenant BikeSaleUK <businessSegment> from country "GB" with language "EN"
    And the customer has a shipping address
    When creating this customer
    And setting up a cash order
    And adding 1 bike item "Cube Stereo Hybrid 120 Pro 750 Allroad" for 4565.50 GBP
    And adding 1 option item "ABL" "Bike Lock - Abus Granit Super Extreme" for 114.99 GBP
    And adding 1 option items "CHX" "Child Seat - Thule Yepp 2 Maxi" for 35.19 GBP
    And creating this order
    And cancelling the order
    Then the order is cancelled

    Examples:
      | businessSegment   |
      | "CUBE_FINANCED"   |
      | "CANYON_FINANCED" |
