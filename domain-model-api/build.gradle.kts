apply(plugin = "io.spring.dependency-management")
apply(plugin = "org.springframework.boot")

dependencies {
    implementation(project(":common-utils"))
    implementation(libs.jackson.module.kotlin)
    implementation(libs.spring.boot.starter.data.r2dbc)
    implementation(libs.bundles.impl.coroutines)

    testImplementation(project(":common-test"))
}

tasks.jar {
    isPreserveFileTimestamps = false
    isReproducibleFileOrder = true
}
