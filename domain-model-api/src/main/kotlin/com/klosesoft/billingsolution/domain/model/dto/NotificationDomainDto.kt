package com.klosesoft.billingsolution.domain.model.dto

import com.klosesoft.billingsolution.domain.model.valueobject.DocumentType
import com.klosesoft.billingsolution.domain.model.valueobject.NotificationSendingStatus
import com.klosesoft.billingsolution.domain.model.valueobject.NotificationType
import com.klosesoft.billingsolution.domain.model.valueobject.ReferenceType
import com.klosesoft.billingsolution.domain.model.valueobject.TargetType
import java.time.LocalDateTime

data class NotificationDomainDto(
    val type: NotificationType,
    val createdAt: LocalDateTime,
    val targetKey: String,
    val targetType: TargetType,
    val sentAt: LocalDateTime? = null,
    val sendingStatus: NotificationSendingStatus,
    val key: String,
    val references: Map<ReferenceType, String>,
    val documentType: DocumentType? = null,
)
