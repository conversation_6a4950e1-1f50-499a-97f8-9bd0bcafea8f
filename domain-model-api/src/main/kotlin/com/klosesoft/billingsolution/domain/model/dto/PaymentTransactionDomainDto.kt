package com.klosesoft.billingsolution.domain.model.dto

import com.klosesoft.billingsolution.domain.model.valueobject.Currency
import com.klosesoft.billingsolution.domain.model.valueobject.DebitCreditIndicator
import com.klosesoft.billingsolution.domain.model.valueobject.PaymentCategory
import com.klosesoft.billingsolution.domain.model.valueobject.PaymentTransactionAssignmentStatus
import java.math.BigDecimal
import java.time.LocalDate

data class PaymentTransactionDomainDto(
    val key: String,
    val debitCreditIndicator: DebitCreditIndicator,
    val assignmentStatus: PaymentTransactionAssignmentStatus,
    val bookingDate: LocalDate,
    val text: String,
    val totalAmount: BigDecimal,
    val currency: Currency,
    val paymentCategory: PaymentCategory,
)
