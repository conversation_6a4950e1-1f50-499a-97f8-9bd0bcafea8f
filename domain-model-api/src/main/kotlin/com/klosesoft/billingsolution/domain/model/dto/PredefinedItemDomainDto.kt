package com.klosesoft.billingsolution.domain.model.dto

import com.klosesoft.billingsolution.domain.model.valueobject.Currency
import com.klosesoft.billingsolution.domain.model.valueobject.DebitCreditIndicator
import com.klosesoft.billingsolution.domain.model.valueobject.ItemCategory
import java.math.BigDecimal

data class PredefinedItemDomainDto(
    val articleNumber: String,
    val debitCreditIndicator: DebitCreditIndicator,
    val category: ItemCategory,
    var itemGroup: String? = null,
    val name: String,
    val description: String? = null,
    val quantity: BigDecimal,
    val currency: Currency,
    val unitNetAmount: BigDecimal,
    val taxes: List<TaxInformationDomainDto> = ArrayList(),
    val key: String,
    val version: Long = 0,
)
