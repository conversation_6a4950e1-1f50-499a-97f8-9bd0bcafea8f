package com.klosesoft.billingsolution.domain.model.dto

import com.klosesoft.billingsolution.domain.model.valueobject.Currency
import com.klosesoft.billingsolution.domain.model.valueobject.Language
import com.klosesoft.billingsolution.domain.model.valueobject.StorageType
import java.math.BigDecimal
import java.util.Locale

data class TenantConfigDomainDto(
    val appClientId: String,
    val storageType: StorageType,
    val orderWorkflow: String,
    val orderWorkflowXml: String? = null,
    val subscriptionWorkflow: String,
    val subscriptionWorkflowXml: String? = null,
    val locale: Locale,
    val theme: String,
    val timeZone: String,
    val defaultCurrency: Currency,
    val defaultTaxRate: BigDecimal,
    val defaultLanguage: Language,
)
