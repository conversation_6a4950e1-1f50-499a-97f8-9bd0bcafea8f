/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package com.klosesoft.billingsolution.generated.api.volt.client.model

import com.klosesoft.billingsolution.generated.api.volt.client.model.ShopperDto

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * Payments - money flowing from a consumer to a merchant.
 *
 * @param currencyCode Currency in which the payment should be made, in ISO 4217 format (3 uppercase letters)
 * @param amount The amount of the transaction in 1/100 units (pence, cents etc)
 * @param type The transaction type (these are predefined)
 * @param shopper 
 * @param uniqueReference Unique reference for the payment from the merchant, must contain only alphanumeric characters
 * @param merchantInternalReference A field which identifies the transaction in the merchant's system, which can be used as a longer alternative to the uniqueReference field.  This field can contain up to 100 characters, including special ones.  - If the **uniqueReference** value is provided, then this field is optional  - If this field contains a value but **uniqueReference** is not provided, a **uniqueReference** will be automatically generated by Volt
 * @param bank UUID of the bank selected to originate the payment
 * @param callback Query string which will be returned to the merchant
 * @param notificationUrl External URL where the notification is going to be sent when the payment status changes
 * @param paymentSuccessUrl URL where the user is redirected after a successful payment. This will overwrite the application configuration set in Fuzebox. Only provide a value here if you need to overwrite it.
 * @param paymentFailureUrl URL where the user is redirected after a failed payment. This will overwrite the application configuration set in Fuzebox. Only provide a value here if you need to overwrite it.
 * @param paymentPendingUrl URL where the user is redirected if payment is pending. This will overwrite the application configuration set in Fuzebox. Only provide a value here if you need to overwrite it.
 * @param paymentCancelUrl URL where the user is redirected after cancelling a payment. This will overwrite the application configuration set in Fuzebox. Only provide a value here if you need to overwrite it.
 * @param checkoutCountries This field should be used only if you require to limit the countries that are visible to the Shopper for this specific payment. Without providing values in this field all the countries that are setup in your Fuzebox account will be available for Shopper to select from 
 */


data class PaymentDto (

    /* Currency in which the payment should be made, in ISO 4217 format (3 uppercase letters) */
    @get:JsonProperty("currencyCode")
    val currencyCode: kotlin.String,

    /* The amount of the transaction in 1/100 units (pence, cents etc) */
    @get:JsonProperty("amount")
    val amount: kotlin.Int,

    /* The transaction type (these are predefined) */
    @get:JsonProperty("type")
    val type: PaymentDto.Type,

    @get:JsonProperty("shopper")
    val shopper: ShopperDto,

    /* Unique reference for the payment from the merchant, must contain only alphanumeric characters */
    @get:JsonProperty("uniqueReference")
    val uniqueReference: kotlin.String? = null,

    /* A field which identifies the transaction in the merchant's system, which can be used as a longer alternative to the uniqueReference field.  This field can contain up to 100 characters, including special ones.  - If the **uniqueReference** value is provided, then this field is optional  - If this field contains a value but **uniqueReference** is not provided, a **uniqueReference** will be automatically generated by Volt */
    @get:JsonProperty("merchantInternalReference")
    val merchantInternalReference: kotlin.String? = null,

    /* UUID of the bank selected to originate the payment */
    @get:JsonProperty("bank")
    val bank: kotlin.String? = null,

    /* Query string which will be returned to the merchant */
    @get:JsonProperty("callback")
    val callback: kotlin.String? = null,

    /* External URL where the notification is going to be sent when the payment status changes */
    @get:JsonProperty("notificationUrl")
    val notificationUrl: kotlin.String? = null,

    /* URL where the user is redirected after a successful payment. This will overwrite the application configuration set in Fuzebox. Only provide a value here if you need to overwrite it. */
    @get:JsonProperty("paymentSuccessUrl")
    val paymentSuccessUrl: kotlin.String? = null,

    /* URL where the user is redirected after a failed payment. This will overwrite the application configuration set in Fuzebox. Only provide a value here if you need to overwrite it. */
    @get:JsonProperty("paymentFailureUrl")
    val paymentFailureUrl: kotlin.String? = null,

    /* URL where the user is redirected if payment is pending. This will overwrite the application configuration set in Fuzebox. Only provide a value here if you need to overwrite it. */
    @get:JsonProperty("paymentPendingUrl")
    val paymentPendingUrl: kotlin.String? = null,

    /* URL where the user is redirected after cancelling a payment. This will overwrite the application configuration set in Fuzebox. Only provide a value here if you need to overwrite it. */
    @get:JsonProperty("paymentCancelUrl")
    val paymentCancelUrl: kotlin.String? = null,

    /* This field should be used only if you require to limit the countries that are visible to the Shopper for this specific payment. Without providing values in this field all the countries that are setup in your Fuzebox account will be available for Shopper to select from  */
    @get:JsonProperty("checkoutCountries")
    val checkoutCountries: kotlin.collections.List<kotlin.String>? = null

) {

    /**
     * The transaction type (these are predefined)
     *
     * Values: BILL,GOODS,PERSON_TO_PERSON,OTHER,SERVICES
     */
    enum class Type(val value: kotlin.String) {
        @JsonProperty(value = "BILL") BILL("BILL"),
        @JsonProperty(value = "GOODS") GOODS("GOODS"),
        @JsonProperty(value = "PERSON_TO_PERSON") PERSON_TO_PERSON("PERSON_TO_PERSON"),
        @JsonProperty(value = "OTHER") OTHER("OTHER"),
        @JsonProperty(value = "SERVICES") SERVICES("SERVICES");
    }

}

