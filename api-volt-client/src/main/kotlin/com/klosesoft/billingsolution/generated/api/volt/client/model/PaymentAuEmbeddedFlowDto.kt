/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package com.klosesoft.billingsolution.generated.api.volt.client.model

import com.klosesoft.billingsolution.generated.api.volt.client.model.ShopperDto

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * Payments - money flowing from a consumer to a merchant.
 *
 * @param currencyCode Currency in which the payment should be made, in ISO 4217 format (3 uppercase letters)
 * @param amount The amount of the transaction in 1/100 units (pence, cents etc)
 * @param type The transaction type (these are predefined)
 * @param additionalDescription Value that represents agreement's and payment's description that will be shown to shopper at his bank.
 * @param shopper 
 * @param uniqueReference Unique reference for the payment from the merchant, must contain only alphanumeric characters
 * @param merchantInternalReference A field which identifies the transaction in the merchant's system, which can be used as a longer alternative to the uniqueReference field.  This field can contain up to 100 characters, including special ones.  - If the **uniqueReference** value is provided, then this field is optional  - If this field contains a value but **uniqueReference** is not provided, a **uniqueReference** will be automatically generated by Volt
 * @param notificationUrl External URL where the notification is going to be sent when the payment status changes
 */


data class PaymentAuEmbeddedFlowDto (

    /* Currency in which the payment should be made, in ISO 4217 format (3 uppercase letters) */
    @get:JsonProperty("currencyCode")
    val currencyCode: kotlin.String,

    /* The amount of the transaction in 1/100 units (pence, cents etc) */
    @get:JsonProperty("amount")
    val amount: kotlin.Int,

    /* The transaction type (these are predefined) */
    @get:JsonProperty("type")
    val type: PaymentAuEmbeddedFlowDto.Type,

    /* Value that represents agreement's and payment's description that will be shown to shopper at his bank. */
    @get:JsonProperty("additionalDescription")
    val additionalDescription: kotlin.String,

    @get:JsonProperty("shopper")
    val shopper: ShopperDto,

    /* Unique reference for the payment from the merchant, must contain only alphanumeric characters */
    @get:JsonProperty("uniqueReference")
    val uniqueReference: kotlin.String? = null,

    /* A field which identifies the transaction in the merchant's system, which can be used as a longer alternative to the uniqueReference field.  This field can contain up to 100 characters, including special ones.  - If the **uniqueReference** value is provided, then this field is optional  - If this field contains a value but **uniqueReference** is not provided, a **uniqueReference** will be automatically generated by Volt */
    @get:JsonProperty("merchantInternalReference")
    val merchantInternalReference: kotlin.String? = null,

    /* External URL where the notification is going to be sent when the payment status changes */
    @get:JsonProperty("notificationUrl")
    val notificationUrl: kotlin.String? = null

) {

    /**
     * The transaction type (these are predefined)
     *
     * Values: MORTGAGE,UTILITY,LOAN,DEPENDANT_SUPPORT,GAMBLING,RETAIL,SALARY,PERSONAL,GOVERNMENT,PENSION,TAX,OTHER
     */
    enum class Type(val value: kotlin.String) {
        @JsonProperty(value = "MORTGAGE") MORTGAGE("MORTGAGE"),
        @JsonProperty(value = "UTILITY") UTILITY("UTILITY"),
        @JsonProperty(value = "LOAN") LOAN("LOAN"),
        @JsonProperty(value = "DEPENDANT_SUPPORT") DEPENDANT_SUPPORT("DEPENDANT_SUPPORT"),
        @JsonProperty(value = "GAMBLING") GAMBLING("GAMBLING"),
        @JsonProperty(value = "RETAIL") RETAIL("RETAIL"),
        @JsonProperty(value = "SALARY") SALARY("SALARY"),
        @JsonProperty(value = "PERSONAL") PERSONAL("PERSONAL"),
        @JsonProperty(value = "GOVERNMENT") GOVERNMENT("GOVERNMENT"),
        @JsonProperty(value = "PENSION") PENSION("PENSION"),
        @JsonProperty(value = "TAX") TAX("TAX"),
        @JsonProperty(value = "OTHER") OTHER("OTHER");
    }

}

