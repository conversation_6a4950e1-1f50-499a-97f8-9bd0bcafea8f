/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package com.klosesoft.billingsolution.generated.api.volt.client.model


import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param code 
 * @param message 
 */


data class PostReportsPayment400ResponseExceptionDto (

    @get:JsonProperty("code")
    val code: java.math.BigDecimal,

    @get:JsonProperty("message")
    val message: kotlin.String

) {


}

