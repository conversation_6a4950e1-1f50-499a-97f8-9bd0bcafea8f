package com.klosesoft.billingsolution.generated.api.external.server.model

import java.util.Objects
import com.fasterxml.jackson.annotation.JsonCreator
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.annotation.JsonValue
import com.klosesoft.billingsolution.generated.api.external.server.model.ApprovalStatusDto
import com.klosesoft.billingsolution.generated.api.external.server.model.ApprovalTypeDto
import com.klosesoft.billingsolution.generated.api.external.server.model.TargetTypeDto
import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid

/**
 * 
 * @param key 
 * @param approvalType 
 * @param targetType 
 * @param targetKey 
 * @param description 
 * @param approvalStatus 
 * @param author 
 * @param createdAt 
 * @param version 
 * @param reviewer 
 * @param comment 
 */
data class ExternalApprovalDetailsDto(

    @get:JsonProperty("key", required = true) val key: kotlin.String,

    @field:Valid
    @get:JsonProperty("approvalType", required = true) val approvalType: ApprovalTypeDto,

    @field:Valid
    @get:JsonProperty("targetType", required = true) val targetType: TargetTypeDto,

    @get:JsonProperty("targetKey", required = true) val targetKey: kotlin.String,

    @get:JsonProperty("description", required = true) val description: kotlin.String,

    @field:Valid
    @get:JsonProperty("approvalStatus", required = true) val approvalStatus: ApprovalStatusDto,

    @get:JsonProperty("author", required = true) val author: kotlin.String,

    @get:JsonProperty("createdAt", required = true) val createdAt: java.time.OffsetDateTime,

    @get:JsonProperty("version", required = true) val version: kotlin.Long,

    @get:JsonProperty("reviewer") val reviewer: kotlin.String? = null,

    @get:JsonProperty("comment") val comment: kotlin.String? = null
    ) {

}

