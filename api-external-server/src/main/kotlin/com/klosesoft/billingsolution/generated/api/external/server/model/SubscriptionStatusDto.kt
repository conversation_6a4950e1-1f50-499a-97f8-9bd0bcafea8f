package com.klosesoft.billingsolution.generated.api.external.server.model

import java.util.Objects
import com.fasterxml.jackson.annotation.JsonValue
import com.fasterxml.jackson.annotation.JsonCreator
import com.fasterxml.jackson.annotation.JsonProperty
import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid

/**
* The status of the subscription
* Values: ACTIVE,PAUSED,CANCELLED,EXPIRED
*/
enum class SubscriptionStatusDto(@get:JsonValue val value: kotlin.String) {

    ACTIVE("ACTIVE"),
    PAUSED("PAUSED"),
    CANCELLED("CANCELLED"),
    EXPIRED("EXPIRED");

    companion object {
        @JvmStatic
        @JsonCreator
        fun forValue(value: kotlin.String): SubscriptionStatusDto {
                return values().first{it -> it.value == value}
        }
    }
}

