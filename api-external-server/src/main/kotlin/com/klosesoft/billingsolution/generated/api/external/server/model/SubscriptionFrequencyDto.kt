package com.klosesoft.billingsolution.generated.api.external.server.model

import java.util.Objects
import com.fasterxml.jackson.annotation.JsonValue
import com.fasterxml.jackson.annotation.JsonCreator
import com.fasterxml.jackson.annotation.JsonProperty
import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid

/**
* The billing frequency of the subscription
* Values: DAILY,WEEKLY,MONTHLY,QUARTERLY,YEARLY
*/
enum class SubscriptionFrequencyDto(@get:JsonValue val value: kotlin.String) {

    DAILY("DAILY"),
    WEEKLY("WEEKLY"),
    <PERSON>ON<PERSON><PERSON><PERSON>("MONTHLY"),
    QUAR<PERSON><PERSON><PERSON>("QUARTERLY"),
    YEARLY("YEARLY");

    companion object {
        @JvmStatic
        @JsonCreator
        fun forValue(value: kotlin.String): SubscriptionFrequencyDto {
                return values().first{it -> it.value == value}
        }
    }
}

