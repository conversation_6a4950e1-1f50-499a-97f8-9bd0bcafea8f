package com.klosesoft.billingsolution.workflow.steps.commission

import com.klosesoft.billingsolution.workflow.steps.AbstractWorkflowStep
import org.flowable.engine.delegate.DelegateExecution
import org.springframework.stereotype.Service

@Service
internal class CreateCommissionInvoices(private val target: CreateCommissionInvoiceService) : AbstractWorkflowStep() {

    override suspend fun exec(
        execution: DelegateExecution,
    ) = target.exec(execution)
}
