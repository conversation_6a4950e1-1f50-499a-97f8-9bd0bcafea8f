package com.klosesoft.billingsolution.workflow.steps.cancel

import com.klosesoft.billingsolution.workflow.steps.AbstractWorkflowStep
import org.flowable.engine.delegate.DelegateExecution
import org.springframework.stereotype.Service

@Service
internal class CreateCreditNote(private val target: CreateCreditNoteService) : AbstractWorkflowStep() {

    override suspend fun exec(
        execution: DelegateExecution,
    ) {
        target.exec(execution)
    }
}
