package com.klosesoft.billingsolution.workflow.steps.paymentassignment

import com.klosesoft.billingsolution.workflow.steps.AbstractWorkflowStep
import org.flowable.engine.delegate.DelegateExecution
import org.springframework.stereotype.Service

@Service
internal class CreatePaymentAssignmentPostingRecord(private val target: CreatePaymentAssignmentPostingRecordService) :
    AbstractWorkflowStep() {

    override suspend fun exec(
        execution: DelegateExecution,
    ) {
        target.exec(execution)
    }
}
