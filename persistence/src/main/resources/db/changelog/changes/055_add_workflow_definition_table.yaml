databaseChangeLog:
  - changeSet:
      id: 55
      author: pk
      comment: Create workflow_definitions table for versioned workflow management
      changes:
        - createTable:
            tableName: workflow_definitions
            columns:
              - column:
                  name: id
                  type: uuid
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: created_at
                  type: datetime
                  constraints:
                    nullable: false
              - column:
                  name: created_by
                  type: uuid
                  constraints:
                    nullable: false
              - column:
                  name: last_modified_at
                  type: datetime
                  constraints:
                    nullable: false
              - column:
                  name: last_modified_by
                  type: uuid
                  constraints:
                    nullable: false
              - column:
                  name: version
                  type: bigint
                  constraints:
                    nullable: false
              - column:
                  name: tenant_id
                  type: uuid
                  constraints:
                    nullable: false
              - column:
                  name: key
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: workflow_type
                  type: varchar(50)
                  constraints:
                    nullable: false
              - column:
                  name: name
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: description
                  type: text
                  constraints:
                    nullable: true
              - column:
                  name: bpmn_xml
                  type: text
                  constraints:
                    nullable: false
              - column:
                  name: workflow_version
                  type: integer
                  defaultValue: 1
                  constraints:
                    nullable: false
              - column:
                  name: is_active
                  type: boolean
                  defaultValue: true
                  constraints:
                    nullable: false
              - column:
                  name: deployment_id
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: process_definition_key
                  type: varchar(255)
                  constraints:
                    nullable: false
        - addForeignKeyConstraint:
            baseColumnNames: tenant_id
            baseTableName: workflow_definitions
            constraintName: fk_workflow_definitions_tenant
            referencedColumnNames: id
            referencedTableName: tenants
        - addUniqueConstraint:
            columnNames: tenant_id,key
            constraintName: uk_workflow_definitions_tenant_key
            tableName: workflow_definitions
        - sql:
            sql: |
              ALTER TABLE workflow_definitions
              ADD CONSTRAINT uk_workflow_definitions_active_per_type
              UNIQUE (tenant_id, workflow_type, is_active)
              DEFERRABLE INITIALLY DEFERRED
        - createIndex:
            indexName: idx_workflow_definitions_tenant_type
            tableName: workflow_definitions
            columns:
              - column:
                  name: tenant_id
              - column:
                  name: workflow_type
        - createIndex:
            indexName: idx_workflow_definitions_active
            tableName: workflow_definitions
            columns:
              - column:
                  name: tenant_id
              - column:
                  name: workflow_type
              - column:
                  name: is_active
        - createIndex:
            indexName: idx_workflow_definitions_process_key
            tableName: workflow_definitions
            columns:
              - column:
                  name: process_definition_key
