-- Create workflow_definitions table for versioned workflow management
CREATE TABLE workflow_definitions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL,
    key VARCHAR(255) NOT NULL,
    workflow_type VARCHAR(50) NOT NULL,
    name VA<PERSON>HAR(255) NOT NULL,
    description TEXT,
    bpmn_xml TEXT NOT NULL,
    workflow_version INTEGER NOT NULL DEFAULT 1,
    is_active BOOLEAN NOT NULL DEFAULT true,
    deployment_id VARCHAR(255),
    process_definition_key VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    created_by UUID NOT NULL,
    last_modified_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    last_modified_by UUID NOT NULL,
    version BIGINT NOT NULL DEFAULT 0,

    CONSTRAINT fk_workflow_definitions_tenant FOREIGN KEY (tenant_id) REFERENCES tenants(id),
    CONSTRAINT uk_workflow_definitions_tenant_key UNIQUE (tenant_id, key),
    CONSTRAINT uk_workflow_definitions_active_per_type UNIQUE (tenant_id, workflow_type, is_active) DEFERRABLE INITIALLY DEFERRED
);

-- Create indexes for performance
CREATE INDEX idx_workflow_definitions_tenant_type ON workflow_definitions(tenant_id, workflow_type);
CREATE INDEX idx_workflow_definitions_active ON workflow_definitions(tenant_id, workflow_type, is_active);
CREATE INDEX idx_workflow_definitions_process_key ON workflow_definitions(process_definition_key);

-- Add comments for documentation
COMMENT ON TABLE workflow_definitions IS 'Versioned workflow definitions with BPMN XML storage';
COMMENT ON COLUMN workflow_definitions.workflow_type IS 'Type of workflow: ORDER or SUBSCRIPTION';
COMMENT ON COLUMN workflow_definitions.bpmn_xml IS 'Complete BPMN XML definition';
COMMENT ON COLUMN workflow_definitions.workflow_version IS 'Version number of this workflow definition';
COMMENT ON COLUMN workflow_definitions.is_active IS 'Whether this version is currently active for new instances';
COMMENT ON COLUMN workflow_definitions.deployment_id IS 'Reference to Flowable deployment ID';
COMMENT ON COLUMN workflow_definitions.process_definition_key IS 'Key used in Flowable process engine';
