databaseChangeLog:
  - changeSet:
      id: 44
      author: pk
      changes:
        - createTable:
            tableName: suppliers
            columns:
              - column:
                  name: id
                  type: uuid
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: created_at
                  type: datetime
                  constraints:
                    nullable: false
              - column:
                  name: created_by
                  type: uuid
                  constraints:
                    nullable: false
              - column:
                  name: last_modified_at
                  type: datetime
                  constraints:
                    nullable: false
              - column:
                  name: last_modified_by
                  type: uuid
                  constraints:
                    nullable: false
              - column:
                  name: version
                  type: bigint
                  constraints:
                    nullable: false
              - column:
                  name: tenant_id
                  type: uuid
                  constraints:
                    nullable: false
              - column:
                  name: key
                  type: varchar(100)
                  constraints:
                    nullable: false
              - column:
                  name: company_name
                  type: varchar(200)
                  constraints:
                    nullable: true
              - column:
                  name: vat_id
                  type: varchar(200)
                  constraints:
                    nullable: true
              - column:
                  name: creditor_account_id
                  type: uuid
                  constraints:
                    nullable: false
              - column:
                  name: billing_address_id
                  type: uuid
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: language
                  type: varchar(50)
                  constraints:
                    nullable: false
              - column:
                  name: properties
                  type: jsonb
                  constraints:
                    nullable: false
        - addUniqueConstraint:
            columnNames: tenant_id,creditor_account_id
            constraintName: tenant_creditor_acc_id_unique_suppliers
            tableName: suppliers
        - addUniqueConstraint:
            columnNames: tenant_id,key
            constraintName: tenant_key_unique_suppliers
            tableName: suppliers
