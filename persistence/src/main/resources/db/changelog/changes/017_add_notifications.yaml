databaseChangeLog:
  - changeSet:
      id: 17
      author: pk
      changes:
        - createTable:
            tableName: notifications
            columns:
              - column:
                  name: id
                  type: uuid
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: created_at
                  type: datetime
                  constraints:
                    nullable: false
              - column:
                  name: created_by
                  type: uuid
                  constraints:
                    nullable: false
              - column:
                  name: tenant_id
                  type: uuid
                  constraints:
                    nullable: false
              - column:
                  name: key
                  type: varchar(100)
                  constraints:
                    nullable: false
              - column:
                  name: type
                  type: varchar(50)
                  constraints:
                    nullable: false
              - column:
                  name: target_key
                  type: varchar(50)
                  constraints:
                    nullable: false
              - column:
                  name: target_type
                  type: varchar(50)
                  constraints:
                    nullable: false
              - column:
                  name: document_type
                  type: varchar(50)
                  constraints:
                    nullable: true
              - column:
                  name: target_id
                  type: UUID
                  constraints:
                    nullable: false
              - column:
                  name: sent_at
                  type: datetime
                  constraints:
                    nullable: true
              - column:
                  name: sending_status
                  type: varchar(50)
                  constraints:
                    nullable: false
              - column:
                  name: references_map
                  type: jsonb
                  constraints:
                    nullable: true
        - addUniqueConstraint:
            columnNames: tenant_id,key
            constraintName: tenant_key_unique_notifications
            tableName: notifications
