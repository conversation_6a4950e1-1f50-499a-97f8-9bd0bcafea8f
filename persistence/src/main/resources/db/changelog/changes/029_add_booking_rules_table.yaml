databaseChangeLog:
  - changeSet:
      id: 29
      author: your_name
      changes:
        - createTable:
            tableName: booking_rules
            columns:
              - column:
                  name: name
                  type: varchar(100)
                  constraints:
                    nullable: false
              - column:
                  name: debit_account_number
                  type: bigint
                  constraints:
                    nullable: false
              - column:
                  name: debit_account_is_party
                  type: BOOLEAN
                  constraints:
                    nullable: false
              - column:
                  name: debit_posting_key
                  type: varchar(10)
                  constraints:
                    nullable: true
              - column:
                  name: credit_account_number
                  type: bigint
                  constraints:
                    nullable: false
              - column:
                  name: credit_account_is_party
                  type: BOOLEAN
                  constraints:
                    nullable: false
              - column:
                  name: credit_posting_key
                  type: varchar(10)
                  constraints:
                    nullable: true
              - column:
                  name: item_groups
                  type: varchar(1024)
                  constraints:
                    nullable: true
              - column:
                  name: item_article_numbers
                  type: varchar(1024)
                  constraints:
                    nullable: true
              - column:
                  name: item_categories
                  type: varchar(1024)
                  constraints:
                    nullable: true
              - column:
                  name: item_names
                  type: varchar(1024)
                  constraints:
                    nullable: true
              - column:
                  name: tax_codes
                  type: varchar(1024)
                  constraints:
                    nullable: true
              - column:
                  name: bank_account_numbers
                  type: varchar(1024)
                  constraints:
                    nullable: true
              - column:
                  name: payment_categories
                  type: varchar(1024)
                  constraints:
                    nullable: true
              - column:
                  name: target_types
                  type: varchar(1024)
                  constraints:
                    nullable: true
              - column:
                  name: debit_credit_indicator
                  type: varchar(10)
                  constraints:
                    nullable: true
              - column:
                  name: tenant_id
                  type: uuid
                  constraints:
                    nullable: false
              - column:
                  name: key
                  type: varchar(100)
                  constraints:
                    nullable: false
              - column:
                  name: id
                  type: uuid
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: created_at
                  type: datetime
                  constraints:
                    nullable: false
              - column:
                  name: created_by
                  type: uuid
                  constraints:
                    nullable: false
              - column:
                  name: last_modified_at
                  type: datetime
                  constraints:
                    nullable: false
              - column:
                  name: booking_rule_type
                  type: varchar(50)
                  constraints:
                    nullable: false
              - column:
                  name: last_modified_by
                  type: uuid
                  constraints:
                    nullable: false
              - column:
                  name: version
                  type: bigint
                  constraints:
                    nullable: false
        - addUniqueConstraint:
            columnNames: tenant_id,key
            constraintName: tenant_key_unique_booking_rules
            tableName: booking_rules
