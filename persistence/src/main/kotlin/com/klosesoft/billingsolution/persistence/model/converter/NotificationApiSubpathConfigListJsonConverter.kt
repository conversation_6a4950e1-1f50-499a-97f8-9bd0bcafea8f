package com.klosesoft.billingsolution.persistence.model.converter

import com.fasterxml.jackson.core.JsonProcessingException
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.type.TypeFactory
import com.klosesoft.billingsolution.domain.model.valueobject.NotificationApiSubpathConfig
import io.r2dbc.postgresql.codec.Json
import org.springframework.core.convert.converter.Converter
import org.springframework.data.convert.ReadingConverter
import org.springframework.data.convert.WritingConverter
import java.io.IOException

@Suppress("PLATFORM_CLASS_MAPPED_TO_KOTLIN")
@WritingConverter
internal class NotificationApiSubpathConfigListToJsonConverter(private val objectMapper: ObjectMapper) :
    Converter<java.util.List<NotificationApiSubpathConfig>, J<PERSON>> {
    override fun convert(
        source: java.util.List<NotificationApiSubpathConfig>,
    ): Json {
        try {
            return Json.of(objectMapper.writeValueAsString(source))
        } catch (e: JsonProcessingException) {
            throw IllegalArgumentException(
                "Error converting to JSON, NotificationApiSubpathConfigJson: $source",
                e,
            )
        }
    }
}

@Suppress("PLATFORM_CLASS_MAPPED_TO_KOTLIN")
@ReadingConverter
internal class JsonToNotificationApiSubpathConfigConverter(private val objectMapper: ObjectMapper) :
    Converter<Json, java.util.List<NotificationApiSubpathConfig>> {
    override fun convert(
        source: Json,
    ): java.util.List<NotificationApiSubpathConfig> {
        try {
            val javaType =
                TypeFactory.defaultInstance()
                    .constructCollectionType(MutableList::class.java, NotificationApiSubpathConfig::class.java)

            return objectMapper.readValue(source.asString(), javaType)
        } catch (e: IOException) {
            throw IllegalArgumentException(
                "Error converting to Object, NotificationApiSubpathConfigJson: $source",
                e,
            )
        }
    }
}
