package com.klosesoft.billingsolution.persistence.service

import com.klosesoft.billingsolution.common.utils.exception.EntityNotFoundException
import com.klosesoft.billingsolution.domain.model.valueobject.WorkflowType
import com.klosesoft.billingsolution.persistence.model.entity.WorkflowDefinition
import com.klosesoft.billingsolution.persistence.repository.WorkflowDefinitionRepository
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate
import org.springframework.stereotype.Service
import java.util.UUID

@Service
class WorkflowDefinitionLoadService(
    r2dbcEntityTemplate: R2dbcEntityTemplate,
) : AbstractLoadService<WorkflowDefinition>(r2dbcEntityTemplate, WorkflowDefinition::class.java) {

    @Autowired
    private lateinit var workflowDefinitionRepository: WorkflowDefinitionRepository

    suspend fun findActiveByTenantIdAndWorkflowType(
        tenantId: UUID,
        workflowType: WorkflowType,
    ): WorkflowDefinition? {
        return workflowDefinitionRepository.findByTenantIdAndWorkflowTypeAndIsActiveTrue(tenantId, workflowType)
    }

    suspend fun findAllByTenantIdAndWorkflowType(
        tenantId: UUID,
        workflowType: WorkflowType,
    ): List<WorkflowDefinition> {
        return workflowDefinitionRepository.findByTenantIdAndWorkflowTypeOrderByWorkflowVersionDesc(tenantId, workflowType)
    }

    suspend fun findByProcessDefinitionKey(
        processDefinitionKey: String,
    ): WorkflowDefinition? {
        return workflowDefinitionRepository.findByProcessDefinitionKey(processDefinitionKey)
    }

    suspend fun getNextVersionNumber(
        tenantId: UUID,
        workflowType: WorkflowType,
    ): Int {
        val existingWorkflows = findAllByTenantIdAndWorkflowType(tenantId, workflowType)
        return if (existingWorkflows.isEmpty()) {
            1
        } else {
            existingWorkflows.maxOf { it.workflowVersion } + 1
        }
    }
}
