package com.klosesoft.billingsolution.persistence.model.entity

import com.klosesoft.billingsolution.common.utils.BillingSolutionConstants
import com.klosesoft.billingsolution.common.utils.TimeProvider
import com.klosesoft.billingsolution.persistence.model.entity.base.TenantBaseEntity
import org.springframework.data.relational.core.mapping.Table
import java.time.LocalDateTime
import java.util.UUID

@Table(name = "sequence_name_mappings")
data class SequenceNameMapping(
    val name: String,
    val mappedName: String,

    override val id: UUID = UUID.randomUUID(),
    override val createdBy: UUID = BillingSolutionConstants.SYSTEM_USER_ID,
    override val createdAt: LocalDateTime = TimeProvider.nowDateTimeInUTC(),
    override val tenantId: UUID,
    override val key: String = UUID.randomUUID().toString(),
) : TenantBaseEntity
