package com.klosesoft.billingsolution.persistence.repository

import com.klosesoft.billingsolution.domain.model.valueobject.WorkflowType
import com.klosesoft.billingsolution.persistence.model.entity.WorkflowDefinition
import org.springframework.data.repository.kotlin.CoroutineCrudRepository
import org.springframework.stereotype.Repository
import java.util.UUID

@Repository
interface WorkflowDefinitionRepository : CoroutineCrudRepository<WorkflowDefinition, UUID> {
    
    suspend fun findByTenantIdAndWorkflowTypeAndIsActiveTrue(
        tenantId: UUID, 
        workflowType: WorkflowType
    ): WorkflowDefinition?
    
    suspend fun findByTenantIdAndWorkflowType(
        tenantId: UUID, 
        workflowType: WorkflowType
    ): List<WorkflowDefinition>
    
    suspend fun findByTenantIdAndWorkflowTypeOrderByWorkflowVersionDesc(
        tenantId: UUID, 
        workflowType: WorkflowType
    ): List<WorkflowDefinition>
    
    suspend fun findByTenantIdAndKey(
        tenantId: UUID, 
        key: String
    ): WorkflowDefinition?
    
    suspend fun findByProcessDefinitionKey(
        processDefinitionKey: String
    ): WorkflowDefinition?
}
