package com.klosesoft.billingsolution.persistence.model.entity

import com.klosesoft.billingsolution.common.utils.TimeProvider
import com.klosesoft.billingsolution.persistence.model.entity.base.TenantUpdatableBaseEntity
import org.springframework.data.annotation.Version
import org.springframework.data.relational.core.mapping.Table
import java.time.LocalDateTime
import java.util.UUID

@Table(name = "roles")
data class Role(
    val name: String,

    override val tenantId: UUID,
    override val key: String,
    override val id: UUID = UUID.randomUUID(),
    override val createdAt: LocalDateTime = TimeProvider.nowDateTimeInUTC(),
    override val createdBy: UUID,
    override val lastModifiedBy: UUID,
    override val lastModifiedAt: LocalDateTime = TimeProvider.nowDateTimeInUTC(),
    @Version
    override val version: Long = 0,
) : TenantUpdatableBaseEntity
