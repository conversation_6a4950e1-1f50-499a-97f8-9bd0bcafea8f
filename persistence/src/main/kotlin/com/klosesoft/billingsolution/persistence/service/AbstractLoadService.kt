package com.klosesoft.billingsolution.persistence.service

import com.klosesoft.billingsolution.common.utils.exception.EntityNotFoundException
import com.klosesoft.billingsolution.domain.model.ReactivePageImpl
import com.klosesoft.billingsolution.persistence.model.entity.base.BaseEntity
import com.klosesoft.billingsolution.persistence.service.rsql.RsqlParser
import kotlinx.coroutines.reactive.asFlow
import kotlinx.coroutines.reactive.awaitSingle
import kotlinx.coroutines.reactor.awaitSingleOrNull
import org.springframework.data.domain.Pageable
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate
import org.springframework.data.relational.core.query.Criteria
import org.springframework.data.relational.core.query.Query
import org.springframework.stereotype.Service
import java.util.UUID

@Service
abstract class AbstractLoadService<T : BaseEntity>(val r2dbcEntityTemplate: R2dbcEntityTemplate, private val type: Class<T>) {
    suspend fun findAllByTenantId(
        tenantId: UUID,
        rsqlFilter: String?,
        pageable: Pageable,
    ): ReactivePageImpl<T> {
        val criteria = Criteria.where(PersistenceFieldConstants.TENANT_ID).`is`(tenantId)
            .and(rsqlFilter?.takeIf { it.isNotBlank() }?.let { RsqlParser().parse(it, type) } ?: Criteria.empty())

        val entries = r2dbcEntityTemplate.select(Query.query(criteria).with(pageable), type).asFlow()
        val count = r2dbcEntityTemplate.count(Query.query(criteria), type).awaitSingle()

        return ReactivePageImpl(entries, pageable, count)
    }

    suspend fun findAllByTenantIdAndBusinessSegmentId(
        tenantId: UUID,
        businessSegmentId: UUID,
        rsqlFilter: String?,
        pageable: Pageable,
    ): ReactivePageImpl<T> {
        val criteria = Criteria.where(PersistenceFieldConstants.TENANT_ID).`is`(tenantId)
            .and(Criteria.where(PersistenceFieldConstants.BUSINESS_SEGMENT_ID).`is`(businessSegmentId))
            .and(rsqlFilter?.takeIf { it.isNotBlank() }?.let { RsqlParser().parse(it, type) } ?: Criteria.empty())

        val entries = r2dbcEntityTemplate.select(Query.query(criteria).with(pageable), type).asFlow()
        val count = r2dbcEntityTemplate.count(Query.query(criteria), type).awaitSingle()

        return ReactivePageImpl(entries, pageable, count)
    }

    suspend fun findById(
        id: UUID,
    ): T = r2dbcEntityTemplate.selectOne(Query.query(Criteria.where(PersistenceFieldConstants.ID).`is`(id)), type).awaitSingleOrNull()
        ?: throw EntityNotFoundException("${type.name} with id $id not found")

    suspend fun findByTenantIdAndKey(
        tenantId: UUID,
        key: String,
    ): T = r2dbcEntityTemplate.selectOne(
        Query.query(
            Criteria.where(PersistenceFieldConstants.TENANT_ID).`is`(tenantId)
                .and(Criteria.where(PersistenceFieldConstants.KEY).`is`(key)),
        ),
        type,
    ).awaitSingleOrNull()
        ?: throw EntityNotFoundException("${type.name} with tenantId $tenantId and key $key not found")

    suspend fun findByTenantIdAndBusinessSegmentIdAndKey(
        tenantId: UUID,
        businessSegmentId: UUID,
        key: String,
    ): T = r2dbcEntityTemplate.selectOne(
        Query.query(
            Criteria.where(
                PersistenceFieldConstants.TENANT_ID,
            ).`is`(tenantId)
                .and(Criteria.where(PersistenceFieldConstants.BUSINESS_SEGMENT_ID).`is`(businessSegmentId))
                .and(Criteria.where(PersistenceFieldConstants.KEY).`is`(key)),
        ),
        type,
    ).awaitSingleOrNull()
        ?: throw EntityNotFoundException(
            "${type.name} with tenantId $tenantId and businessSegmentId $businessSegmentId and key $key not found",
        )
}
