package com.klosesoft.billingsolution.persistence.model.entity

import com.klosesoft.billingsolution.common.utils.TimeProvider
import com.klosesoft.billingsolution.domain.model.valueobject.BalanceCaseItemType
import com.klosesoft.billingsolution.domain.model.valueobject.Currency
import com.klosesoft.billingsolution.domain.model.valueobject.DebitCreditIndicator
import com.klosesoft.billingsolution.persistence.model.entity.base.TenantBaseEntity
import org.springframework.data.relational.core.mapping.Table
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

@Table(name = "balance_case_items")
data class BalanceCaseItem(
    var balanceCaseId: UUID,
    val referenceKey: String? = null,
    var postingDate: LocalDate,
    val debitCreditIndicator: DebitCreditIndicator,
    val type: BalanceCaseItemType,
    val totalAmount: BigDecimal,
    val currency: Currency,

    override val tenantId: UUID,
    override val key: String,
    override val id: UUID = UUID.randomUUID(),
    override val createdAt: LocalDateTime = TimeProvider.nowDateTimeInUTC(),
    override val createdBy: UUID,
) : TenantBaseEntity
