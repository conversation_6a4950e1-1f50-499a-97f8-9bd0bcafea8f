package com.klosesoft.billingsolution.persistence.repository

import com.klosesoft.billingsolution.persistence.model.entity.OrderSupplier
import org.springframework.data.repository.kotlin.CoroutineSortingRepository
import org.springframework.stereotype.Repository
import java.util.UUID

@Repository
internal interface OrderSupplierRepository : CoroutineSortingRepository<OrderSupplier, UUID> {
    suspend fun findByOrderIdAndSupplierId(
        orderId: UUID,
        supplierId: UUID,
    ): OrderSupplier
}
