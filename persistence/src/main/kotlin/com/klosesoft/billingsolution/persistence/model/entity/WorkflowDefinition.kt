package com.klosesoft.billingsolution.persistence.model.entity

import com.klosesoft.billingsolution.common.utils.TimeProvider
import com.klosesoft.billingsolution.domain.model.valueobject.WorkflowType
import com.klosesoft.billingsolution.persistence.model.entity.base.TenantUpdatableBaseEntity
import org.springframework.data.annotation.Version
import org.springframework.data.relational.core.mapping.Table
import java.time.LocalDateTime
import java.util.UUID

@Table(name = "workflow_definitions")
data class WorkflowDefinition(
    val workflowType: WorkflowType,
    val name: String,
    val description: String? = null,
    val bpmnXml: String, // CLOB for storing the BPMN XML
    val workflowVersion: Int = 1,
    val isActive: Boolean = true,
    val deploymentId: String? = null, // Reference to Flowable deployment
    val processDefinitionKey: String, // Key used in Flowable
    
    override val tenantId: UUID,
    override val key: String,
    override val id: UUID = UUID.randomUUID(),
    override val createdAt: LocalDateTime = TimeProvider.nowDateTimeInUTC(),
    override val createdBy: UUID,
    override val lastModifiedBy: UUID,
    override val lastModifiedAt: LocalDateTime = TimeProvider.nowDateTimeInUTC(),
    @Version
    override val version: Long = 0,
) : TenantUpdatableBaseEntity {
    
    companion object {
        fun generateKey(tenantKey: String, workflowType: WorkflowType, workflowVersion: Int): String {
            return "${tenantKey}_${workflowType.name.lowercase()}_v${workflowVersion}"
        }
        
        fun generateProcessDefinitionKey(tenantKey: String, workflowType: WorkflowType): String {
            return "custom_${tenantKey}_${workflowType.name.lowercase()}"
        }
    }
}
