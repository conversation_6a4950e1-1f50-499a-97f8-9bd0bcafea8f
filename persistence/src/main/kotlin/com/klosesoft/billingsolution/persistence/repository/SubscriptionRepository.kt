package com.klosesoft.billingsolution.persistence.repository

import com.klosesoft.billingsolution.persistence.model.entity.Subscription
import kotlinx.coroutines.flow.Flow
import org.springframework.data.repository.kotlin.CoroutineSortingRepository
import org.springframework.stereotype.Repository
import java.util.UUID

@Repository
internal interface SubscriptionRepository : CoroutineSortingRepository<Subscription, UUID> {

    suspend fun findByTenantIdAndKey(
        tenantId: UUID,
        key: String,
    ): Subscription?

    suspend fun findByTenantIdAndCustomerId(
        tenantId: UUID,
        customerId: UUID,
    ): Flow<Subscription>

    suspend fun findByBalanceCaseId(
        balanceCaseId: UUID,
    ): Subscription?

    fun findByTenantId(
        tenantId: UUID,
    ): Flow<Subscription>
}
