package com.klosesoft.billingsolution.databasefiller.creator

import com.klosesoft.billingsolution.databasefiller.DomainUtil
import com.klosesoft.billingsolution.domain.model.dto.SubscriptionDomainDto
import com.klosesoft.billingsolution.domain.model.valueobject.Currency
import com.klosesoft.billingsolution.domain.model.valueobject.PropertyKey
import com.klosesoft.billingsolution.domain.model.valueobject.SubscriptionFrequency
import com.klosesoft.billingsolution.domain.model.valueobject.SubscriptionStatus
import java.math.BigDecimal
import java.time.LocalDate

object SubscriptionCreator {

    fun createMonthlyCarLeaseSubscription(
        key: String,
        customerKey: String,
        businessSegmentKey: String,
        vehicleModel: String,
        monthlyAmount: BigDecimal,
        currency: Currency,
        startDate: LocalDate,
        endDate: LocalDate? = null,
        properties: MutableMap<PropertyKey, String> = mutableMapOf(),
    ): SubscriptionDomainDto {
        // Add VIN if not provided
        if (!properties.containsKey(PropertyKey.VIN)) {
            properties[PropertyKey.VIN] = DomainUtil.randomVIN()
        }

        return SubscriptionDomainDto(
            key = key,
            name = "$vehicleModel Monthly Lease",
            description = "Monthly car lease subscription for $vehicleModel",
            customerKey = customerKey,
            businessSegmentKey = businessSegmentKey,
            status = SubscriptionStatus.ACTIVE,
            frequency = SubscriptionFrequency.MONTHLY,
            amount = monthlyAmount,
            currency = currency,
            startDate = startDate,
            endDate = endDate,
            nextBillingDate = startDate.plusMonths(1),
            properties = properties,
        )
    }

    fun createWeeklyCarLeaseSubscription(
        key: String,
        customerKey: String,
        businessSegmentKey: String,
        vehicleModel: String,
        weeklyAmount: BigDecimal,
        currency: Currency,
        startDate: LocalDate,
        endDate: LocalDate? = null,
        properties: MutableMap<PropertyKey, String> = mutableMapOf(),
    ): SubscriptionDomainDto {
        // Add VIN if not provided
        if (!properties.containsKey(PropertyKey.VIN)) {
            properties[PropertyKey.VIN] = DomainUtil.randomVIN()
        }

        return SubscriptionDomainDto(
            key = key,
            name = "$vehicleModel Weekly Lease",
            description = "Weekly car lease subscription for $vehicleModel",
            customerKey = customerKey,
            businessSegmentKey = businessSegmentKey,
            status = SubscriptionStatus.ACTIVE,
            frequency = SubscriptionFrequency.WEEKLY,
            amount = weeklyAmount,
            currency = currency,
            startDate = startDate,
            endDate = endDate,
            nextBillingDate = startDate.plusWeeks(1),
            properties = properties,
        )
    }

    fun createMaintenanceSubscription(
        key: String,
        customerKey: String,
        businessSegmentKey: String,
        vehicleModel: String,
        monthlyAmount: BigDecimal,
        currency: Currency,
        startDate: LocalDate,
        endDate: LocalDate? = null,
    ): SubscriptionDomainDto = SubscriptionDomainDto(
        key = key,
        name = "$vehicleModel Maintenance Package",
        description = "Monthly maintenance package for $vehicleModel including regular service, oil changes, and basic repairs",
        customerKey = customerKey,
        businessSegmentKey = businessSegmentKey,
        status = SubscriptionStatus.ACTIVE,
        frequency = SubscriptionFrequency.MONTHLY,
        amount = monthlyAmount,
        currency = currency,
        startDate = startDate,
        endDate = endDate,
        nextBillingDate = startDate.plusMonths(1),
    )

    fun createInsuranceSubscription(
        key: String,
        customerKey: String,
        businessSegmentKey: String,
        vehicleModel: String,
        monthlyAmount: BigDecimal,
        currency: Currency,
        startDate: LocalDate,
        endDate: LocalDate? = null,
    ): SubscriptionDomainDto = SubscriptionDomainDto(
        key = key,
        name = "$vehicleModel Comprehensive Insurance",
        description = "Monthly comprehensive insurance coverage for $vehicleModel",
        customerKey = customerKey,
        businessSegmentKey = businessSegmentKey,
        status = SubscriptionStatus.ACTIVE,
        frequency = SubscriptionFrequency.MONTHLY,
        amount = monthlyAmount,
        currency = currency,
        startDate = startDate,
        endDate = endDate,
        nextBillingDate = startDate.plusMonths(1),
    )
}
