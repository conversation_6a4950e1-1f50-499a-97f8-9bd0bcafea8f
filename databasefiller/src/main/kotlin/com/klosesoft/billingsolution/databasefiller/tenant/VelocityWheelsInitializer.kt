package com.klosesoft.billingsolution.databasefiller.tenant

import com.klosesoft.billingsolution.common.utils.BillingSolutionConstants
import com.klosesoft.billingsolution.common.utils.BillingSolutionConstants.SYSTEM_USER_ID
import com.klosesoft.billingsolution.common.utils.BillingSolutionConstants.SYSTEM_USER_KEY
import com.klosesoft.billingsolution.common.utils.BillingSolutionConstants.TENANT_KEY_VELOCITY_WHEELS
import com.klosesoft.billingsolution.common.utils.TimeProvider
import com.klosesoft.billingsolution.databasefiller.DomainUtil
import com.klosesoft.billingsolution.databasefiller.creator.BookkeepingCreator
import com.klosesoft.billingsolution.databasefiller.creator.CustomerCreator
import com.klosesoft.billingsolution.databasefiller.creator.NotificationReceiverCreator
import com.klosesoft.billingsolution.databasefiller.creator.PaymentCreator
import com.klosesoft.billingsolution.databasefiller.creator.ReportConfigCreator
import com.klosesoft.billingsolution.databasefiller.creator.SubscriptionCreator
import com.klosesoft.billingsolution.databasefiller.creator.TenantCreator
import com.klosesoft.billingsolution.databasefiller.creator.TranslationCreator
import com.klosesoft.billingsolution.domain.logic.api.job.report.ReportJob
import com.klosesoft.billingsolution.domain.logic.api.service.WorkflowApiService
import com.klosesoft.billingsolution.domain.logic.service.SubscriptionItemService
import com.klosesoft.billingsolution.domain.logic.service.billing.TranslationService
import com.klosesoft.billingsolution.domain.logic.service.bookkeeping.BookingAccountService
import com.klosesoft.billingsolution.domain.logic.service.bookkeeping.BookingRuleService
import com.klosesoft.billingsolution.domain.logic.service.common.AddressService
import com.klosesoft.billingsolution.domain.logic.service.common.BusinessSegmentService
import com.klosesoft.billingsolution.domain.logic.service.common.CustomerService
import com.klosesoft.billingsolution.domain.logic.service.common.NotificationApiReceiverService
import com.klosesoft.billingsolution.domain.logic.service.common.TenantService
import com.klosesoft.billingsolution.domain.logic.service.payment.PaymentAccountService
import com.klosesoft.billingsolution.domain.logic.service.payment.PaymentCategorizationRuleService
import com.klosesoft.billingsolution.domain.logic.service.user.RoleService
import com.klosesoft.billingsolution.domain.logic.service.user.UserService
import com.klosesoft.billingsolution.domain.model.dto.AddressDomainDto
import com.klosesoft.billingsolution.domain.model.dto.SubscriptionItemRequestDomainDto
import com.klosesoft.billingsolution.domain.model.dto.TaxInformationDomainDto
import com.klosesoft.billingsolution.domain.model.valueobject.BookingAccountType
import com.klosesoft.billingsolution.domain.model.valueobject.BookingRuleType
import com.klosesoft.billingsolution.domain.model.valueobject.Currency
import com.klosesoft.billingsolution.domain.model.valueobject.DebitCreditIndicator
import com.klosesoft.billingsolution.domain.model.valueobject.EmailTranslationKey
import com.klosesoft.billingsolution.domain.model.valueobject.Feature
import com.klosesoft.billingsolution.domain.model.valueobject.GeneralDocumentTranslationKey
import com.klosesoft.billingsolution.domain.model.valueobject.ItemCategory
import com.klosesoft.billingsolution.domain.model.valueobject.Language
import com.klosesoft.billingsolution.domain.model.valueobject.PaymentAccountType
import com.klosesoft.billingsolution.domain.model.valueobject.PaymentCategory
import com.klosesoft.billingsolution.domain.model.valueobject.PropertyKey
import com.klosesoft.billingsolution.domain.model.valueobject.ReportFormat
import com.klosesoft.billingsolution.domain.model.valueobject.ReportInterval
import com.klosesoft.billingsolution.domain.model.valueobject.ReportType
import com.klosesoft.billingsolution.domain.model.valueobject.Right
import com.klosesoft.billingsolution.domain.model.valueobject.TaxType
import com.klosesoft.billingsolution.domain.model.valueobject.TranslationType
import com.klosesoft.billingsolution.persistence.model.entity.Customer
import com.klosesoft.billingsolution.persistence.model.entity.Role
import com.klosesoft.billingsolution.persistence.model.entity.Subscription
import com.klosesoft.billingsolution.persistence.model.entity.payment.PaymentCategorizationRule
import com.klosesoft.billingsolution.persistence.service.CustomerLoadService
import com.klosesoft.billingsolution.persistence.service.RoleLoadService
import com.klosesoft.billingsolution.persistence.service.SubscriptionLoadService
import com.klosesoft.billingsolution.persistence.service.TenantLoadService
import com.klosesoft.billingsolution.persistence.service.UserLoadService
import com.klosesoft.billingsolution.workflow.WorkflowDeploymentService
import org.springframework.core.env.Environment
import org.springframework.core.io.ResourceLoader
import org.springframework.stereotype.Component
import java.io.IOException
import java.math.BigDecimal
import java.time.LocalDate
import java.util.Locale
import java.util.UUID

@Component
class VelocityWheelsInitializer(
    private val tenantService: TenantService,
    private val tenantLoadService: TenantLoadService,
    private val customerLoadService: CustomerLoadService,
    private val businessSegmentService: BusinessSegmentService,
    private val userService: UserService,
    private val userLoadService: UserLoadService,
    private val customerService: CustomerService,
    private val environment: Environment,
    private val bookingAccountService: BookingAccountService,
    private val bookingRuleService: BookingRuleService,
    private val roleService: RoleService,
    private val roleLoadService: RoleLoadService,
    private val paymentAccountService: PaymentAccountService,
    private val paymentCategorizationRuleService: PaymentCategorizationRuleService,
    private val reportConfigCreator: ReportConfigCreator,
    private val reportJob: ReportJob,
    private val translationService: TranslationService,
    private val resourceLoader: ResourceLoader,
    private val addressService: AddressService,
    private val notificationApiReceiverService: NotificationApiReceiverService,
    private val workflowApiService: WorkflowApiService,
    private val workflowDeploymentService: WorkflowDeploymentService,
    private val subscriptionItemService: SubscriptionItemService,
    private val subscriptionLoadService: SubscriptionLoadService,
) {

    private companion object {
        const val PREMIUM_LEASING = "PREMIUM_LEASING"
        const val ECONOMY_LEASING = "ECONOMY_LEASING"
    }

    suspend fun setupTenant() {
        createTenant()
        createBusinessSegments()
        createTenantWebPortalUsers()
        createBookingkeepingConfig()
        createPaymentConfig()
        createTranslations()

        if (environment.activeProfiles.contains("demo")) {
            addCustomers()
            addSubscriptions()
            createReports()
        }

        if (environment.activeProfiles.contains("cucumber")) {
            createCucumberNotificationApiReceiver()
        }
    }

    private suspend fun createReports() {
        val tenantId = tenantLoadService.findTenantByKey(TENANT_KEY_VELOCITY_WHEELS).id
        reportConfigCreator.createConfig(tenantId, ReportType.PAYMENT, ReportFormat.JSON, ReportInterval.DAILY, "UTC")
        reportConfigCreator.createConfig(tenantId, ReportType.SUBLEDGER, ReportFormat.JSON, ReportInterval.DAILY, "UTC")

        reportJob.generateSubledgerReport()
        reportJob.generatePaymentReport()
        reportJob.generateApprovalReport()
    }

    private suspend fun createTenantWebPortalUsers() {
        val tenantId = tenantLoadService.findTenantByKey(TENANT_KEY_VELOCITY_WHEELS).id

        createRoles(tenantId)
        createUserRoles(tenantId)
        createRoleRights(tenantId)
    }

    private suspend fun createRoles(
        tenantId: UUID,
    ) {
        roleService.createRole(createRole("Read-only", tenantId), emptyList(), SYSTEM_USER_ID)
        roleService.createRole(createRole("Full Access", tenantId), emptyList(), SYSTEM_USER_ID)
        roleService.createRole(createRole("Administration", tenantId), emptyList(), SYSTEM_USER_ID)
    }

    private suspend fun createRole(
        name: String,
        tenantId: UUID,
    ) = Role(
        name = name,
        createdBy = SYSTEM_USER_ID,
        lastModifiedBy = SYSTEM_USER_ID,
        key = name,
        tenantId = tenantId,
    )

    private suspend fun createUserRoles(
        tenantId: UUID,
    ) {
        setRolesForUser("<EMAIL>", listOf("Full Access", "Administration"), tenantId)
        setRolesForUser("<EMAIL>", listOf("Full Access", "Administration"), tenantId)
        setRolesForUser("<EMAIL>", listOf("Full Access", "Administration"), tenantId)

        setRolesForUser("<EMAIL>", listOf("Read-only"), tenantId)
    }

    private suspend fun setRolesForUser(
        key: String,
        roleKeys: List<String>,
        tenantUUID: UUID,
    ) {
        val user = userLoadService.findByKey(key)

        val roles = mutableListOf<Role>()

        roleKeys.forEach { roleKey ->
            val role = roleLoadService.findByTenantIdAndKey(tenantUUID, roleKey)
            roles.add(role)
        }

        userService.updateUser(user, roles, SYSTEM_USER_ID)
    }

    private suspend fun createRoleRights(
        tenantId: UUID,
    ) {
        addReadOnlyRights(tenantId)
        addFullAccessRights(tenantId)
        addAdminRights(tenantId)
    }

    private suspend fun addReadOnlyRights(
        tenantId: UUID,
    ) {
        val role = roleLoadService.findByTenantIdAndKey(tenantId, "Read-only")
        roleService.updateRole(
            role,
            Right.entries.filter {
                it.category == Right.Category.READ
            },
            SYSTEM_USER_ID,
        )
    }

    private suspend fun addFullAccessRights(
        tenantId: UUID,
    ) {
        val role = roleLoadService.findByTenantIdAndKey(tenantId, "Full Access")
        roleService.updateRole(
            role,
            Right.entries.filter {
                it.category == Right.Category.READ || it.category == Right.Category.WRITE
            },
            SYSTEM_USER_ID,
        )
    }

    private suspend fun addAdminRights(
        tenantId: UUID,
    ) {
        val role = roleLoadService.findByTenantIdAndKey(tenantId, "Administration")
        roleService.updateRole(
            role,
            Right.entries.filter {
                it.category == Right.Category.ADMIN || it.category == Right.Category.INTERNAL
            },
            SYSTEM_USER_ID,
        )
    }

    private suspend fun createTranslations() {
        val tenantId = tenantLoadService.findTenantByKey(TENANT_KEY_VELOCITY_WHEELS).id

        translationService.createTranslations(
            listOf(
                // General document translations
                TranslationCreator.createTranslation(tenantId, GeneralDocumentTranslationKey.COPY.name, Language.EN, "COPY"),

                // Document titles
                TranslationCreator.createTranslation(
                    tenantId = tenantId,
                    key = "DOCUMENT_TITLE",
                    language = Language.EN,
                    value = "Vehicle Lease Agreement",
                ),
                TranslationCreator.createTranslation(
                    tenantId = tenantId,
                    key = "DOCUMENT_TITLE",
                    language = Language.DE,
                    value = "Fahrzeug-Leasingvertrag",
                ),

                // Document headers
                TranslationCreator.createTranslation(
                    tenantId = tenantId,
                    key = "HEADER_DEPOSIT",
                    language = Language.EN,
                    value = "Lease Deposit Invoice",
                ),
                TranslationCreator.createTranslation(
                    tenantId = tenantId,
                    key = "HEADER_FINAL",
                    language = Language.EN,
                    value = "Lease Invoice",
                ),
                TranslationCreator.createTranslation(
                    tenantId = tenantId,
                    key = "HEADER_REVERSED",
                    language = Language.EN,
                    value = "Credit Note",
                ),

                // Customer and document information labels
                TranslationCreator.createTranslation(
                    tenantId = tenantId,
                    key = "CUSTOMER_LABEL",
                    language = Language.EN,
                    value = "Customer:",
                ),
                TranslationCreator.createTranslation(
                    tenantId = tenantId,
                    key = "DATE_LABEL",
                    language = Language.EN,
                    value = "Date:",
                ),
                TranslationCreator.createTranslation(
                    tenantId = tenantId,
                    key = "ORDER_LABEL",
                    language = Language.EN,
                    value = "Lease Agreement:",
                ),
                TranslationCreator.createTranslation(
                    tenantId = tenantId,
                    key = "INVOICE_NUMBER_LABEL",
                    language = Language.EN,
                    value = "Invoice Number:",
                ),
                TranslationCreator.createTranslation(
                    tenantId = tenantId,
                    key = "INVOICE_DATE_LABEL",
                    language = Language.EN,
                    value = "Invoice Date:",
                ),

                // Vehicle information labels
                TranslationCreator.createTranslation(
                    tenantId = tenantId,
                    key = "VEHICLE_INFO_LABEL",
                    language = Language.EN,
                    value = "Vehicle Information",
                ),
                TranslationCreator.createTranslation(
                    tenantId = tenantId,
                    key = "VIN_LABEL",
                    language = Language.EN,
                    value = "VIN:",
                ),
                TranslationCreator.createTranslation(
                    tenantId = tenantId,
                    key = "LEASE_PERIOD_LABEL",
                    language = Language.EN,
                    value = "Lease Period:",
                ),
                TranslationCreator.createTranslation(
                    tenantId = tenantId,
                    key = "LEASE_START_LABEL",
                    language = Language.EN,
                    value = "Lease Start:",
                ),

                // Item table headers - these are the missing ones causing the error
                TranslationCreator.createTranslation(
                    tenantId = tenantId,
                    key = "SERVICE_TYPE_LABEL",
                    language = Language.EN,
                    value = "Service Type",
                ),
                TranslationCreator.createTranslation(
                    tenantId = tenantId,
                    key = "DESCRIPTION_LABEL",
                    language = Language.EN,
                    value = "Description",
                ),
                TranslationCreator.createTranslation(
                    tenantId = tenantId,
                    key = "QUANTITY_LABEL",
                    language = Language.EN,
                    value = "Quantity",
                ),
                TranslationCreator.createTranslation(
                    tenantId = tenantId,
                    key = "UNIT_PRICE_LABEL",
                    language = Language.EN,
                    value = "Unit Price",
                ),
                TranslationCreator.createTranslation(
                    tenantId = tenantId,
                    key = "TOTAL_PRICE_LABEL",
                    language = Language.EN,
                    value = "Total Price",
                ),

                // Total labels
                TranslationCreator.createTranslation(
                    tenantId = tenantId,
                    key = "NET_TOTAL_LABEL",
                    language = Language.EN,
                    value = "Net Total:",
                ),
                TranslationCreator.createTranslation(
                    tenantId = tenantId,
                    key = "TAX_LABEL",
                    language = Language.EN,
                    value = "VAT (20%):",
                ),
                TranslationCreator.createTranslation(
                    tenantId = tenantId,
                    key = "GROSS_TOTAL_LABEL",
                    language = Language.EN,
                    value = "Total Amount:",
                ),

                // Payment and footer labels
                TranslationCreator.createTranslation(
                    tenantId = tenantId,
                    key = "PAYMENT_INFO_LABEL",
                    language = Language.EN,
                    value = "Payment Information",
                ),
                TranslationCreator.createTranslation(
                    tenantId = tenantId,
                    key = "THANK_YOU_LABEL",
                    language = Language.EN,
                    value = "Thank you for choosing Velocity Wheels!",
                ),

                // Email translations for subscription invoice notifications
                TranslationCreator.createTranslation(
                    tenantId = tenantId,
                    key = EmailTranslationKey.EMAIL_SUBJECT.name,
                    language = Language.EN,
                    value = "Velocity Wheels - Lease Invoice for {{SUBSCRIPTION_KEY}}",
                    translationType = TranslationType.EMAIL,
                ),
                TranslationCreator.createTranslation(
                    tenantId = tenantId,
                    key = EmailTranslationKey.EMAIL_BODY.name,
                    language = Language.EN,
                    value = "Dear {{CUSTOMER_NAME}},\n\n" +
                        "Please find attached your lease invoice for subscription {{SUBSCRIPTION_KEY}}.\n\n" +
                        "If you have any questions regarding your lease agreement or this invoice, " +
                        "please don't hesitate to contact <NAME_EMAIL> or +44 20 7946 0958.\n\n" +
                        "Thank you for choosing Velocity Wheels for your vehicle leasing needs.\n\n" +
                        "Best regards,\n" +
                        "The Velocity Wheels Team",
                    translationType = TranslationType.EMAIL,
                ),
            ),
        )
    }

    private suspend fun createBookingkeepingConfig() {
        val tenantId = tenantLoadService.fetchTenantId(TENANT_KEY_VELOCITY_WHEELS)

        createBookingAccounts(tenantId)
        createBookingRules(tenantId)
    }

    private suspend fun createBookingAccounts(
        tenantId: UUID,
    ) {
        bookingAccountService.createBookingAccount(
            BookkeepingCreator.createBookingAccount(
                tenantId = tenantId,
                accountNumber = 1000L,
                name = "Cash and Bank",
                description = "Cash and bank accounts",
                bookingAccountType = BookingAccountType.ASSET,
            ),
        )

        bookingAccountService.createBookingAccount(
            BookkeepingCreator.createBookingAccount(
                tenantId = tenantId,
                accountNumber = 1200L,
                name = "Accounts Receivable",
                description = "Customer receivables",
                bookingAccountType = BookingAccountType.ASSET,
            ),
        )

        bookingAccountService.createBookingAccount(
            BookkeepingCreator.createBookingAccount(
                tenantId = tenantId,
                accountNumber = 4000L,
                name = "Leasing Revenue",
                description = "Revenue from vehicle leasing",
                bookingAccountType = BookingAccountType.REVENUE,
            ),
        )

        bookingAccountService.createBookingAccount(
            BookkeepingCreator.createBookingAccount(
                tenantId = tenantId,
                accountNumber = 2000L,
                name = "Accounts Payable",
                description = "Supplier payables",
                bookingAccountType = BookingAccountType.LIABILITY,
            ),
        )

        bookingAccountService.createBookingAccount(
            BookkeepingCreator.createBookingAccount(
                tenantId = tenantId,
                accountNumber = 1760L,
                name = "VAT Input",
                description = "Input VAT",
                bookingAccountType = BookingAccountType.ASSET,
            ),
        )

        bookingAccountService.createBookingAccount(
            BookkeepingCreator.createBookingAccount(
                tenantId = tenantId,
                accountNumber = 3806L,
                name = "VAT Output",
                description = "Output VAT",
                bookingAccountType = BookingAccountType.LIABILITY,
            ),
        )
    }

    private suspend fun createBookingRules(
        tenantId: UUID,
    ) {
        bookingRuleService.createBookingRule(
            BookkeepingCreator.createBookingRule(
                tenantId = tenantId,
                name = "Leasing Revenue Rule",
                bookingRuleType = BookingRuleType.DOCUMENT_ITEM,
                debitAccountNumber = 1200L,
                debitAccountIsParty = true,
                debitPostingKey = "01",
                creditAccountNumber = 4000L,
                creditAccountIsCreditor = false,
                creditPostingKey = "50",
                debitCreditIndicator = DebitCreditIndicator.DEBIT,
            ),
        )

        bookingRuleService.createBookingRule(
            BookkeepingCreator.createBookingRule(
                tenantId = tenantId,
                name = "VAT Rule",
                bookingRuleType = BookingRuleType.TAX_CODE,
                debitAccountNumber = 1760L,
                debitAccountIsParty = false,
                debitPostingKey = "16",
                creditAccountNumber = 3806L,
                creditAccountIsCreditor = false,
                creditPostingKey = "81",
                debitCreditIndicator = DebitCreditIndicator.DEBIT,
            ),
        )
    }

    private suspend fun createBusinessSegments() {
        val logo = getLogo()

        val originatorAddress = addressService.createAddress(
            TENANT_KEY_VELOCITY_WHEELS,
            SYSTEM_USER_KEY,
            AddressDomainDto(
                UUID.randomUUID().toString(),
                companyName = "Velocity Wheels Ltd.",
                street = "Speed Avenue",
                houseNumber = "42",
                city = "London",
                country = "UK",
                postalCode = "SW1A 1AA",
                mailAddress = "",
            ),
        )

        val tenantId = tenantLoadService.findTenantByKey(TENANT_KEY_VELOCITY_WHEELS).id
        businessSegmentService.createBusinessSegment(
            TenantCreator.createBusinessSegment(
                tenantId,
                PREMIUM_LEASING,
                "'VW-PREM-'&\$year&'-'&\$numberRange(10000,99999)",
                logo,
                originatorAddressId = originatorAddress.id,
                vatId = "GB987654321",
            ),
        )

        businessSegmentService.createBusinessSegment(
            TenantCreator.createBusinessSegment(
                tenantId,
                ECONOMY_LEASING,
                "'VW-ECO-'&\$year&'-'&\$numberRange(10000,99999)",
                logo,
                originatorAddressId = originatorAddress.id,
                vatId = "GB987654321",
            ),
        )
    }

    protected suspend fun createTenant() {
        val tenantVelocityWheels = tenantService.createTenant(TenantCreator.createActiveTenant(TENANT_KEY_VELOCITY_WHEELS))
        tenantService.createTenantConfig(
            TenantCreator.createTenantConfig(
                tenantId = tenantVelocityWheels.id,
                appClientId = "4vw12wheels34lease56789abc",
                orderWorkflow = BillingSolutionConstants.STANDARD_ORDER_WORKFLOW,
                subscriptionWorkflow = BillingSolutionConstants.VELOCITY_WHEELS_SUBSCRIPTION_WORKFLOW,
                ledgerCurrency = Currency.GBP,
                features = listOf(Feature.ENABLE_BALANCE_CASE),
                locale = Locale.UK,
                theme = "velocity-wheels",
                timeZone = "UTC",
                defaultLanguage = Language.EN,
                defaultTaxRate = BigDecimal("20.00"),
            ),
        )

        workflowDeploymentService.deployWorkflows()
    }

    private suspend fun createPaymentConfig() {
        val tenantId = tenantLoadService.fetchTenantId(TENANT_KEY_VELOCITY_WHEELS)

        createPaymentAccounts(tenantId)
        createPaymentCategorizationRule(tenantId)
    }

    private suspend fun createPaymentAccounts(
        tenantId: UUID,
    ) {
        paymentAccountService.createPaymentAccount(
            PaymentCreator.createPaymentAccount(
                tenantId = tenantId,
                accountId = "*****************",
                name = "Velocity Wheels Main Account",
                description = "Primary account for lease payments",
                bic = "VWHEELSB",
                accountHolder = "Velocity Wheels Ltd.",
                paymentAccountType = PaymentAccountType.BANK_ACCOUNT,
                defaultAccount = true,
            ),
        )
    }

    private suspend fun createPaymentCategorizationRule(
        tenantId: UUID,
    ) {
        val paymentCategorizationRule = PaymentCategorizationRule(
            tenantId = tenantId,
            name = "Lease Payment Rule",
            targetPaymentCategory = PaymentCategory.CUSTOMER_PAYMENT,
            senders = null,
            receivers = null,
            purposes = "lease,leasing,vehicle",
            debitCreditIndicator = null,
            createdBy = SYSTEM_USER_ID,
            lastModifiedBy = SYSTEM_USER_ID,
            key = UUID.randomUUID().toString(),
            lastModifiedAt = TimeProvider.nowDateTimeInUTC(),
            version = 0L,
        )

        paymentCategorizationRuleService.createPaymentCategorizationRule(paymentCategorizationRule)
    }

    private suspend fun createCucumberNotificationApiReceiver() {
        notificationApiReceiverService.createNotificationApiReceiver(
            TENANT_KEY_VELOCITY_WHEELS,
            SYSTEM_USER_KEY,
            NotificationReceiverCreator.createCucumberNotificationReceiver(),
        )
    }

    private suspend fun getLogo(): ByteArray {
        val logoResource = resourceLoader.getResource("classpath:/logos/logo-velocity-wheels.png")

        if (logoResource.exists()) {
            try {
                return logoResource.contentAsByteArray
            } catch (e: IOException) {
                throw IllegalStateException("error while reading logo", e)
            }
        } else {
            throw IllegalStateException("logo was not found")
        }
    }

    private suspend fun addCustomers() {
        // Premium customers
        customerService.createCustomer(
            TENANT_KEY_VELOCITY_WHEELS,
            SYSTEM_USER_KEY,
            CustomerCreator.createCompany(
                key = "Luxury_Motors_Ltd",
                companyName = "Luxury Motors Ltd",
                language = Language.EN,
                city = "London",
                country = "UK",
                vat = "GB123456789",
            ),
        )

        customerService.createCustomer(
            TENANT_KEY_VELOCITY_WHEELS,
            SYSTEM_USER_KEY,
            CustomerCreator.createPerson(
                key = "James_Bond",
                firstName = "James",
                lastName = "Bond",
                language = Language.EN,
                city = "London",
                country = "UK",
            ),
        )

        // Economy customers
        customerService.createCustomer(
            TENANT_KEY_VELOCITY_WHEELS,
            SYSTEM_USER_KEY,
            CustomerCreator.createCompany(
                key = "Budget_Fleet_Services",
                companyName = "Budget Fleet Services",
                language = Language.EN,
                city = "Manchester",
                country = "UK",
                vat = "GB987654321",
            ),
        )

        customerService.createCustomer(
            TENANT_KEY_VELOCITY_WHEELS,
            SYSTEM_USER_KEY,
            CustomerCreator.createPerson(
                key = "Sarah_Connor",
                firstName = "Sarah",
                lastName = "Connor",
                language = Language.EN,
                city = "Birmingham",
                country = "UK",
            ),
        )

        customerService.createCustomer(
            TENANT_KEY_VELOCITY_WHEELS,
            SYSTEM_USER_KEY,
            CustomerCreator.createPerson(
                key = "Michael_Knight",
                firstName = "Michael",
                lastName = "Knight",
                language = Language.EN,
                city = "Edinburgh",
                country = "UK",
            ),
        )
    }

    private suspend fun addSubscriptions() {
        val tenantId = tenantLoadService.fetchTenantId(TENANT_KEY_VELOCITY_WHEELS)
        val now = TimeProvider.nowDateInTimeZone(tenantLoadService.findTenantConfigByTenantId(tenantId).getTimeZoneAsObject())
        val currency = Currency.GBP

        // Premium leasing subscriptions
        val luxuryMotors = customerLoadService.findByTenantIdAndKey(tenantId, "Luxury_Motors_Ltd")
        addPremiumSubscriptions(
            luxuryMotors,
            now,
            currency,
        )

        val jamesBond = customerLoadService.findByTenantIdAndKey(tenantId, "James_Bond")
        addPremiumSubscriptions(
            jamesBond,
            now,
            currency,
        )

        // Economy leasing subscriptions
        val budgetFleet = customerLoadService.findByTenantIdAndKey(tenantId, "Budget_Fleet_Services")
        addEconomySubscriptions(
            budgetFleet,
            now,
            currency,
        )

        val sarahConnor = customerLoadService.findByTenantIdAndKey(tenantId, "Sarah_Connor")
        addEconomySubscriptions(
            sarahConnor,
            now,
            currency,
        )

        val michaelKnight = customerLoadService.findByTenantIdAndKey(tenantId, "Michael_Knight")
        addEconomySubscriptions(
            michaelKnight,
            now,
            currency,
        )
    }

    private suspend fun addPremiumSubscriptions(
        customer: Customer,
        startDate: LocalDate,
        currency: Currency,
    ) {
        // Create one comprehensive BMW X5 subscription with multiple items
        val totalAmount = BigDecimal("899.00") + BigDecimal("149.00") + BigDecimal("89.00") // Lease + Maintenance + Insurance
        val subscriptionKey = "${customer.key}_BMW_X5_PACKAGE"

        // Start the subscription workflow
        workflowApiService.startSubscriptionWorkflow(
            TENANT_KEY_VELOCITY_WHEELS,
            SYSTEM_USER_KEY,
            SubscriptionCreator.createMonthlyCarLeaseSubscription(
                key = subscriptionKey,
                customerKey = customer.key,
                businessSegmentKey = PREMIUM_LEASING,
                vehicleModel = "BMW X5 M50i Complete Package",
                monthlyAmount = totalAmount,
                currency = currency,
                startDate = startDate.minusDays(30),
                endDate = startDate.plusYears(3),
                properties = mutableMapOf(
                    PropertyKey.VIN to DomainUtil.randomVIN(),
                    PropertyKey.PAYMENT_TERMS to "36 months"
                ),
            ),
        )

        // Load the created subscription and add items
        val tenantId = tenantLoadService.fetchTenantId(TENANT_KEY_VELOCITY_WHEELS)
        val subscription = subscriptionLoadService.findByTenantIdAndKey(tenantId, subscriptionKey)
        addPremiumSubscriptionItems(subscription, currency)
    }

    private suspend fun addEconomySubscriptions(
        customer: Customer,
        startDate: LocalDate,
        currency: Currency,
    ) {
        // Create one comprehensive Ford Focus subscription with multiple items
        val totalAmount = BigDecimal("299.00") + BigDecimal("79.00") + BigDecimal("49.00") // Lease + Maintenance + Insurance
        val subscriptionKey = "${customer.key}_FORD_FOCUS_PACKAGE"

        // Start the subscription workflow
        workflowApiService.startSubscriptionWorkflow(
            TENANT_KEY_VELOCITY_WHEELS,
            SYSTEM_USER_KEY,
            SubscriptionCreator.createMonthlyCarLeaseSubscription(
                key = subscriptionKey,
                customerKey = customer.key,
                businessSegmentKey = ECONOMY_LEASING,
                vehicleModel = "Ford Focus Titanium Complete Package",
                monthlyAmount = totalAmount,
                currency = currency,
                startDate = startDate.minusDays(15),
                endDate = startDate.plusYears(2),
                properties = mutableMapOf(
                    PropertyKey.VIN to DomainUtil.randomVIN(),
                    PropertyKey.PAYMENT_TERMS to "24 months"
                ),
            ),
        )

        // Load the created subscription and add items
        val tenantId = tenantLoadService.fetchTenantId(TENANT_KEY_VELOCITY_WHEELS)
        val subscription = subscriptionLoadService.findByTenantIdAndKey(tenantId, subscriptionKey)
        addEconomySubscriptionItems(subscription, currency)

        // Add a special weekly lease for Michael Knight (KITT)
        if (customer.key == "Michael_Knight") {
            val kittSubscriptionKey = "${customer.key}_KITT_WEEKLY_LEASE"

            workflowApiService.startSubscriptionWorkflow(
                TENANT_KEY_VELOCITY_WHEELS,
                SYSTEM_USER_KEY,
                SubscriptionCreator.createWeeklyCarLeaseSubscription(
                    key = kittSubscriptionKey,
                    customerKey = customer.key,
                    businessSegmentKey = ECONOMY_LEASING,
                    vehicleModel = "Pontiac Trans Am (KITT) Weekly Lease",
                    weeklyAmount = BigDecimal("199.00"),
                    currency = currency,
                    startDate = startDate.minusDays(7),
                    endDate = startDate.plusMonths(6),
                    properties = mutableMapOf(
                        PropertyKey.VIN to "KITT2000KNIGHTIND",
                        PropertyKey.PAYMENT_TERMS to "26 weeks"
                    ),
                ),
            )

            // Load the created KITT subscription and add items
            val kittSubscription = subscriptionLoadService.findByTenantIdAndKey(tenantId, kittSubscriptionKey)
            addKittSubscriptionItems(kittSubscription, currency)
        }
    }

    private suspend fun addPremiumSubscriptionItems(
        subscription: Subscription,
        currency: Currency,
    ) {
        val userId = userLoadService.findByKey(SYSTEM_USER_KEY).id

        // BMW X5 Lease Item
        subscriptionItemService.addSubscriptionItem(
            userId = userId,
            subscription = subscription,
            subscriptionItemRequestDomainDto = createLeaseSubscriptionItem(
                key = "${subscription.key}_LEASE_ITEM",
                vehicleModel = "BMW X5 M50i",
                description = "Monthly lease for BMW X5 M50i",
                amount = BigDecimal("899.00"),
                currency = currency,
            ),
            withoutUpdateAmounts = true,
        )

        // BMW X5 Maintenance Item
        subscriptionItemService.addSubscriptionItem(
            userId = userId,
            subscription = subscription,
            subscriptionItemRequestDomainDto = createMaintenanceSubscriptionItem(
                key = "${subscription.key}_MAINTENANCE_ITEM",
                vehicleModel = "BMW X5 M50i",
                description = "Monthly maintenance package for BMW X5 M50i including regular service, oil changes, and premium repairs",
                amount = BigDecimal("149.00"),
                currency = currency,
            ),
            withoutUpdateAmounts = true,
        )

        // BMW X5 Insurance Item
        subscriptionItemService.addSubscriptionItem(
            userId = userId,
            subscription = subscription,
            subscriptionItemRequestDomainDto = createInsuranceSubscriptionItem(
                key = "${subscription.key}_INSURANCE_ITEM",
                vehicleModel = "BMW X5 M50i",
                description = "Monthly comprehensive insurance coverage for BMW X5 M50i",
                amount = BigDecimal("89.00"),
                currency = currency,
            ),
            // Update amounts on the last item
            withoutUpdateAmounts = false,
        )
    }

    private suspend fun addEconomySubscriptionItems(
        subscription: Subscription,
        currency: Currency,
    ) {
        val userId = userLoadService.findByKey(SYSTEM_USER_KEY).id

        // Ford Focus Lease Item
        subscriptionItemService.addSubscriptionItem(
            userId = userId,
            subscription = subscription,
            subscriptionItemRequestDomainDto = createLeaseSubscriptionItem(
                key = "${subscription.key}_LEASE_ITEM",
                vehicleModel = "Ford Focus Titanium",
                description = "Monthly lease for Ford Focus Titanium",
                amount = BigDecimal("299.00"),
                currency = currency,
            ),
            withoutUpdateAmounts = true,
        )

        // Ford Focus Maintenance Item
        subscriptionItemService.addSubscriptionItem(
            userId = userId,
            subscription = subscription,
            subscriptionItemRequestDomainDto = createMaintenanceSubscriptionItem(
                key = "${subscription.key}_MAINTENANCE_ITEM",
                vehicleModel = "Ford Focus Titanium",
                description = "Monthly basic maintenance package for Ford Focus Titanium including regular service and oil changes",
                amount = BigDecimal("79.00"),
                currency = currency,
            ),
            withoutUpdateAmounts = true,
        )

        // Ford Focus Insurance Item
        subscriptionItemService.addSubscriptionItem(
            userId = userId,
            subscription = subscription,
            subscriptionItemRequestDomainDto = createInsuranceSubscriptionItem(
                key = "${subscription.key}_INSURANCE_ITEM",
                vehicleModel = "Ford Focus Titanium",
                description = "Monthly basic insurance coverage for Ford Focus Titanium",
                amount = BigDecimal("49.00"),
                currency = currency,
            ),
            // Update amounts on the last item
            withoutUpdateAmounts = false,
        )
    }

    private suspend fun addKittSubscriptionItems(
        subscription: Subscription,
        currency: Currency,
    ) {
        val userId = userLoadService.findByKey(SYSTEM_USER_KEY).id

        // KITT Lease Item
        subscriptionItemService.addSubscriptionItem(
            userId = userId,
            subscription = subscription,
            subscriptionItemRequestDomainDto = createLeaseSubscriptionItem(
                key = "${subscription.key}_LEASE_ITEM",
                vehicleModel = "Pontiac Trans Am (KITT)",
                description = "Weekly lease for the legendary KITT - Knight Industries Two Thousand",
                amount = BigDecimal("199.00"),
                currency = currency,
            ),
            // Update amounts since this is the only item
            withoutUpdateAmounts = false,
        )
    }

    private fun createLeaseSubscriptionItem(
        key: String,
        vehicleModel: String,
        description: String,
        amount: BigDecimal,
        currency: Currency,
    ): SubscriptionItemRequestDomainDto = SubscriptionItemRequestDomainDto(
        key = key,
        articleNumber = "LEASE-${vehicleModel.replace(" ", "-").uppercase()}",
        debitCreditIndicator = DebitCreditIndicator.DEBIT,
        category = ItemCategory.SERVICE,
        itemGroup = "Vehicle Lease",
        name = "$vehicleModel Lease",
        description = description,
        supplierKey = null,
        quantity = BigDecimal.ONE,
        currency = currency,
        unitNetAmount = amount,
        taxes = listOf(
            TaxInformationDomainDto(
                taxType = TaxType.VAT,
                taxRate = BigDecimal("20.00"),
                currency = currency,
            ),
        ),
    )

    private fun createMaintenanceSubscriptionItem(
        key: String,
        vehicleModel: String,
        description: String,
        amount: BigDecimal,
        currency: Currency,
    ): SubscriptionItemRequestDomainDto = SubscriptionItemRequestDomainDto(
        key = key,
        articleNumber = "MAINT-${vehicleModel.replace(" ", "-").uppercase()}",
        debitCreditIndicator = DebitCreditIndicator.DEBIT,
        category = ItemCategory.SERVICE,
        itemGroup = "Vehicle Maintenance",
        name = "$vehicleModel Maintenance",
        description = description,
        supplierKey = null,
        quantity = BigDecimal.ONE,
        currency = currency,
        unitNetAmount = amount,
        taxes = listOf(
            TaxInformationDomainDto(
                taxType = TaxType.VAT,
                taxRate = BigDecimal("20.00"),
                currency = currency,
            ),
        ),
    )

    private fun createInsuranceSubscriptionItem(
        key: String,
        vehicleModel: String,
        description: String,
        amount: BigDecimal,
        currency: Currency,
    ): SubscriptionItemRequestDomainDto = SubscriptionItemRequestDomainDto(
        key = key,
        articleNumber = "INS-${vehicleModel.replace(" ", "-").uppercase()}",
        debitCreditIndicator = DebitCreditIndicator.DEBIT,
        category = ItemCategory.SERVICE,
        itemGroup = "Vehicle Insurance",
        name = "$vehicleModel Insurance",
        description = description,
        supplierKey = null,
        quantity = BigDecimal.ONE,
        currency = currency,
        unitNetAmount = amount,
        taxes = listOf(
            TaxInformationDomainDto(
                taxType = TaxType.VAT,
                taxRate = BigDecimal("20.00"),
                currency = currency,
            ),
        ),
    )
}
