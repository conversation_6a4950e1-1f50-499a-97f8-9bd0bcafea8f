import org.openapitools.generator.gradle.plugin.tasks.GenerateTask

apply(plugin = "org.springframework.boot")
apply(plugin = "io.spring.dependency-management")

dependencies {
    implementation(libs.spring.boot.starter.webflux)
    implementation(libs.spring.boot.starter.validation)
    implementation(libs.jackson.module.kotlin)
    implementation(libs.bundles.impl.coroutines)
    implementation(libs.bundles.impl.network)
}

val openApiGenerateOutgoingNotification by tasks.registering(GenerateTask::class) {
    generatorName.set("kotlin-spring")
    inputSpec.set("$projectDir/../api/outgoing-notification-api.yaml")
    outputDir.set("$projectDir")
    apiPackage.set("com.klosesoft.billingsolution.generated.api.outgoing.notification.server")
    modelPackage.set("com.klosesoft.billingsolution.generated.api.outgoing.notification.model")
    modelNameSuffix.set("Dto")
    ignoreFileOverride.set("$rootDir/.openapi-generator-ignore")
    validateSpec.set(true)
    configOptions.set(
        mapOf(
            "enumPropertyNaming" to "original",
            "interfaceOnly" to "true",
            "exceptionHandler" to "false",
            "useTags" to "true",
            "useBeanValidation" to "true",
            "useSpringBoot3" to "true",
            "documentationProvider" to "none",
            "skipDefaultInterface" to "true",
            "useSwaggerUI" to "false",
            "reactive" to "true",
            "gradleBuildFile" to "false",
        ),
    )
}

tasks.jar {
    isPreserveFileTimestamps = false
    isReproducibleFileOrder = true
}
