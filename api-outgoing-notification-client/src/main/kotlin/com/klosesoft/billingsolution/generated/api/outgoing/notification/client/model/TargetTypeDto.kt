/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package com.klosesoft.billingsolution.generated.api.outgoing.notification.client.model


import com.fasterxml.jackson.annotation.JsonProperty

/**
 * The type of the target
 *
 * Values: ORDER,CUSTOMER,SUPPLIER,SUBSCRIPTION,POSTING_RECORD,DOCUMENT,PAYMENT_TRANSACTION,PAYMENT_ASSIGNMENT
 */

enum class TargetTypeDto(val value: kotlin.String) {

    @JsonProperty(value = "ORDER")
    ORDER("ORDER"),

    @JsonProperty(value = "CUSTOMER")
    CUSTOMER("CUSTOMER"),

    @<PERSON>sonProperty(value = "SUPPLIER")
    SUPPLIER("SUPPLIER"),

    @JsonProperty(value = "SUBSCRIPTION")
    SUBSCRIPTION("SUBSCRIPTION"),

    @JsonProperty(value = "POSTING_RECORD")
    POSTING_RECORD("POSTING_RECORD"),

    @JsonProperty(value = "DOCUMENT")
    DOCUMENT("DOCUMENT"),

    @JsonProperty(value = "PAYMENT_TRANSACTION")
    PAYMENT_TRANSACTION("PAYMENT_TRANSACTION"),

    @JsonProperty(value = "PAYMENT_ASSIGNMENT")
    PAYMENT_ASSIGNMENT("PAYMENT_ASSIGNMENT");

    /**
     * Override [toString()] to avoid using the enum variable name as the value, and instead use
     * the actual value defined in the API spec file.
     *
     * This solves a problem when the variable name and its value are different, and ensures that
     * the client sends the correct enum values to the server always.
     */
    override fun toString(): kotlin.String = value

    companion object {
        /**
         * Converts the provided [data] to a [String] on success, null otherwise.
         */
        fun encode(data: kotlin.Any?): kotlin.String? = if (data is TargetTypeDto) "$data" else null

        /**
         * Returns a valid [TargetTypeDto] for [data], null otherwise.
         */
        fun decode(data: kotlin.Any?): TargetTypeDto? = data?.let {
          val normalizedData = "$it".lowercase()
          values().firstOrNull { value ->
            it == value || normalizedData == "$value".lowercase()
          }
        }
    }
}

