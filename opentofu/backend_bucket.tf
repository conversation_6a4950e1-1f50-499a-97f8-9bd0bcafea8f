resource "aws_s3_bucket" "document_bucket" {
  bucket = "klosesoft-backend-storage2"
  tags = {
    Name        = "Bucket for storing"
    Environment = "Dev"
  }
}

resource "aws_s3_bucket_ownership_controls" "document_bucket" {
  bucket = aws_s3_bucket.cloud_trail_bucket.id
  rule {
    object_ownership = "BucketOwnerPreferred"
  }
}

resource "aws_s3_bucket_acl" "document_bucket_acl" {
  depends_on = [aws_s3_bucket_ownership_controls.document_bucket]

  bucket = aws_s3_bucket.cloud_trail_bucket.id
  acl    = "private"
}

data "aws_iam_policy_document" "document_bucket_policy" {
  statement {
    sid    = "BackendWritePolicy"
    effect = "Allow"

    principals {
      type = "AWS"
      identifiers = [aws_iam_user.backend_s3_user.arn]
    }

    actions = ["s3:PutObject", "s3:GetObject", "s3:DeleteObject"]
    resources = ["arn:aws:s3:::klosesoft-backend-storage2/*"]
  }
}

resource "aws_s3_bucket_policy" "document_bucket_policy" {
  bucket = "klosesoft-backend-storage2"
  policy = data.aws_iam_policy_document.document_bucket_policy.json
}
