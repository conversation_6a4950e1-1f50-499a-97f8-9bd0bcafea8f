resource "aws_s3_bucket" "klosesoft_demoshop_bucket" {
  bucket = "klosesoft-demoshop-bucket2"
}

resource "aws_s3_bucket_ownership_controls" "klosesoft_demoshop_bucket" {
  bucket = aws_s3_bucket.klosesoft_demoshop_bucket.id

  rule {
    object_ownership = "BucketOwnerPreferred"
  }
}

resource "aws_s3_bucket_public_access_block" "klosesoft_demoshop_bucket" {
  bucket = aws_s3_bucket.klosesoft_demoshop_bucket.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

resource "aws_s3_bucket_acl" "klosesoft_demoshop_bucket" {
  depends_on = [aws_s3_bucket_ownership_controls.klosesoft_demoshop_bucket, aws_s3_bucket_public_access_block.klosesoft_demoshop_bucket]

  bucket = aws_s3_bucket.klosesoft_demoshop_bucket.id
  acl    = "private"
}

data "aws_iam_policy_document" "demoshop_s3_policy" {
  statement {
    actions   = ["s3:GetObject"]
    resources = ["${aws_s3_bucket.klosesoft_demoshop_bucket.arn}/*"]

    principals {
      type        = "AWS"
      identifiers = [aws_cloudfront_origin_access_identity.demoshop_origin_access_identity.iam_arn]
    }
  }
}

resource "aws_s3_bucket_policy" "klosesoft_demoshop_bucket" {
  bucket = aws_s3_bucket.klosesoft_demoshop_bucket.id
  policy = data.aws_iam_policy_document.demoshop_s3_policy.json
}

resource "aws_s3_bucket_website_configuration" "klosesoft_demoshop_bucket" {
  bucket = aws_s3_bucket.klosesoft_demoshop_bucket.id

  index_document {
    suffix = "index.html"
  }

  error_document {
    key = "index.html"
  }
}
