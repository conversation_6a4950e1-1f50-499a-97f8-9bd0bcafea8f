resource "aws_s3_bucket" "cloud_trail_bucket" {
  bucket = "klosesoft-cloud-trail-bucket2"
}

resource "aws_cloudtrail" "cloud_trail" {
  name                          = "management-events"
  s3_bucket_name                = aws_s3_bucket.cloud_trail_bucket.id
  include_global_service_events = true
  is_multi_region_trail         = true
  enable_log_file_validation    = true
}

resource "aws_s3_bucket_ownership_controls" "cloud_trail_bucket" {
  bucket = aws_s3_bucket.cloud_trail_bucket.id
  rule {
    object_ownership = "BucketOwnerPreferred"
  }
}

resource "aws_s3_bucket_acl" "cloud_trail_bucket_acl" {
  depends_on = [aws_s3_bucket_ownership_controls.cloud_trail_bucket]

  bucket = aws_s3_bucket.cloud_trail_bucket.id
  acl    = "private"
}

data "aws_iam_policy_document" "bucket_policy" {
  statement {
    sid    = "AWSCloudTrailAclCheck"
    effect = "Allow"

    principals {
      type = "Service"
      identifiers = ["cloudtrail.amazonaws.com"]
    }

    actions = ["s3:GetBucketAcl", "s3:GetBucketLocation"]
    resources = ["arn:aws:s3:::klosesoft-cloud-trail-bucket2"]
  }

  statement {
    sid    = "AWSCloudTrailWrite"
    effect = "Allow"

    principals {
      type = "Service"
      identifiers = ["cloudtrail.amazonaws.com"]
    }

    actions = ["s3:PutObject"]
    resources = ["arn:aws:s3:::klosesoft-cloud-trail-bucket2/AWSLogs/234542553877/*"]

    condition {
      test     = "StringEquals"
      variable = "s3:x-amz-acl"
      values = ["bucket-owner-full-control"]
    }
  }
}

resource "aws_s3_bucket_policy" "bucket_policy" {
  bucket = aws_s3_bucket.cloud_trail_bucket.id
  policy = data.aws_iam_policy_document.bucket_policy.json
}
