1. Create terraform_deployment user in AWS Management Console
2. Create and assign following policy to the user:
   ```
{
"Version": "2012-10-17",
"Statement": [
{
"Sid": "TerraformDeployment",
"Effect": "Allow",
"Action": [
"s3:*",
"apigateway:*",
"acm:*",
"rds:*",
"logs:*",
"iam:CreateRole",
"iam:GetRole",
"iam:PassRole",
"iam:CreatePolicy",
"iam:CreatePolicyVersion",
"iam:ListRolePolicies",
"iam:ListAttachedRolePolicies",
"iam:ListInstanceProfilesForRole",
"iam:DeleteRole",
"iam:GetPolicy",
"iam:AttachRolePolicy",
"iam:DetachRolePolicy",
"iam:GetPolicyVersion",
"iam:ListPolicyVersions",
"iam:DeletePolicy",
"ecr:*",
"ecs:*",
"ec2:*",
"elasticloadbalancing:*",
"cognito-idp:*",
"cloudfront:*",
"iam:*",
"cloudtrail:*",
"ses:*"
],
"Resource": "*"
},
{
"Sid": "TerraformDeploymentSLRForRDS",
"Effect": "Allow",
"Action": [
"iam:CreateServiceLinkedRole"
],
"Resource": "arn:aws:iam::*:role/aws-service-role/rds.amazonaws.com/AWSServiceRoleForRDS",
"Condition": {
"StringLike": {
"iam:AWSServiceName": "rds.amazonaws.com"
}
}
},
{
"Sid": "TerraformDeploymentSLRForELB",
"Effect": "Allow",
"Action": [
"iam:CreateServiceLinkedRole"
],
"Resource": "arn:aws:iam::*:role/aws-service-role/elasticloadbalancing.amazonaws.com/AWSServiceRoleForElasticLoadBalancing",
"Condition": {
"StringLike": {
"iam:AWSServiceName": "elasticloadbalancing.amazonaws.com"
}
}
},
{
"Sid": "TerraformDeploymentSLRForECS",
"Effect": "Allow",
"Action": [
"iam:CreateServiceLinkedRole"
],
"Resource": "arn:aws:iam::*:role/aws-service-role/ecs.amazonaws.com/AWSServiceRoleForECS",
"Condition": {
"StringLike": {
"iam:AWSServiceName": "ecs.amazonaws.com"
}
}
}
]
}
   ```
3. Create and download access key and secret key for the user
4. Install OpenTofu on your laptop
5. Run the following command in this directory to initialize the terraform:

   ```tofu init```
6. Run the following commands to create the plan:
   ```
   set AWS_ACCESS_KEY_ID "TODO: replace with your access key"
   set AWS_SECRET_ACCESS_KEY "TODO: replace with your secret key"
   tofu plan
   ```
7. Run the following command to apply the changes:

   ```tofu apply```
