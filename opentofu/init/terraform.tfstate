{"version": 4, "terraform_version": "1.9.0", "serial": 2, "lineage": "acf5b70d-4b1f-1b1d-c21e-aec03691a778", "outputs": {}, "resources": [{"mode": "managed", "type": "aws_s3_bucket", "name": "klosesoft_tofu_state_bucket2", "provider": "provider[\"registry.opentofu.org/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"acceleration_status": "", "acl": null, "arn": "arn:aws:s3:::klosesoft-tofu-state-bucket2", "bucket": "klosesoft-tofu-state-bucket2", "bucket_domain_name": "klosesoft-tofu-state-bucket2.s3.amazonaws.com", "bucket_prefix": "", "bucket_region": "eu-central-1", "bucket_regional_domain_name": "klosesoft-tofu-state-bucket2.s3.eu-central-1.amazonaws.com", "cors_rule": [], "force_destroy": false, "grant": [{"id": "b2639641bb534b1ff30af647bc9848637e88b45bff09b586913a7b04bd19eedc", "permissions": ["FULL_CONTROL"], "type": "CanonicalUser", "uri": ""}], "hosted_zone_id": "Z21DNDUVLTQW6Q", "id": "klosesoft-tofu-state-bucket2", "lifecycle_rule": [], "logging": [], "object_lock_configuration": [], "object_lock_enabled": false, "policy": "", "region": "eu-central-1", "replication_configuration": [], "request_payer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "server_side_encryption_configuration": [{"rule": [{"apply_server_side_encryption_by_default": [{"kms_master_key_id": "", "sse_algorithm": "AES256"}], "bucket_key_enabled": false}]}], "tags": {"Environment": "<PERSON>", "Name": "Bucket for OpenTofu State"}, "tags_all": {"Environment": "<PERSON>", "Name": "Bucket for OpenTofu State"}, "timeouts": null, "versioning": [{"enabled": false, "mfa_delete": false}], "website": [], "website_domain": null, "website_endpoint": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjM2MDAwMDAwMDAwMDAsInJlYWQiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19"}]}], "check_results": null}