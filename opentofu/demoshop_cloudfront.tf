resource "aws_acm_certificate" "demoshop_cert" {
  domain_name       = "clientshop.klosesoft.com"
  validation_method = "EMAIL"
  provider          = aws.useast1

  validation_option {
    domain_name       = "clientshop.klosesoft.com"
    validation_domain = "klosesoft.com"
  }

  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_cloudfront_distribution" "demoshop_distribution" {
  origin {
    domain_name = aws_s3_bucket.klosesoft_demoshop_bucket.bucket_regional_domain_name
    origin_id   = aws_s3_bucket.klosesoft_demoshop_bucket.id

    s3_origin_config {
      origin_access_identity = aws_cloudfront_origin_access_identity.demoshop_origin_access_identity.cloudfront_access_identity_path
    }
  }

  price_class = "PriceClass_100"

  enabled             = true
  is_ipv6_enabled     = true
  comment             = "demoshop distribution"
  default_root_object = "index.html"

  aliases = ["clientshop.klosesoft.com"]

  custom_error_response {
    error_caching_min_ttl = 300
    error_code            = 403
    response_code         = 200
    response_page_path    = "/index.html"
  }

  default_cache_behavior {
    allowed_methods  = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
    cached_methods   = ["GET", "HEAD"]
    target_origin_id = aws_s3_bucket.klosesoft_demoshop_bucket.id

    forwarded_values {
      query_string = false

      cookies {
        forward = "none"
      }
    }

    viewer_protocol_policy = "redirect-to-https"
    min_ttl                = 0
    default_ttl            = 60
    max_ttl                = 86400
  }

  viewer_certificate {
    acm_certificate_arn      = aws_acm_certificate.demoshop_cert.arn
    ssl_support_method       = "sni-only"
    minimum_protocol_version = "TLSv1.2_2021"
  }

  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }
}

resource "aws_cloudfront_origin_access_identity" "demoshop_origin_access_identity" {
  comment = "demoshop S3 origin identity"
}
