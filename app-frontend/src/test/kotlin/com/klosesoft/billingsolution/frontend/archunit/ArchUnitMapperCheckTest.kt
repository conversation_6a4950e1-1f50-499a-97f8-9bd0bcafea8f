package com.klosesoft.billingsolution.frontend.archunit

import com.tngtech.archunit.core.importer.ClassFileImporter
import com.tngtech.archunit.lang.syntax.ArchRuleDefinition
import io.kotest.core.spec.style.StringSpec

class ArchUnitMapperCheckTest :
    StringSpec({
        "webPortalMapper classes should not import external packages" {
            val importedClasses = ClassFileImporter().importPackages("com.klosesoft.billingsolution.frontend.webportal.mapper")

            val rule = ArchRuleDefinition.noClasses()
                .should().dependOnClassesThat().resideInAnyPackage("..external..")

            rule.check(importedClasses)
        }

        "externalMapper classes should not import WebPortal packages" {
            val importedClasses = ClassFileImporter().importPackages("com.klosesoft.billingsolution.frontend.external.mapper")

            val rule = ArchRuleDefinition.noClasses()
                .should().dependOnClassesThat().resideInAnyPackage("..webportal..")

            rule.check(importedClasses)
        }
    })
