package com.klosesoft.billingsolution.frontend.webportal

import com.klosesoft.billingsolution.domain.logic.api.service.WorkflowApiService
import com.klosesoft.billingsolution.domain.model.valueobject.Right
import com.klosesoft.billingsolution.domain.model.valueobject.WorkflowType
import com.klosesoft.billingsolution.frontend.webportal.auth.WebPortalTenantAccessAllowedChecker
import com.klosesoft.billingsolution.generated.api.webportal.server.WebPortalWorkflowApi
import com.klosesoft.billingsolution.generated.api.webportal.server.model.WorkflowTypeDto
import org.springframework.http.ResponseEntity
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.RestController

@RestController
@WebPortalApiAuth
class WebPortalWorkflowController(
    private val workflowApiService: WorkflowApiService,
    private val webPortalTenantAccessAllowedChecker: WebPortalTenantAccessAllowedChecker,
) : WebPortalWorkflowApi {

    @Transactional(readOnly = true)
    override suspend fun loadWorkflowDefinition(
        xTenantKey: String,
        workflowType: WorkflowTypeDto,
    ): ResponseEntity<String> {
        webPortalTenantAccessAllowedChecker.throwExceptionIfNotAuthorizedForCurrentUser(
            xTenantKey,
            Right.WORKFLOW_READ,
        )

        val domainWorkflowType = when (workflowType) {
            WorkflowTypeDto.ORDER -> WorkflowType.ORDER
            WorkflowTypeDto.SUBSCRIPTION -> WorkflowType.SUBSCRIPTION
        }

        return ResponseEntity.ok(workflowApiService.loadWorkflowDefinition(xTenantKey, domainWorkflowType))
    }

    @Transactional
    override suspend fun updateWorkflowDefinition(
        xTenantKey: String,
        workflowType: WorkflowTypeDto,
        body: String,
    ): ResponseEntity<String> {
        webPortalTenantAccessAllowedChecker.throwExceptionIfNotAuthorizedForCurrentUser(
            xTenantKey,
            Right.WORKFLOW_WRITE,
        )

        val domainWorkflowType = when (workflowType) {
            WorkflowTypeDto.ORDER -> WorkflowType.ORDER
            WorkflowTypeDto.SUBSCRIPTION -> WorkflowType.SUBSCRIPTION
        }

        val updatedXml = workflowApiService.updateWorkflowDefinition(xTenantKey, domainWorkflowType, body)

        return ResponseEntity.ok(updatedXml)
    }
}
