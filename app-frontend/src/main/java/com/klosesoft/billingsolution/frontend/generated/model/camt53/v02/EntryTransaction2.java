
package com.klosesoft.billingsolution.frontend.generated.model.camt53.v02;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;

import java.util.ArrayList;
import java.util.List;


/**
 * <p>Java class for EntryTransaction2 complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>{@code
 * <complexType name="EntryTransaction2">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="Refs" type="{urn:iso:std:iso:20022:tech:xsd:camt.053.001.02}TransactionReferences2" minOccurs="0"/>
 *         <element name="AmtDtls" type="{urn:iso:std:iso:20022:tech:xsd:camt.053.001.02}AmountAndCurrencyExchange3" minOccurs="0"/>
 *         <element name="Avlbty" type="{urn:iso:std:iso:20022:tech:xsd:camt.053.001.02}CashBalanceAvailability2" maxOccurs="unbounded" minOccurs="0"/>
 *         <element name="BkTxCd" type="{urn:iso:std:iso:20022:tech:xsd:camt.053.001.02}BankTransactionCodeStructure4" minOccurs="0"/>
 *         <element name="Chrgs" type="{urn:iso:std:iso:20022:tech:xsd:camt.053.001.02}ChargesInformation6" maxOccurs="unbounded" minOccurs="0"/>
 *         <element name="Intrst" type="{urn:iso:std:iso:20022:tech:xsd:camt.053.001.02}TransactionInterest2" maxOccurs="unbounded" minOccurs="0"/>
 *         <element name="RltdPties" type="{urn:iso:std:iso:20022:tech:xsd:camt.053.001.02}TransactionParty2" minOccurs="0"/>
 *         <element name="RltdAgts" type="{urn:iso:std:iso:20022:tech:xsd:camt.053.001.02}TransactionAgents2" minOccurs="0"/>
 *         <element name="Purp" type="{urn:iso:std:iso:20022:tech:xsd:camt.053.001.02}Purpose2Choice" minOccurs="0"/>
 *         <element name="RltdRmtInf" type="{urn:iso:std:iso:20022:tech:xsd:camt.053.001.02}RemittanceLocation2" maxOccurs="10" minOccurs="0"/>
 *         <element name="RmtInf" type="{urn:iso:std:iso:20022:tech:xsd:camt.053.001.02}RemittanceInformation5" minOccurs="0"/>
 *         <element name="RltdDts" type="{urn:iso:std:iso:20022:tech:xsd:camt.053.001.02}TransactionDates2" minOccurs="0"/>
 *         <element name="RltdPric" type="{urn:iso:std:iso:20022:tech:xsd:camt.053.001.02}TransactionPrice2Choice" minOccurs="0"/>
 *         <element name="RltdQties" type="{urn:iso:std:iso:20022:tech:xsd:camt.053.001.02}TransactionQuantities1Choice" maxOccurs="unbounded" minOccurs="0"/>
 *         <element name="FinInstrmId" type="{urn:iso:std:iso:20022:tech:xsd:camt.053.001.02}SecurityIdentification4Choice" minOccurs="0"/>
 *         <element name="Tax" type="{urn:iso:std:iso:20022:tech:xsd:camt.053.001.02}TaxInformation3" minOccurs="0"/>
 *         <element name="RtrInf" type="{urn:iso:std:iso:20022:tech:xsd:camt.053.001.02}ReturnReasonInformation10" minOccurs="0"/>
 *         <element name="CorpActn" type="{urn:iso:std:iso:20022:tech:xsd:camt.053.001.02}CorporateAction1" minOccurs="0"/>
 *         <element name="SfkpgAcct" type="{urn:iso:std:iso:20022:tech:xsd:camt.053.001.02}CashAccount16" minOccurs="0"/>
 *         <element name="AddtlTxInf" type="{urn:iso:std:iso:20022:tech:xsd:camt.053.001.02}Max500Text" minOccurs="0"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "EntryTransaction2", propOrder = {
    "refs",
    "amtDtls",
    "avlbty",
    "bkTxCd",
    "chrgs",
    "intrst",
    "rltdPties",
    "rltdAgts",
    "purp",
    "rltdRmtInf",
    "rmtInf",
    "rltdDts",
    "rltdPric",
    "rltdQties",
    "finInstrmId",
    "tax",
    "rtrInf",
    "corpActn",
    "sfkpgAcct",
    "addtlTxInf"
})
public class EntryTransaction2 {

    @XmlElement(name = "Refs")
    protected TransactionReferences2 refs;
    @XmlElement(name = "AmtDtls")
    protected AmountAndCurrencyExchange3 amtDtls;
    @XmlElement(name = "Avlbty")
    protected List<CashBalanceAvailability2> avlbty;
    @XmlElement(name = "BkTxCd")
    protected BankTransactionCodeStructure4 bkTxCd;
    @XmlElement(name = "Chrgs")
    protected List<ChargesInformation6> chrgs;
    @XmlElement(name = "Intrst")
    protected List<TransactionInterest2> intrst;
    @XmlElement(name = "RltdPties")
    protected TransactionParty2 rltdPties;
    @XmlElement(name = "RltdAgts")
    protected TransactionAgents2 rltdAgts;
    @XmlElement(name = "Purp")
    protected Purpose2Choice purp;
    @XmlElement(name = "RltdRmtInf")
    protected List<RemittanceLocation2> rltdRmtInf;
    @XmlElement(name = "RmtInf")
    protected RemittanceInformation5 rmtInf;
    @XmlElement(name = "RltdDts")
    protected TransactionDates2 rltdDts;
    @XmlElement(name = "RltdPric")
    protected TransactionPrice2Choice rltdPric;
    @XmlElement(name = "RltdQties")
    protected List<TransactionQuantities1Choice> rltdQties;
    @XmlElement(name = "FinInstrmId")
    protected SecurityIdentification4Choice finInstrmId;
    @XmlElement(name = "Tax")
    protected TaxInformation3 tax;
    @XmlElement(name = "RtrInf")
    protected ReturnReasonInformation10 rtrInf;
    @XmlElement(name = "CorpActn")
    protected CorporateAction1 corpActn;
    @XmlElement(name = "SfkpgAcct")
    protected CashAccount16 sfkpgAcct;
    @XmlElement(name = "AddtlTxInf")
    protected String addtlTxInf;

    /**
     * Gets the value of the refs property.
     *
     * @return
     *     possible object is
     *     {@link TransactionReferences2 }
     *
     */
    public TransactionReferences2 getRefs() {
        return refs;
    }

    /**
     * Sets the value of the refs property.
     *
     * @param value
     *     allowed object is
     *     {@link TransactionReferences2 }
     *
     */
    public void setRefs(TransactionReferences2 value) {
        this.refs = value;
    }

    /**
     * Gets the value of the amtDtls property.
     *
     * @return
     *     possible object is
     *     {@link AmountAndCurrencyExchange3 }
     *
     */
    public AmountAndCurrencyExchange3 getAmtDtls() {
        return amtDtls;
    }

    /**
     * Sets the value of the amtDtls property.
     *
     * @param value
     *     allowed object is
     *     {@link AmountAndCurrencyExchange3 }
     *
     */
    public void setAmtDtls(AmountAndCurrencyExchange3 value) {
        this.amtDtls = value;
    }

    /**
     * Gets the value of the avlbty property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the Jakarta XML Binding object.
     * This is why there is not a {@code set} method for the avlbty property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getAvlbty().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link CashBalanceAvailability2 }
     *
     *
     * @return
     *     The value of the avlbty property.
     */
    public List<CashBalanceAvailability2> getAvlbty() {
        if (avlbty == null) {
            avlbty = new ArrayList<>();
        }
        return this.avlbty;
    }

    /**
     * Gets the value of the bkTxCd property.
     *
     * @return
     *     possible object is
     *     {@link BankTransactionCodeStructure4 }
     *
     */
    public BankTransactionCodeStructure4 getBkTxCd() {
        return bkTxCd;
    }

    /**
     * Sets the value of the bkTxCd property.
     *
     * @param value
     *     allowed object is
     *     {@link BankTransactionCodeStructure4 }
     *
     */
    public void setBkTxCd(BankTransactionCodeStructure4 value) {
        this.bkTxCd = value;
    }

    /**
     * Gets the value of the chrgs property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the Jakarta XML Binding object.
     * This is why there is not a {@code set} method for the chrgs property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getChrgs().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link ChargesInformation6 }
     *
     *
     * @return
     *     The value of the chrgs property.
     */
    public List<ChargesInformation6> getChrgs() {
        if (chrgs == null) {
            chrgs = new ArrayList<>();
        }
        return this.chrgs;
    }

    /**
     * Gets the value of the intrst property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the Jakarta XML Binding object.
     * This is why there is not a {@code set} method for the intrst property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getIntrst().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link TransactionInterest2 }
     *
     *
     * @return
     *     The value of the intrst property.
     */
    public List<TransactionInterest2> getIntrst() {
        if (intrst == null) {
            intrst = new ArrayList<>();
        }
        return this.intrst;
    }

    /**
     * Gets the value of the rltdPties property.
     *
     * @return
     *     possible object is
     *     {@link TransactionParty2 }
     *
     */
    public TransactionParty2 getRltdPties() {
        return rltdPties;
    }

    /**
     * Sets the value of the rltdPties property.
     *
     * @param value
     *     allowed object is
     *     {@link TransactionParty2 }
     *
     */
    public void setRltdPties(TransactionParty2 value) {
        this.rltdPties = value;
    }

    /**
     * Gets the value of the rltdAgts property.
     *
     * @return
     *     possible object is
     *     {@link TransactionAgents2 }
     *
     */
    public TransactionAgents2 getRltdAgts() {
        return rltdAgts;
    }

    /**
     * Sets the value of the rltdAgts property.
     *
     * @param value
     *     allowed object is
     *     {@link TransactionAgents2 }
     *
     */
    public void setRltdAgts(TransactionAgents2 value) {
        this.rltdAgts = value;
    }

    /**
     * Gets the value of the purp property.
     *
     * @return
     *     possible object is
     *     {@link Purpose2Choice }
     *
     */
    public Purpose2Choice getPurp() {
        return purp;
    }

    /**
     * Sets the value of the purp property.
     *
     * @param value
     *     allowed object is
     *     {@link Purpose2Choice }
     *
     */
    public void setPurp(Purpose2Choice value) {
        this.purp = value;
    }

    /**
     * Gets the value of the rltdRmtInf property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the Jakarta XML Binding object.
     * This is why there is not a {@code set} method for the rltdRmtInf property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getRltdRmtInf().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link RemittanceLocation2 }
     *
     *
     * @return
     *     The value of the rltdRmtInf property.
     */
    public List<RemittanceLocation2> getRltdRmtInf() {
        if (rltdRmtInf == null) {
            rltdRmtInf = new ArrayList<>();
        }
        return this.rltdRmtInf;
    }

    /**
     * Gets the value of the rmtInf property.
     *
     * @return
     *     possible object is
     *     {@link RemittanceInformation5 }
     *
     */
    public RemittanceInformation5 getRmtInf() {
        return rmtInf;
    }

    /**
     * Sets the value of the rmtInf property.
     *
     * @param value
     *     allowed object is
     *     {@link RemittanceInformation5 }
     *
     */
    public void setRmtInf(RemittanceInformation5 value) {
        this.rmtInf = value;
    }

    /**
     * Gets the value of the rltdDts property.
     *
     * @return
     *     possible object is
     *     {@link TransactionDates2 }
     *
     */
    public TransactionDates2 getRltdDts() {
        return rltdDts;
    }

    /**
     * Sets the value of the rltdDts property.
     *
     * @param value
     *     allowed object is
     *     {@link TransactionDates2 }
     *
     */
    public void setRltdDts(TransactionDates2 value) {
        this.rltdDts = value;
    }

    /**
     * Gets the value of the rltdPric property.
     *
     * @return
     *     possible object is
     *     {@link TransactionPrice2Choice }
     *
     */
    public TransactionPrice2Choice getRltdPric() {
        return rltdPric;
    }

    /**
     * Sets the value of the rltdPric property.
     *
     * @param value
     *     allowed object is
     *     {@link TransactionPrice2Choice }
     *
     */
    public void setRltdPric(TransactionPrice2Choice value) {
        this.rltdPric = value;
    }

    /**
     * Gets the value of the rltdQties property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the Jakarta XML Binding object.
     * This is why there is not a {@code set} method for the rltdQties property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getRltdQties().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link TransactionQuantities1Choice }
     *
     *
     * @return
     *     The value of the rltdQties property.
     */
    public List<TransactionQuantities1Choice> getRltdQties() {
        if (rltdQties == null) {
            rltdQties = new ArrayList<>();
        }
        return this.rltdQties;
    }

    /**
     * Gets the value of the finInstrmId property.
     *
     * @return
     *     possible object is
     *     {@link SecurityIdentification4Choice }
     *
     */
    public SecurityIdentification4Choice getFinInstrmId() {
        return finInstrmId;
    }

    /**
     * Sets the value of the finInstrmId property.
     *
     * @param value
     *     allowed object is
     *     {@link SecurityIdentification4Choice }
     *
     */
    public void setFinInstrmId(SecurityIdentification4Choice value) {
        this.finInstrmId = value;
    }

    /**
     * Gets the value of the tax property.
     *
     * @return
     *     possible object is
     *     {@link TaxInformation3 }
     *
     */
    public TaxInformation3 getTax() {
        return tax;
    }

    /**
     * Sets the value of the tax property.
     *
     * @param value
     *     allowed object is
     *     {@link TaxInformation3 }
     *
     */
    public void setTax(TaxInformation3 value) {
        this.tax = value;
    }

    /**
     * Gets the value of the rtrInf property.
     *
     * @return
     *     possible object is
     *     {@link ReturnReasonInformation10 }
     *
     */
    public ReturnReasonInformation10 getRtrInf() {
        return rtrInf;
    }

    /**
     * Sets the value of the rtrInf property.
     *
     * @param value
     *     allowed object is
     *     {@link ReturnReasonInformation10 }
     *
     */
    public void setRtrInf(ReturnReasonInformation10 value) {
        this.rtrInf = value;
    }

    /**
     * Gets the value of the corpActn property.
     *
     * @return
     *     possible object is
     *     {@link CorporateAction1 }
     *
     */
    public CorporateAction1 getCorpActn() {
        return corpActn;
    }

    /**
     * Sets the value of the corpActn property.
     *
     * @param value
     *     allowed object is
     *     {@link CorporateAction1 }
     *
     */
    public void setCorpActn(CorporateAction1 value) {
        this.corpActn = value;
    }

    /**
     * Gets the value of the sfkpgAcct property.
     *
     * @return
     *     possible object is
     *     {@link CashAccount16 }
     *
     */
    public CashAccount16 getSfkpgAcct() {
        return sfkpgAcct;
    }

    /**
     * Sets the value of the sfkpgAcct property.
     *
     * @param value
     *     allowed object is
     *     {@link CashAccount16 }
     *
     */
    public void setSfkpgAcct(CashAccount16 value) {
        this.sfkpgAcct = value;
    }

    /**
     * Gets the value of the addtlTxInf property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getAddtlTxInf() {
        return addtlTxInf;
    }

    /**
     * Sets the value of the addtlTxInf property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setAddtlTxInf(String value) {
        this.addtlTxInf = value;
    }

}
