
package com.klosesoft.billingsolution.frontend.generated.model.camt53.v02;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for ProprietaryAgent2 complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>{@code
 * <complexType name="ProprietaryAgent2">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="Tp" type="{urn:iso:std:iso:20022:tech:xsd:camt.053.001.02}Max35Text"/>
 *         <element name="Agt" type="{urn:iso:std:iso:20022:tech:xsd:camt.053.001.02}BranchAndFinancialInstitutionIdentification4"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ProprietaryAgent2", propOrder = {
    "tp",
    "agt"
})
public class ProprietaryAgent2 {

    @XmlElement(name = "Tp", required = true)
    protected String tp;
    @XmlElement(name = "Agt", required = true)
    protected BranchAndFinancialInstitutionIdentification4 agt;

    /**
     * Gets the value of the tp property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getTp() {
        return tp;
    }

    /**
     * Sets the value of the tp property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setTp(String value) {
        this.tp = value;
    }

    /**
     * Gets the value of the agt property.
     *
     * @return
     *     possible object is
     *     {@link BranchAndFinancialInstitutionIdentification4 }
     *
     */
    public BranchAndFinancialInstitutionIdentification4 getAgt() {
        return agt;
    }

    /**
     * Sets the value of the agt property.
     *
     * @param value
     *     allowed object is
     *     {@link BranchAndFinancialInstitutionIdentification4 }
     *
     */
    public void setAgt(BranchAndFinancialInstitutionIdentification4 value) {
        this.agt = value;
    }

}
