
package com.klosesoft.billingsolution.frontend.generated.model.camt53.v02;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for CashAccount20 complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>{@code
 * <complexType name="CashAccount20">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="Id" type="{urn:iso:std:iso:20022:tech:xsd:camt.053.001.02}AccountIdentification4Choice"/>
 *         <element name="Tp" type="{urn:iso:std:iso:20022:tech:xsd:camt.053.001.02}CashAccountType2" minOccurs="0"/>
 *         <element name="Ccy" type="{urn:iso:std:iso:20022:tech:xsd:camt.053.001.02}ActiveOrHistoricCurrencyCode" minOccurs="0"/>
 *         <element name="Nm" type="{urn:iso:std:iso:20022:tech:xsd:camt.053.001.02}Max70Text" minOccurs="0"/>
 *         <element name="Ownr" type="{urn:iso:std:iso:20022:tech:xsd:camt.053.001.02}PartyIdentification32" minOccurs="0"/>
 *         <element name="Svcr" type="{urn:iso:std:iso:20022:tech:xsd:camt.053.001.02}BranchAndFinancialInstitutionIdentification4" minOccurs="0"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "CashAccount20", propOrder = {
    "id",
    "tp",
    "ccy",
    "nm",
    "ownr",
    "svcr"
})
public class CashAccount20 {

    @XmlElement(name = "Id", required = true)
    protected AccountIdentification4Choice id;
    @XmlElement(name = "Tp")
    protected CashAccountType2 tp;
    @XmlElement(name = "Ccy")
    protected String ccy;
    @XmlElement(name = "Nm")
    protected String nm;
    @XmlElement(name = "Ownr")
    protected PartyIdentification32 ownr;
    @XmlElement(name = "Svcr")
    protected BranchAndFinancialInstitutionIdentification4 svcr;

    /**
     * Gets the value of the id property.
     *
     * @return
     *     possible object is
     *     {@link AccountIdentification4Choice }
     *
     */
    public AccountIdentification4Choice getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     *
     * @param value
     *     allowed object is
     *     {@link AccountIdentification4Choice }
     *
     */
    public void setId(AccountIdentification4Choice value) {
        this.id = value;
    }

    /**
     * Gets the value of the tp property.
     *
     * @return
     *     possible object is
     *     {@link CashAccountType2 }
     *
     */
    public CashAccountType2 getTp() {
        return tp;
    }

    /**
     * Sets the value of the tp property.
     *
     * @param value
     *     allowed object is
     *     {@link CashAccountType2 }
     *
     */
    public void setTp(CashAccountType2 value) {
        this.tp = value;
    }

    /**
     * Gets the value of the ccy property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getCcy() {
        return ccy;
    }

    /**
     * Sets the value of the ccy property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setCcy(String value) {
        this.ccy = value;
    }

    /**
     * Gets the value of the nm property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getNm() {
        return nm;
    }

    /**
     * Sets the value of the nm property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setNm(String value) {
        this.nm = value;
    }

    /**
     * Gets the value of the ownr property.
     *
     * @return
     *     possible object is
     *     {@link PartyIdentification32 }
     *
     */
    public PartyIdentification32 getOwnr() {
        return ownr;
    }

    /**
     * Sets the value of the ownr property.
     *
     * @param value
     *     allowed object is
     *     {@link PartyIdentification32 }
     *
     */
    public void setOwnr(PartyIdentification32 value) {
        this.ownr = value;
    }

    /**
     * Gets the value of the svcr property.
     *
     * @return
     *     possible object is
     *     {@link BranchAndFinancialInstitutionIdentification4 }
     *
     */
    public BranchAndFinancialInstitutionIdentification4 getSvcr() {
        return svcr;
    }

    /**
     * Sets the value of the svcr property.
     *
     * @param value
     *     allowed object is
     *     {@link BranchAndFinancialInstitutionIdentification4 }
     *
     */
    public void setSvcr(BranchAndFinancialInstitutionIdentification4 value) {
        this.svcr = value;
    }

}
