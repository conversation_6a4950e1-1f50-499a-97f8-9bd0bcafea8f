
package com.klosesoft.billingsolution.frontend.generated.model.camt53.v02;

import jakarta.xml.bind.annotation.*;


/**
 * <p>Java class for RemittanceLocation2 complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>{@code
 * <complexType name="RemittanceLocation2">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="RmtId" type="{urn:iso:std:iso:20022:tech:xsd:camt.053.001.02}Max35Text" minOccurs="0"/>
 *         <element name="RmtLctnMtd" type="{urn:iso:std:iso:20022:tech:xsd:camt.053.001.02}RemittanceLocationMethod2Code" minOccurs="0"/>
 *         <element name="RmtLctnElctrncAdr" type="{urn:iso:std:iso:20022:tech:xsd:camt.053.001.02}Max2048Text" minOccurs="0"/>
 *         <element name="RmtLctnPstlAdr" type="{urn:iso:std:iso:20022:tech:xsd:camt.053.001.02}NameAndAddress10" minOccurs="0"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "RemittanceLocation2", propOrder = {
    "rmtId",
    "rmtLctnMtd",
    "rmtLctnElctrncAdr",
    "rmtLctnPstlAdr"
})
public class RemittanceLocation2 {

    @XmlElement(name = "RmtId")
    protected String rmtId;
    @XmlElement(name = "RmtLctnMtd")
    @XmlSchemaType(name = "string")
    protected RemittanceLocationMethod2Code rmtLctnMtd;
    @XmlElement(name = "RmtLctnElctrncAdr")
    protected String rmtLctnElctrncAdr;
    @XmlElement(name = "RmtLctnPstlAdr")
    protected NameAndAddress10 rmtLctnPstlAdr;

    /**
     * Gets the value of the rmtId property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getRmtId() {
        return rmtId;
    }

    /**
     * Sets the value of the rmtId property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setRmtId(String value) {
        this.rmtId = value;
    }

    /**
     * Gets the value of the rmtLctnMtd property.
     *
     * @return
     *     possible object is
     *     {@link RemittanceLocationMethod2Code }
     *
     */
    public RemittanceLocationMethod2Code getRmtLctnMtd() {
        return rmtLctnMtd;
    }

    /**
     * Sets the value of the rmtLctnMtd property.
     *
     * @param value
     *     allowed object is
     *     {@link RemittanceLocationMethod2Code }
     *
     */
    public void setRmtLctnMtd(RemittanceLocationMethod2Code value) {
        this.rmtLctnMtd = value;
    }

    /**
     * Gets the value of the rmtLctnElctrncAdr property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getRmtLctnElctrncAdr() {
        return rmtLctnElctrncAdr;
    }

    /**
     * Sets the value of the rmtLctnElctrncAdr property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setRmtLctnElctrncAdr(String value) {
        this.rmtLctnElctrncAdr = value;
    }

    /**
     * Gets the value of the rmtLctnPstlAdr property.
     *
     * @return
     *     possible object is
     *     {@link NameAndAddress10 }
     *
     */
    public NameAndAddress10 getRmtLctnPstlAdr() {
        return rmtLctnPstlAdr;
    }

    /**
     * Sets the value of the rmtLctnPstlAdr property.
     *
     * @param value
     *     allowed object is
     *     {@link NameAndAddress10 }
     *
     */
    public void setRmtLctnPstlAdr(NameAndAddress10 value) {
        this.rmtLctnPstlAdr = value;
    }

}
