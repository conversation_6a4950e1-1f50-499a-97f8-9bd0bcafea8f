# Default values for backend.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:
  repository: ************.dkr.ecr.eu-central-1.amazonaws.com/frontend
  pullPolicy: Always
  # Overrides the image tag whose default is the chart appVersion.
  tag: "latest"

imagePullSecrets: []

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Automatically mount a ServiceAccount's API credentials?
  automount: true
  # Annotations to add to the service account
  annotations: { }
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: "backend-service-account"

podAnnotations: { }
podLabels: { }

podSecurityContext:
  fsGroup: 2000

securityContext:
  capabilities:
    drop:
      - ALL
#  readOnlyRootFilesystem: true
  runAsNonRoot: true
  runAsUser: 1000

service:
  type: NodePort
  port: 8080

ingress:
  enabled: true
  className: alb
  annotations:
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/scheme: internal
    alb.ingress.kubernetes.io/target-type: instance
    alb.ingress.kubernetes.io/healthcheck-path: /actuator/health
    alb.ingress.kubernetes.io/load-balancer-attributes: deletion_protection.enabled=true
  hosts:
    - host: backend-internal.local
      paths:
        - path: /*
          pathType: ImplementationSpecific
  tls: [ ]

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
#  limits:
#  #   cpu: 100m
#  memory: 512Mi
#  requests:
#    cpu: 100m
#    memory: 512Mi

livenessProbe:
  httpGet:
    path: /actuator/health/liveness
    port: http
readinessProbe:
  httpGet:
    path: /actuator/health/readiness
    port: http

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 1
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

# Additional volumes on the output Deployment definition.
volumes: [ ]
# - name: foo
#   secret:
#     secretName: mysecret
#     optional: false

# Additional volumeMounts on the output Deployment definition.
volumeMounts: [ ]
# - name: foo
#   mountPath: "/etc/foo"
#   readOnly: true

nodeSelector: { }

tolerations: [ ]

affinity: { }
